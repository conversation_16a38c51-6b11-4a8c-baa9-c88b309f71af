import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { developmentFormat, productionFormat } from '../format';
import { createLogger } from 'winston';
import os from 'os';
import Transport from 'winston-transport';

// Define interface for log output
interface LogOutput {
  timestamp: string;
  level: string;
  message: string;
  environment?: string;
  service?: string;
  hostname?: string;
  pid?: number;
  error?: {
    message: string;
    name: string;
  };
  stack?: string;
  [key: string]: unknown;
}

// Custom transport to capture log output
class MemoryTransport extends Transport {
  public logs: string[] = [];

  constructor(opts?: Transport.TransportStreamOptions) {
    super(opts);
  }

  log(info: Record<string | symbol, unknown>, callback: () => void) {
    const message = info[Symbol.for('message')];
    if (typeof message === 'string') {
      this.logs.push(message);
    }
    callback();
  }

  clearLogs() {
    this.logs = [];
  }
}

describe('Logging Format', () => {
  let originalNodeEnv: string | undefined;
  let originalServiceName: string | undefined;
  let memoryTransport: MemoryTransport;

  beforeEach(() => {
    // Save original environment variables
    originalNodeEnv = process.env.NODE_ENV;
    originalServiceName = process.env.SERVICE_NAME;

    // Create memory transport for capturing logs
    memoryTransport = new MemoryTransport();
  });

  afterEach(() => {
    // Restore environment variables
    process.env.NODE_ENV = originalNodeEnv;
    process.env.SERVICE_NAME = originalServiceName;

    // Clear logs
    memoryTransport.clearLogs();
  });

  describe('Development Format', () => {
    it('should format logs with timestamp, level, and message', () => {
      // Create a logger with the development format
      const logger = createLogger({
        format: developmentFormat,
        transports: [memoryTransport],
      });

      // Log a message
      logger.info('Test message');

      // Check the output format
      expect(memoryTransport.logs.length).toBe(1);
      const logOutput = memoryTransport.logs[0];

      // Should contain timestamp in YYYY-MM-DD HH:mm:ss format
      expect(logOutput).toMatch(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/);

      // Should contain the log level
      expect(logOutput).toContain('info');

      // Should contain the message
      expect(logOutput).toContain('Test message');
    });

    it('should include error stack traces', () => {
      const logger = createLogger({
        format: developmentFormat,
        transports: [memoryTransport],
      });

      const testError = new Error('Test error');
      logger.error('Error occurred', { error: testError });

      expect(memoryTransport.logs.length).toBe(1);
      const logOutput = memoryTransport.logs[0];

      // Should contain the error message
      expect(logOutput).toContain('Test error');

      // Should contain stack trace information
      expect(logOutput).toContain('Error');
    });

    it('should include additional metadata', () => {
      const logger = createLogger({
        format: developmentFormat,
        transports: [memoryTransport],
      });

      logger.info('Test with metadata', { user: 'testuser', action: 'login' });

      expect(memoryTransport.logs.length).toBe(1);
      const logOutput = memoryTransport.logs[0];

      // Should contain the metadata
      expect(logOutput).toContain('testuser');
      expect(logOutput).toContain('login');
    });
  });

  describe('Production Format', () => {
    it('should output JSON format with required fields', () => {
      process.env.NODE_ENV = 'production';
      process.env.SERVICE_NAME = 'test-service';

      const logger = createLogger({
        format: productionFormat,
        transports: [memoryTransport],
      });

      logger.info('Production test message');

      expect(memoryTransport.logs.length).toBe(1);
      const logOutput = JSON.parse(memoryTransport.logs[0]) as LogOutput;

      // Check required fields
      expect(logOutput).toHaveProperty('timestamp');
      expect(logOutput).toHaveProperty('level', 'info');
      expect(logOutput).toHaveProperty('message', 'Production test message');
      expect(logOutput).toHaveProperty('environment', 'production');
      expect(logOutput).toHaveProperty('service', 'test-service');
      expect(logOutput).toHaveProperty('hostname', os.hostname());
      expect(logOutput).toHaveProperty('pid', process.pid);
    });

    it('should mask sensitive data like email addresses', () => {
      const logger = createLogger({
        format: productionFormat,
        transports: [memoryTransport],
      });

      logger.info('User <NAME_EMAIL>');

      expect(memoryTransport.logs.length).toBe(1);
      const logOutput = JSON.parse(memoryTransport.logs[0]) as LogOutput;

      // Email should be masked
      expect(logOutput.message).not.toContain('<EMAIL>');
      expect(logOutput.message).toContain('[EMAIL REDACTED]');
    });

    it('should handle error objects correctly', () => {
      const logger = createLogger({
        format: productionFormat,
        transports: [memoryTransport],
      });

      const testError = new Error('Production error');
      logger.error('Error in production', { error: testError });

      expect(memoryTransport.logs.length).toBe(1);
      const logOutput = JSON.parse(memoryTransport.logs[0]) as LogOutput;

      // Should have error information
      expect(logOutput).toHaveProperty('error');
      if (logOutput.error) {
        expect(logOutput.error).toHaveProperty('message', 'Production error');
        expect(logOutput.error).toHaveProperty('name', 'Error');
      }
      expect(logOutput).toHaveProperty('stack');
      if (logOutput.stack) {
        expect(logOutput.stack).toContain('Error: Production error');
      }
    });

    it('should use default service name if not provided', () => {
      process.env.NODE_ENV = 'production';
      // Properly delete the environment variable
      delete process.env.SERVICE_NAME;

      const logger = createLogger({
        format: productionFormat,
        transports: [memoryTransport],
      });

      logger.info('Test default service name');

      expect(memoryTransport.logs.length).toBe(1);
      const logOutput = JSON.parse(memoryTransport.logs[0]) as LogOutput;

      // Should use default service name
      expect(logOutput).toHaveProperty('service', 'einstein-backend');
    });
  });
});
