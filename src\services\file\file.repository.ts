import type { File } from "./file.model";

export interface FileRepository {
	create(
		file: Omit<File, "id" | "createdAt" | "updatedAt" | "deletedAt">,
	): Promise<File>;
	findAll(params?: {
		userId?: string;
		limit?: number;
		offset?: number;
	}): Promise<File[]>;
	findById(id: string): Promise<File | null>;
	findByUserId(userId: string): Promise<File[] | null>;
	findByStorageName(storageName: string): Promise<File | null>;
	deleteById(id: string): Promise<File | null>;
	updateByUserId(userId: string, file: Partial<File>): Promise<File | null>;
}
