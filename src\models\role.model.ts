/**
 * This module defines the Role model for managing role-based access control (RBAC) in the system.
 * Roles are collections of permissions that can be assigned to users, providing a way to
 * manage access control at a group level rather than individual user level.
 *
 * The Role model supports a many-to-many relationship with permissions, allowing each role
 * to have multiple permissions, and each permission to be assigned to multiple roles.
 */

/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */

// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {
	type Ref,
	getModelForClass,
	modelOptions,
	prop,
} from "@typegoose/typegoose";
import { Permission } from "./permission.model";

/**
 * Configuration for the Role model
 * - Collection name: roles
 * - Includes timestamps for document creation and updates
 * - Supports automatic population of permission references
 */
@modelOptions({
	schemaOptions: {
		collection: "roles",
		timestamps: true,
	},
})
/**
 * Class representing a role in the system's RBAC (Role-Based Access Control).
 * Each role is a collection of permissions that can be assigned to users.
 *
 * @example
 * ```typescript
 * // Example role
 * {
 *   name: "project_manager",
 *   description: "Can manage all aspects of projects",
 *   permissions: ["project.read", "project.write", "methodology.read"]
 * }
 * ```
 */
export class Role {
	/**
	 * Unique identifier name for the role
	 * @prop required - Role must have a name
	 * @prop unique - No two roles can have the same name
	 * @prop trim - Whitespace will be trimmed from the name
	 * @example "project_manager", "methodology_editor", "admin"
	 */
	@prop({ required: true, unique: true, trim: true, type: () => String })
	public name!: string;

	/**
	 * Human-readable description of the role's purpose and responsibilities
	 * @prop required - Role must have a description
	 * @prop trim - Whitespace will be trimmed from the description
	 * @example "Can manage all aspects of projects and methodologies"
	 */
	@prop({ required: true, trim: true, type: () => String })
	public description!: string;

	/**
	 * Array of references to Permission documents
	 * Defines what actions users with this role can perform
	 * @prop required - false (a role can exist without permissions)
	 * @prop trim - Whitespace will be trimmed
	 * @prop ref - References the Permission model
	 * @example ["project.read", "methodology.write", "user.manage"]
	 */
	@prop({ required: false, trim: true, ref: Permission })
	public permissions!: Ref<Permission>[];
}

// Create and export the Mongoose model for Role
const RoleModel = getModelForClass(Role);
export default RoleModel;
