import logger from "@/utils/logging";
import type { Request, Response } from "express";
import type { FileService } from "./file.service";

// Define types for request body
interface FileUploadRequestBody {
	isPrivate?: string | boolean;
	sharedWith?: string | string[];
	[key: string]: unknown;
}

export class FileController {
	constructor(private fileService: FileService) {}

	async uploadFiles(req: Request, res: Response) {
		const files = req.files as Express.Multer.File[] | undefined;
		const body = req.body as FileUploadRequestBody;
		const isPrivate = body.isPrivate;
		// const sharedWith = body.sharedWith;
		let sharedWith: string[] = [];

		if (!files || files.length === 0) {
			return res.status(400).json({ error: "No file uploaded" });
		}

		if (isPrivate && body.sharedWith) {
			logger.debug(`Shared with: ${body.sharedWith}`);
			if (typeof body.sharedWith === "string") {
				try {
					sharedWith = JSON.parse(body.sharedWith) as string[];
				} catch (error) {
					logger.error(`Error parsing sharedWith: ${error}`);
					sharedWith = body.sharedWith
						.split(",")
						.map((email: string) => email.trim());
				}
			} else if (Array.isArray(body.sharedWith)) {
				sharedWith = body.sharedWith;
			} else {
				return res.status(400).json({ error: "Invalid sharedWith" });
			}
		}
		const metadata = Object.keys(body).reduce<Record<string, unknown>>(
			(acc, key) => {
				if (key !== "isPrivate" && key !== "sharedWith") {
					acc[key] = body[key];
				}
				return acc;
			},
			{},
		);
		const userId = req.headers["x-azure-ad-object-id"] as string;
		const createdFiles = await this.fileService.uploadFile({
			files: files,
			userId,
			metadata,
			isPrivate:
				typeof isPrivate === "string"
					? isPrivate === "true"
					: Boolean(isPrivate),
			sharedWith,
		});
		const fileNames = createdFiles.map((file) => file.originalName);
		logger.debug(`User ${userId} uploaded files: ${fileNames}`);
		res.status(201).json(createdFiles);
	}

	async getUserFiles(req: Request, res: Response) {
		const userId = req.headers["x-azure-ad-object-id"] as string;
		const files = await this.fileService.getUserFiles(userId);
		res.status(200).json(files);
	}

	async getFileByUserId(req: Request, res: Response) {
		const userId = req.params.userId;
		const file = await this.fileService.getFilesByUserId(userId);
		res.status(200).json(file);
	}

	async viewFile(req: Request, res: Response) {
		try {
			const { containerName, blob } = req.params;

			if (!containerName || !blob) {
				return res.status(400).json({
					error: "Container name and blob name are required",
				});
			}

			logger.debug(`Streaming file: ${blob} from container: ${containerName}`);

			// Get stream and metadata from the service
			const { stream, contentType, contentLength, lastModified } =
				await this.fileService.streamBlob(containerName, blob);

			// Set response headers
			if (contentType) {
				res.setHeader("Content-Type", contentType);
			}

			if (contentLength) {
				res.setHeader("Content-Length", contentLength);
			}

			if (lastModified) {
				res.setHeader("Last-Modified", lastModified.toUTCString());
			}

			// Set cache headers for better performance
			res.setHeader("Cache-Control", "public, max-age=3600"); // Cache for 1 hour
			res.setHeader("ETag", `"${containerName}-${blob}"`);

			// Handle range requests for video/large files
			const range = req.headers.range;
			if (range && contentLength) {
				// Parse range header
				const parts = range.replace(/bytes=/, "").split("-");
				const start = Number.parseInt(parts[0], 10);
				const end = parts[1]
					? Number.parseInt(parts[1], 10)
					: contentLength - 1;
				const chunksize = end - start + 1;

				res.status(206); // Partial Content
				res.setHeader(
					"Content-Range",
					`bytes ${start}-${end}/${contentLength}`,
				);
				res.setHeader("Accept-Ranges", "bytes");
				res.setHeader("Content-Length", chunksize);
			} else {
				res.status(200);
			}

			// Pipe the stream to response
			stream.pipe(res);

			// Handle stream errors
			stream.on("error", (error) => {
				logger.error(`Stream error: ${error}`);
				if (!res.headersSent) {
					res.status(500).json({ error: "Error streaming file" });
				}
			});

			// Handle stream end
			stream.on("end", () => {
				logger.debug(
					`Successfully streamed file: ${blob} from container: ${containerName}`,
				);
			});
		} catch (error) {
			logger.error(`Error in viewFile: ${error}`);

			if (!res.headersSent) {
				if (error instanceof Error && error.message.includes("not found")) {
					return res.status(404).json({ error: "File not found" });
				}
				return res.status(500).json({ error: "Internal server error" });
			}
		}
	}

	async grantAccessToFile(req: Request, res: Response) {
		interface GrantAccessRequestBody {
			fileName: string;
			userId: string;
		}

		const { fileName, userId } = req.body as GrantAccessRequestBody;
		const ownerId = req.headers["x-azure-ad-object-id"] as string;
		logger.debug(
			`Granting access to file: ${fileName} for user: ${userId} by owner: ${ownerId}`,
		);
		const sasToken = await this.fileService.grantAccessToFile({
			fileName,
			userId,
			ownerId,
		});
		res.status(200).json(sasToken);
	}
}
