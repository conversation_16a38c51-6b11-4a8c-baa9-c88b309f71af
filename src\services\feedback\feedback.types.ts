import { Feedback } from './feedback.model';
import { z } from 'zod';

/**
 * Input type for creating a new feedback entry
 */
export interface FeedbackInput {
  message: string;
  azureAdObjectId: string;
  page_url?: string;
  page_title?: string;
}

/**
 * Response type returned from feedback operations
 */
export interface FeedbackResponse {
  id: string;
  message: string;
  azureAdObjectId: string;
  createdAt: Date;
  page_url?: string;
  page_title?: string;
}

/**
 * Type for feedback creation without auto-generated fields
 */
export type FeedbackCreateInput = Omit<Feedback, 'id' | 'createdAt' | 'updatedAt'>;

/**
 * Parameters for querying feedback
 */
export interface FeedbackQueryParams {
  limit?: number;
  offset?: number;
  azureAdObjectId?: string;
}

/**
 * Validation schema for feedback submission
 */
export const createFeedbackSchema = z.object({
  message: z.string().min(1, 'Feedback message is required'),
  page_url: z.string().optional(),
  page_title: z.string().optional(),
});

/**
 * Type for feedback request body derived from the validation schema
 */
export type CreateFeedbackRequest = z.infer<typeof createFeedbackSchema>;

/**
 * Parameter type for feedback ID routes
 */
export interface FeedbackIdParams {
  id: string;
}
