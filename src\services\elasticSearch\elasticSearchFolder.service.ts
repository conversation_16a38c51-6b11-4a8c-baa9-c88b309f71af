import { ElasticSearchFolderModel } from "./elasticSearchFolder.model";

export interface ElasticSearchFolderQuery {
	es_index?: string;
	file_path_search?: string;
	limit?: number;
	offset?: number;
}

export interface ElasticSearchFolderResponse {
	id: string;
	es_index: string;
	file_path: string;
	path_depth: number;
	imported_at: Date;
}

export class ElasticSearchFolderService {
	/**
	 * Get documents by es_index
	 */
	public async getByEsIndex(
		es_index: string,
		limit = 50,
		offset = 0,
	): Promise<ElasticSearchFolderResponse[]> {
		const documents = await ElasticSearchFolderModel.find({ es_index })
			.limit(limit)
			.skip(offset)
			.sort({ imported_at: -1 })
			.lean();

		return documents.map((doc) => ({
			id: doc._id.toString(),
			es_index: doc.es_index,
			file_path: doc.file_path,
			path_depth: doc.path_depth,
			imported_at: doc.imported_at,
		}));
	}

	/**
	 * Search documents by sub-text in file_path
	 */
	public async searchByFilePath(
		searchText: string,
		limit = 50,
		offset = 0,
	): Promise<ElasticSearchFolderResponse[]> {
		const documents = await ElasticSearchFolderModel.find({
			file_path: { $regex: searchText, $options: "i" },
		})
			.limit(limit)
			.skip(offset)
			.sort({ imported_at: -1 })
			.lean();

		return documents.map((doc) => ({
			id: doc._id.toString(),
			es_index: doc.es_index,
			file_path: doc.file_path,
			path_depth: doc.path_depth,
			imported_at: doc.imported_at,
		}));
	}

	/**
	 * Search documents by es_indexes and sub-text in file_path
	 */
	public async searchByEsIndexAndFilePath(
		es_indexes: string[],
		searchText: string,
		limit = 50,
		offset = 0,
	): Promise<ElasticSearchFolderResponse[]> {
		const documents = await ElasticSearchFolderModel.find({
			es_index: { $in: es_indexes },
			file_path: { $regex: searchText, $options: "i" },
		})
			.limit(limit)
			.skip(offset)
			.sort({ imported_at: -1 })
			.lean();

		return documents.map((doc) => ({
			id: doc._id.toString(),
			es_index: doc.es_index,
			file_path: doc.file_path,
			path_depth: doc.path_depth,
			imported_at: doc.imported_at,
		}));
	}

	/**
	 * Get all documents with optional filtering
	 */
	public async getAll(
		query: ElasticSearchFolderQuery = {},
	): Promise<ElasticSearchFolderResponse[]> {
		const { es_index, file_path_search, limit = 50, offset = 0 } = query;

		const filter: any = {};

		if (es_index) {
			filter.es_index = es_index;
		}

		if (file_path_search) {
			filter.file_path = { $regex: file_path_search, $options: "i" };
		}

		const documents = await ElasticSearchFolderModel.find(filter)
			.limit(limit)
			.skip(offset)
			.sort({ imported_at: -1 })
			.lean();

		return documents.map((doc) => ({
			id: doc._id.toString(),
			es_index: doc.es_index,
			file_path: doc.file_path,
			path_depth: doc.path_depth,
			imported_at: doc.imported_at,
		}));
	}

	/**
	 * Get document by ID
	 */
	public async getById(
		id: string,
	): Promise<ElasticSearchFolderResponse | null> {
		const document = await ElasticSearchFolderModel.findById(id).lean();

		if (!document) {
			return null;
		}

		return {
			id: document._id.toString(),
			es_index: document.es_index,
			file_path: document.file_path,
			path_depth: document.path_depth,
			imported_at: document.imported_at,
		};
	}

	/**
	 * Get count of documents matching criteria
	 */
	public async getCount(query: ElasticSearchFolderQuery = {}): Promise<number> {
		const { es_index, file_path_search } = query;

		const filter: any = {};

		if (es_index) {
			filter.es_index = es_index;
		}

		if (file_path_search) {
			filter.file_path = { $regex: file_path_search, $options: "i" };
		}

		return await ElasticSearchFolderModel.countDocuments(filter);
	}
}
