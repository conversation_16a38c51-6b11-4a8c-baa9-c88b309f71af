---
description: 
globs: 
alwaysApply: true
---
# Project Architecture Guidelines

This project follows Domain-Driven Design (DDD) principles with clean architecture patterns. The codebase is organized to maximize maintainability, testability, and separation of concerns.

## Directory Structure

- `src/services/` - Contains domain-specific services organized by bounded context
- `src/models/` - Domain models and schemas
- `src/controllers/` - API controllers (when not co-located with services)
- `src/middlewares/` - Express middleware
- `src/config/` - Application configuration
- `src/utils/` - Shared utility functions
- `src/routes/` - API route definitions
- `src/repositories/` - Data access layer implementations
- `src/infrastructure/` - Infrastructure concerns (databases, external services)

## Domain Service Structure

Each domain service follows a consistent structure:

```
services/
└── domain-name/
    ├── domain.service.ts      - Business logic implementation
    ├── domain.controller.ts   - HTTP request handling
    ├── domain.routes.ts       - Route definitions
    ├── domain.repository.ts   - Repository interface
    ├── domain.model.ts        - Domain model definition
    ├── mongoDomain.repository.ts - MongoDB implementation
    └── otherImplementation.service.ts - Additional implementations
```

## Code Organization Principles

1. **Dependency Injection**: Services receive their dependencies via constructor injection
2. **Repository Pattern**: Data access is abstracted through repositories
3. **Interface Segregation**: Interfaces define contracts between layers
4. **Single Responsibility**: Each file has a well-defined, single responsibility
5. **Domain Encapsulation**: Domain logic is encapsulated within service boundaries

