# Server Configuration
PORT=4000
NODE_ENV=development

# Database Configuration - PostgreSQL
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=dev_database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/dev_database

# Database Connection Pool Settings
DB_POOL_MAX=20
DB_POOL_IDLE_TIMEOUT=30000

# SSL Configuration (for production)
DB_SSL=false
DB_SSL_CA=
DB_SSL_CERT=
DB_SSL_KEY=

# MongoDB Configuration
MONGO_URI=mongodb://localhost:27017/einstein

# Elasticsearch Configuration
ELASTIC_NODE=https://localhost:9200
ELASTIC_USERNAME=elastic
ELASTIC_PASSWORD=
ELASTIC_INDEX=sharepoint_crawl
ELASTIC_REJECT_UNAUTHORIZED=false

# Azure Storage Configuration
AZURE_STORAGE_CONNECTION_STRING=
AZURE_STORAGE_CONTAINER_NAME=methodology-files

# Logging Configuration
LOG_PATH=./logs/development.log

# Authentication Configuration
AUTH_ENABLED=true

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100

# API Rate Limiting
API_RATE_LIMIT_WINDOW_MS=60000
API_RATE_LIMIT_MAX=100

# Auth Rate Limiting
AUTH_RATE_LIMIT_WINDOW_MS=900000
AUTH_RATE_LIMIT_MAX=100

# Upload Rate Limiting
UPLOAD_RATE_LIMIT_WINDOW_MS=3600000
UPLOAD_RATE_LIMIT_MAX=10

# Search Rate Limiting
SEARCH_RATE_LIMIT_WINDOW_MS=60000
SEARCH_RATE_LIMIT_MAX=80
