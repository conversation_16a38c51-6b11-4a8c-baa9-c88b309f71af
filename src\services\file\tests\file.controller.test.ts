import request from "supertest";
import app from "../../../infrastructure/app";
import { FileService } from "../file.service";
import { vi } from "vitest";

describe("FileController", () => {
	const testUserId = "<EMAIL>";

	// Mock data for consistent testing
	const mockFileData = {
		url: "https://test.com/test.txt",
		storageName: "mock-storage-name-123",
		userId: testUserId,
		originalName: "test.txt",
		mimeType: "text/plain",
		size: 100,
		isPrivate: false,
		sharedWith: [],
		metadata: {},
	};

	const mockSasToken = "mock-sas-token-for-testing";

	// Setup all mocks before each test
	beforeEach(() => {
		// Mock all FileService methods
		vi.spyOn(FileService.prototype, "uploadFile").mockResolvedValue([
			mockFileData,
		]);
		vi.spyOn(FileService.prototype, "getUserFiles").mockResolvedValue([
			mockFileData,
		]);
		vi.spyOn(FileService.prototype, "getFilesByUserId").mockResolvedValue([
			mockFileData,
		]);
		vi.spyOn(FileService.prototype, "accessFile").mockResolvedValue(
			mockSasToken,
		);
		vi.spyOn(FileService.prototype, "grantAccessToFile").mockResolvedValue(
			mockSasToken,
		);
	});

	afterEach(() => {
		vi.resetAllMocks();
	});

	describe("POST /api/einstein/files/upload", () => {
		it("should upload files and return 201", async () => {
			const sharedWith = ["<EMAIL>", "<EMAIL>"];
			const res = await request(app)
				.post("/api/einstein/files/upload")
				.set("x-azure-ad-object-id", testUserId)
				.attach("files", Buffer.from("dummy file content"), "test.txt")
				.field("isPrivate", false)
				.field("sharedWith", sharedWith);

			expect(res.status).toBe(201);
			expect(Array.isArray(res.body)).toBe(true);

			// Type assertion for response body
			const responseBody = res.body as Array<{ originalName: string }>;
			expect(responseBody[0]).toHaveProperty("originalName", "test.txt");

			console.log(res.body);

			// Verify service method was called with correct parameters
			expect(FileService.prototype.uploadFile).toHaveBeenCalledWith({
				files: expect.any(Array) as unknown as Express.Multer.File[],
				userId: testUserId,
				metadata: expect.any(Object) as unknown as Record<string, unknown>,
				isPrivate: false,
				sharedWith: expect.any(Array) as unknown as string[],
			});
		});

		it("should return 400 if no file uploaded", async () => {
			const res = await request(app)
				.post("/api/einstein/files/upload")
				.set("x-azure-ad-object-id", testUserId);

			expect(res.status).toBe(400);
			expect(res.body).toHaveProperty("error", "No file uploaded");
			expect(FileService.prototype.uploadFile).not.toHaveBeenCalled();
		});

		it("should handle the sharedWith field as an array when provided as JSON string", async () => {
			const res = await request(app)
				.post("/api/einstein/files/upload")
				.set("x-azure-ad-object-id", testUserId)
				.attach("files", Buffer.from("dummy file content"), "test.txt")
				.field("isPrivate", "true")
				.field("sharedWith", JSON.stringify(["<EMAIL>"]));

			expect(res.status).toBe(201);
			expect(FileService.prototype.uploadFile).toHaveBeenCalledWith(
				expect.objectContaining({
					sharedWith: ["<EMAIL>"],
				}),
			);
		});
	});

	describe("GET /api/einstein/files/user", () => {
		it("should return user files", async () => {
			const res = await request(app)
				.get("/api/einstein/files/user")
				.set("x-azure-ad-object-id", testUserId);

			expect(res.status).toBe(200);
			expect(Array.isArray(res.body)).toBe(true);
			expect(FileService.prototype.getUserFiles).toHaveBeenCalledWith(
				testUserId,
			);
		});
	});

	describe("GET /api/einstein/files/user/:userId", () => {
		it("should return files for a given userId", async () => {
			const targetUserId = "<EMAIL>";
			const res = await request(app)
				.get(`/api/einstein/files/user/${targetUserId}`)
				.set("x-azure-ad-object-id", testUserId);

			expect(res.status).toBe(200);
			expect(Array.isArray(res.body)).toBe(true);
			expect(FileService.prototype.getFilesByUserId).toHaveBeenCalledWith(
				targetUserId,
			);
		});
	});

	describe("GET /api/einstein/files/:fileName", () => {
		it("should return a SAS token for viewing a file", async () => {
			const fileName = "mock-storage-name-123";

			const res = await request(app)
				.get(`/api/einstein/files/${fileName}`)
				.set("x-azure-ad-object-id", testUserId);

			console.log(res.body);

			expect(res.status).toBe(200);
			expect(res.body).toBe(mockSasToken);
			expect(FileService.prototype.accessFile).toHaveBeenCalledWith(
				fileName,
				testUserId,
			);
		});
	});

	describe("POST /api/einstein/files/grant-access", () => {
		it("should grant access to a file", async () => {
			const fileName = "mock-storage-name-123";
			const targetUserId = "<EMAIL>";

			const res = await request(app)
				.post("/api/einstein/files/grant-access")
				.set("x-azure-ad-object-id", testUserId)
				.send({ fileName, userId: targetUserId });

			console.log(res.body);
			expect(res.status).toBe(200);
			expect(res.body).toBe(mockSasToken);
			expect(FileService.prototype.grantAccessToFile).toHaveBeenCalledWith({
				fileName,
				userId: targetUserId,
				ownerId: testUserId,
			});
		});
	});
});
