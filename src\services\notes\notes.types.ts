import { z } from "zod";

export const getNotesQuerySchema = z.object({
	tradeId: z.string().optional(),
	tradeInfoId: z.string().optional(),
	activeOnly: z.string().optional().transform(val => val === 'true'),
	visibleOnly: z.string().optional().transform(val => val === 'true'),
});

export const getNoteByIdParamsSchema = z.object({
	id: z.string(),
});

export const bulkUpdateNotesSchema = z.object({
	updates: z.array(
		z.object({
			id: z.string().min(1, "ID is required"),
			updates: z.record(z.any()).refine(
				(obj) => Object.keys(obj).length > 0,
				"Updates object cannot be empty"
			)
		})
	).min(1, "At least one update is required")
});

export type GetNotesQuery = z.infer<typeof getNotesQuerySchema>;
export type GetNoteByIdParams = z.infer<typeof getNoteByIdParamsSchema>;
export type BulkUpdateNotesBody = z.infer<typeof bulkUpdateNotesSchema>;