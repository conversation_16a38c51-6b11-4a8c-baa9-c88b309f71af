import { Router } from "express";
import { MethodologyController } from "@/controllers/methodology/methodology.controller";
import { MethodologyService } from "@/services/methodology.service";
import multer from "multer";

const router: Router = Router();

// Configure multer with limits and file filter if needed
const upload = multer({
	storage: multer.memoryStorage(),
	limits: {
		fileSize: 50 * 1024 * 1024, // 10MB limit
		files: 10, // Maximum 10 files
	},
});

const methodologyService = new MethodologyService();
const methodologyController = new MethodologyController(methodologyService);

// Node routes
router.post("/nodes", methodologyController.createNode);
router.patch("/nodes/:nodeId", methodologyController.updateNode);
router.delete("/nodes/:nodeId", methodologyController.deleteNode);
router.delete("/file", methodologyController.deleteFile);
router.delete(
	"/:nodeId/files/:fileId",
	methodologyController.deleteFileByParams,
);

// File upload routes
router.post(
	"/:methodologyId/files",
	upload.single("file"),
	methodologyController.uploadMethodologyFile,
);
// router.post('/upload', upload.single('file'), methodologyController.uploadFile);
router.post(
	"/bulk-upload",
	upload.array("file"),
	methodologyController.bulkUploadFile,
);

// Get Methodologies
router.get("/", methodologyController.getMethodologies);
router.post("/", methodologyController.createMethodology);
router.put("/:id", methodologyController.updateMethodology);

// Add new route for fetching all nodes
router.get("/all", methodologyController.getAllMethodologies);

export default router;
