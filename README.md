# Einstein Backend

A comprehensive backend service for the Einstein application, built with Node.js, TypeScript, Express, and PostgreSQL. This service provides APIs for project management, methodology handling, search functionality, and more.

## 📋 Table of Contents

- [Features](#features)
- [Project Structure](#project-structure)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Environment Configuration](#environment-configuration)
- [Database Setup](#database-setup)
- [Running the Application](#running-the-application)
- [API Documentation](#api-documentation)
- [Testing](#testing)
- [Docker Support](#docker-support)
- [Contributing](#contributing)
- [License](#license)

## 🚀 Features

- **RESTful API** with Express.js and TypeScript
- **Authentication & Authorization** with Azure AD integration
- **Database Integration** with PostgreSQL (Prisma ORM) and MongoDB
- **Search Functionality** with Elasticsearch
- **File Management** with Azure Blob Storage
- **Rate Limiting** and security middlewares
- **Comprehensive Logging** with <PERSON>
- **API Documentation** with Swagger/OpenAPI
- **Testing Suite** with Vitest
- **Docker Support** for containerized deployment
- **Code Quality** with Biome (linting and formatting)

## 📁 Project Structure

```
Einstein-be/
├── src/
│   ├── config/                 # Configuration files
│   │   ├── config.ts          # Main configuration
│   │   └── swagger.ts         # Swagger/OpenAPI setup
│   ├── controllers/           # Route controllers
│   │   ├── abacus/           # Abacus-related controllers
│   │   ├── howTo/            # How-to guides controllers
│   │   ├── methodology/      # Methodology controllers
│   │   ├── projects/         # Project management controllers
│   │   ├── search/           # Search functionality controllers
│   │   └── users/            # User management controllers
│   ├── infrastructure/        # Infrastructure layer
│   │   ├── database/         # Database configurations
│   │   ├── app.ts            # Express app setup
│   │   └── server.ts         # Server initialization
│   ├── middlewares/          # Custom middleware
│   │   ├── errorHandling.ts  # Error handling middleware
│   │   ├── rateLimiting.ts   # Rate limiting middleware
│   │   ├── requestLogging.ts # Request logging middleware
│   │   └── validateToken.ts  # Authentication middleware
│   ├── models/               # Data models
│   ├── repositories/         # Data access layer
│   ├── routes/               # Route definitions
│   │   ├── abacus/          # Abacus routes
│   │   ├── howTo/           # How-to routes
│   │   ├── methodology/     # Methodology routes
│   │   ├── project/         # Project routes
│   │   ├── search/          # Search routes
│   │   ├── users/           # User routes
│   │   └── index.ts         # Main router configuration
│   ├── services/             # Business logic layer
│   │   ├── abacus/          # Abacus services
│   │   ├── duration/        # Duration calculation services
│   │   ├── elasticSearch/   # Elasticsearch services
│   │   ├── feedback/        # Feedback services
│   │   ├── file/            # File management services
│   │   ├── howTo/           # How-to services
│   │   ├── maps/            # Map services
│   │   └── repositories/    # Repository services
│   ├── shared/               # Shared utilities and types
│   ├── utils/                # Utility functions
│   │   ├── exceptions/      # Custom exception classes
│   │   ├── logging/         # Logging utilities
│   │   ├── colors.ts        # Color utilities
│   │   └── csvHelper.ts     # CSV processing utilities
│   └── index.ts              # Application entry point
├── prisma/
│   └── schema.prisma         # Database schema
├── test/                     # Test files
├── logs/                     # Application logs
├── dist/                     # Compiled JavaScript output
├── .env.example              # Environment variables template
├── .env.development          # Development environment variables
├── .env.test                 # Test environment variables
├── package.json              # Dependencies and scripts
├── tsconfig.json             # TypeScript configuration
├── tsconfig.build.json       # Build-specific TypeScript config
├── biome.json                # Biome configuration
├── vitest.config.mts         # Vitest testing configuration
├── Dockerfile.dev            # Docker configuration for development
└── README.md                 # This file
```

## 🛠 Prerequisites

Before running this application, make sure you have the following installed:

- **Node.js** (version 18.x or higher)
- **Yarn** (package manager)
- **PostgreSQL** (version 12.x or higher)
- **MongoDB** (version 4.x or higher)
- **Elasticsearch** (version 8.x or higher)
- **Docker** (optional, for containerized deployment)

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Einstein-be
   ```

2. **Install dependencies**
   ```bash
   yarn install
   ```

3. **Generate Prisma client**
   ```bash
   yarn prisma generate
   ```

## ⚙️ Environment Configuration

1. **Copy the environment template**
   ```bash
   cp .env.example .env.development
   ```

2. **Configure environment variables**
   
   Edit `.env.development` with your specific configuration:

   ```env
   # Server Configuration
   PORT=4000
   NODE_ENV=development
   
   # Database Configuration - PostgreSQL
   POSTGRES_HOST=localhost
   POSTGRES_PORT=5432
   POSTGRES_USER=postgres
   POSTGRES_PASSWORD=your_password
   POSTGRES_DB=einstein_db
   DATABASE_URL=postgresql://postgres:your_password@localhost:5432/einstein_db
   
   # MongoDB Configuration
   MONGO_URI=mongodb://localhost:27017/einstein
   
   # Elasticsearch Configuration
   ELASTIC_NODE=https://localhost:9200
   ELASTIC_USERNAME=elastic
   ELASTIC_PASSWORD=your_elastic_password
   ELASTIC_INDEX=sharepoint_crawl
   
   # Azure Storage Configuration
   AZURE_STORAGE_CONNECTION_STRING=your_azure_connection_string
   AZURE_STORAGE_CONTAINER_NAME=methodology-files
   
   # Authentication Configuration
   AUTH_ENABLED=true
   ```

3. **Create environment files for different stages**
   ```bash
   cp .env.development .env.test
   cp .env.development .env.production
   ```

## 🗄️ Database Setup

1. **Start PostgreSQL server**
   ```bash
   # Using Docker
   docker run --name postgres -e POSTGRES_PASSWORD=your_password -p 5432:5432 -d postgres
   
   # Or start your local PostgreSQL service
   ```

2. **Run database migrations**
   ```bash
   yarn prisma migrate dev
   ```

3. **Seed the database with initial roles, permissions, and users**
   ```bash
   # Edit the seed configuration first
   # Update src/scripts/seed/config.ts with your Azure AD Object IDs
   
   # Run the full seeding process
   yarn seed
   
   # Or run individual utilities
   yarn seed:stats      # View current seeded data
   yarn seed:validate   # Validate data integrity
   yarn seed:export     # Export data to JSON
   ```

   See `src/scripts/seed/README.md` for detailed seeding documentation.

## 🚀 Running the Application

### Development Mode

```bash
# Start in development mode with hot reload
yarn dev
```

### Production Mode

```bash
# Build the application
yarn build

# Start the production server
yarn start
```

### Available Scripts

- `yarn dev` - Start development server with hot reload
- `yarn build` - Build the application for production
- `yarn start` - Start the production server
- `yarn test` - Run tests
- `yarn test:coverage` - Run tests with coverage report
- `yarn test:ui` - Run tests with UI interface
- `yarn lint` - Run linter
- `yarn lint:fix` - Run linter and fix issues
- `yarn format` - Format code with Biome

## 📚 API Documentation

Once the application is running, you can access the API documentation at:

- **Swagger UI**: `http://localhost:4000/api-docs`
- **Health Check**: `http://localhost:4000/api/health`

### API Endpoints

#### Einstein API (`/api/einstein`)
- `/how-to` - How-to guides management
- `/projects` - Project management
- `/users` - User management
- `/map` - Map services
- `/methodologies` - Methodology management
- `/search` - Search functionality
- `/feedback` - Feedback system
- `/files` - File management

#### Abacus API (`/api/abacus`)
- `/users` - User management for Abacus
- `/projects` - Project management for Abacus
- `/accumulated-duration-refactored` - Duration calculations

## 🧪 Testing

Run the test suite using:

```bash
# Run all tests
yarn test

# Run tests with coverage
yarn test:coverage

# Run tests with UI
yarn test:ui

# Run tests in watch mode
yarn test --watch
```

## 🐳 Docker Support

### Development Environment

```bash
# Build and run with Docker
docker build -f Dockerfile.dev -t einstein-backend-dev .
docker run -p 4000:4000 einstein-backend-dev
```

### Using Docker Compose (recommended)

Create a `docker-compose.yml` file:

```yaml
version: '3.8'
services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "4000:4000"
    environment:
      - NODE_ENV=development
    depends_on:
      - postgres
      - mongodb
      - elasticsearch
    volumes:
      - .:/app
      - /app/node_modules

  postgres:
    image: postgres:15
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: einstein_db
    ports:
      - "5432:5432"

  mongodb:
    image: mongo:6
    ports:
      - "27017:27017"

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
```

Then run:

```bash
docker-compose up
```

## 🔧 Configuration Details

### Rate Limiting

The application includes several rate limiting configurations:

- **General**: 100 requests per 15 minutes
- **API**: 100 requests per minute
- **Authentication**: 100 requests per 15 minutes
- **Upload**: 10 requests per hour
- **Search**: 80 requests per minute

### Logging

Logs are configured with Winston and include:

- **Development**: Debug level logging to console and file
- **Production**: Info level logging to file with rotation
- **Log Files**: Stored in `./logs/` directory

### Security

- CORS enabled for cross-origin requests
- Authentication middleware with Azure AD integration
- Rate limiting on all endpoints
- Request correlation IDs for tracing
- Comprehensive error handling

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature`
3. Commit your changes: `git commit -am 'Add your feature'`
4. Push to the branch: `git push origin feature/your-feature`
5. Submit a pull request

### Code Style

This project uses Biome for code formatting and linting:

```bash
# Format code
yarn format

# Lint code
yarn lint

# Fix linting issues
yarn lint:fix
```

## 📄 License

This project is proprietary and confidential. All rights reserved. Unauthorized copying, distribution, or use of this software is strictly prohibited.

## 🆘 Support

For support and questions:

1. Check the [API Documentation](http://localhost:4000/api-docs)
2. Review the logs in the `./logs/` directory
3. Create an issue in the repository
4. Contact the development team

---

**Happy Coding! 🚀**