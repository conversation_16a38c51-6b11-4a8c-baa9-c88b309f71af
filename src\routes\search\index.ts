import { Router } from "express";
import { ElasticSearchController } from "../../controllers/search/elasticsearch.controller";
import ElasticSearchService from "../../services/elasticSearch/elasticSearch.service";
import { ElasticSearchFolderController } from "../../services/elasticSearch/elasticSearchFolder.controller";
import { ElasticSearchFolderService } from "../../services/elasticSearch/elasticSearchFolder.service";
import config from "../../config/config";
import { searchLimiter } from "../../middlewares/rateLimiting";

// Create router
const router = Router();

// Create Elasticsearch service and controller
const elasticSearchClient = ElasticSearchService.createClient({
	node: config.elasticsearch?.node || "https://localhost:9200",
	username: config.elasticsearch?.username || "elastic",
	password: config.elasticsearch?.password || "",
	rejectUnauthorized: config.elasticsearch?.rejectUnauthorized !== false,
});

// const esIndexName = config.elasticsearch?.indexName || 'sharepoint_crawl'; // No longer needed for service constructor
const elasticSearchInstance = new ElasticSearchService(elasticSearchClient);
const elasticSearchController = new ElasticSearchController(
	elasticSearchInstance,
);

// Create ElasticSearchFolder service and controller
const elasticSearchFolderService = new ElasticSearchFolderService();
const elasticSearchFolderController = new ElasticSearchFolderController(
	elasticSearchFolderService,
);

// Search routes - Updated
router.post("/search", searchLimiter, elasticSearchController.searchPOST);

// ElasticSearchFolder routes
router.post(
	"/path-search",
	searchLimiter,
	elasticSearchFolderController.searchByEsIndexAndFilePath,
);

export default router;
