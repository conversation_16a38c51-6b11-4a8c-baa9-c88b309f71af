import { FileService } from "../file.service";
import type { MongoFileRepository } from "../mongoFile.repository";
import type { AzureBlobService } from "../azureBlob.service";
import { vi, describe, it, expect, beforeEach } from "vitest";
import type { File } from "../file.model";

describe("FileService", () => {
	// Test data
	const testUserId = "test-user-id";
	const testFileName = "test-file.txt";
	const testStorageName = "storage-name-123";
	const testUrl =
		"https://test-storage.blob.core.windows.net/container/test-file.txt";
	const testSasToken = "mock-sas-token";

	// Mock repositories with vi.spyOn
	let fileService: FileService;
	let mockFileRepo: MongoFileRepository;
	let mockAzureBlobService: AzureBlobService;

	beforeEach(() => {
		// Create mock repositories
		mockFileRepo = {
			create: vi.fn(),
			findByUserId: vi.fn(),
			findByStorageName: vi.fn(),
			updateByUserId: vi.fn(),
		} as unknown as MongoFileRepository;

		mockAzureBlobService = {
			uploadFile: vi.fn(),
			generateSASToken: vi.fn(),
		} as unknown as AzureBlobService;

		// Set up spies
		// Using 'any' here is acceptable in tests to mock the repository behavior
		vi.spyOn(mockFileRepo, "create").mockImplementation((file) =>
			// eslint-disable-next-line @typescript-eslint/no-explicit-any
			Promise.resolve(file as unknown as File),
		);
		vi.spyOn(mockFileRepo, "findByUserId").mockResolvedValue([]);
		vi.spyOn(mockFileRepo, "findByStorageName").mockResolvedValue(null);
		vi.spyOn(mockFileRepo, "updateByUserId").mockResolvedValue(null);

		vi.spyOn(mockAzureBlobService, "uploadFile").mockResolvedValue({
			url: testUrl,
			storageName: testStorageName,
		});
		vi.spyOn(mockAzureBlobService, "generateSASToken").mockResolvedValue(
			testSasToken,
		);

		// Create service with mock repositories
		fileService = new FileService(mockFileRepo, mockAzureBlobService);
	});

	afterEach(() => {
		vi.resetAllMocks();
	});

	describe("uploadFile", () => {
		it("should upload files successfully", async () => {
			// Arrange
			const mockFiles = [
				{
					buffer: Buffer.from("test content"),
					originalname: testFileName,
					mimetype: "text/plain",
					size: 100,
				},
			] as Express.Multer.File[];

			const mockCreatedFile = {
				url: testUrl,
				storageName: testStorageName,
				userId: testUserId,
				originalName: testFileName,
				mimeType: "text/plain",
				size: 100,
				isPrivate: false,
				sharedWith: [],
				metadata: {},
			};

			mockFileRepo.create = vi.fn().mockResolvedValue(mockCreatedFile);

			// Act
			const result = await fileService.uploadFile({
				files: mockFiles,
				userId: testUserId,
				isPrivate: false,
				sharedWith: [],
			});

			// Assert
			expect(mockAzureBlobService.uploadFile).toHaveBeenCalledWith(
				mockFiles[0].buffer,
				mockFiles[0].originalname,
				mockFiles[0].mimetype,
			);

			expect(mockFileRepo.create).toHaveBeenCalledWith(
				expect.objectContaining({
					url: testUrl,
					storageName: testStorageName,
					userId: testUserId,
					originalName: testFileName,
				}),
			);

			expect(result).toHaveLength(1);
			expect(result[0]).toEqual(mockCreatedFile);
		});

		it("should handle multiple files", async () => {
			// Arrange
			const mockFiles = [
				{
					buffer: Buffer.from("file 1 content"),
					originalname: "file1.txt",
					mimetype: "text/plain",
					size: 100,
				},
				{
					buffer: Buffer.from("file 2 content"),
					originalname: "file2.txt",
					mimetype: "text/plain",
					size: 200,
				},
			] as Express.Multer.File[];

			mockAzureBlobService.uploadFile = vi
				.fn()
				.mockImplementation((_buffer, name, _mimetype) => {
					return Promise.resolve({
						url: `https://test-storage.blob.core.windows.net/container/${name}`,
						storageName: `storage-${name}`,
					});
				});

			// eslint-disable-next-line @typescript-eslint/no-explicit-any
			mockFileRepo.create = vi.fn().mockImplementation((fileData) => {
				// eslint-disable-next-line @typescript-eslint/no-explicit-any
				return Promise.resolve(fileData as unknown as File);
			});

			// Act
			const result = await fileService.uploadFile({
				files: mockFiles,
				userId: testUserId,
				isPrivate: true,
				sharedWith: ["shared-user-id"],
			});

			// Assert
			expect(mockAzureBlobService.uploadFile).toHaveBeenCalledTimes(2);
			expect(mockFileRepo.create).toHaveBeenCalledTimes(2);
			expect(result).toHaveLength(2);
			expect(result[0].originalName).toBe("file1.txt");
			expect(result[1].originalName).toBe("file2.txt");
		});
	});

	describe("getUserFiles", () => {
		it("should return files for a user", async () => {
			// Arrange
			const mockFiles = [
				{ userId: testUserId, originalName: "file1.txt" },
				{ userId: testUserId, originalName: "file2.txt" },
			];

			mockFileRepo.findByUserId = vi.fn().mockResolvedValue(mockFiles);

			// Act
			const result = await fileService.getUserFiles(testUserId);

			// Assert
			expect(mockFileRepo.findByUserId).toHaveBeenCalledWith(testUserId);
			expect(result).toEqual(mockFiles);
		});
	});

	describe("getFilesByUserId", () => {
		it("should return files for specified user ID", async () => {
			// Arrange
			const mockFiles = [
				{ userId: testUserId, originalName: "file1.txt" },
				{ userId: testUserId, originalName: "file2.txt" },
			];

			mockFileRepo.findByUserId = vi.fn().mockResolvedValue(mockFiles);

			// Act
			const result = await fileService.getFilesByUserId(testUserId);

			// Assert
			expect(mockFileRepo.findByUserId).toHaveBeenCalledWith(testUserId);
			expect(result).toEqual(mockFiles);
		});
	});

	describe("accessFile", () => {
		it("should return SAS token when user is the owner", async () => {
			// Arrange
			const mockFile = {
				userId: testUserId,
				storageName: testStorageName,
				isPrivate: true,
				sharedWith: [],
			};

			mockFileRepo.findByStorageName = vi.fn().mockResolvedValue(mockFile);
			mockAzureBlobService.generateSASToken = vi
				.fn()
				.mockResolvedValue(testSasToken);

			// Act
			const result = await fileService.accessFile(testStorageName, testUserId);

			// Assert
			expect(mockFileRepo.findByStorageName).toHaveBeenCalledWith(
				testStorageName,
			);
			expect(mockAzureBlobService.generateSASToken).toHaveBeenCalledWith(
				testStorageName,
			);
			expect(result).toEqual(testSasToken);
		});

		it("should throw error when file is not found", async () => {
			// Arrange
			mockFileRepo.findByStorageName = vi.fn().mockResolvedValue(null);

			// Act & Assert
			await expect(
				fileService.accessFile(testStorageName, testUserId),
			).rejects.toThrow(/not found/i);
		});
	});

	describe("grantAccessToFile", () => {
		it("should grant access to file and return SAS token", async () => {
			// Arrange
			const otherUserId = "other-user-id";
			const mockFile = {
				userId: testUserId,
				storageName: testStorageName,
				isPrivate: true,
				sharedWith: [],
			};

			mockFileRepo.findByStorageName = vi.fn().mockResolvedValue(mockFile);
			mockFileRepo.updateByUserId = vi.fn().mockResolvedValue(true);
			mockAzureBlobService.generateSASToken = vi
				.fn()
				.mockResolvedValue(testSasToken);

			// Act
			const result = await fileService.grantAccessToFile({
				fileName: testStorageName,
				userId: otherUserId,
				ownerId: testUserId,
			});

			// Assert
			expect(mockFileRepo.findByStorageName).toHaveBeenCalledWith(
				testStorageName,
			);
			expect(mockFileRepo.updateByUserId).toHaveBeenCalledWith(testUserId, {
				sharedWith: [otherUserId],
			});
			expect(mockAzureBlobService.generateSASToken).toHaveBeenCalledWith(
				testStorageName,
			);
			expect(result).toEqual(testSasToken);
		});
	});
});
