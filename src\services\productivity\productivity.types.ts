// Request body types for productivity endpoints

export interface GetStagesByPhaseRequest {
	phase: string;
}

export interface GetCategoryTypesByPhaseAndStageRequest {
	phase: string;
	stage: string;
}

export interface GetDataByPhaseStageAndCategoryTypeRequest {
	phase: string;
	stage: string;
	categoryType: string;
}

// Response types
export interface ProductivityDataResponse<T = any> {
	data: T;
}

export interface ProductivityErrorResponse {
	error: string;
}
