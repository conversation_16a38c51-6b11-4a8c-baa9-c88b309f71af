export interface FilterConfig {
	generated_at: Date;
	version: string;
	source_collection: string;
	total_documents: number;
	common_filters: CommonFilter[];
	dynamic_filters: DynamicFilters;
	statistics: Statistics;
}

export interface CommonFilter {
	field: string;
	label: string;
	type: string;
	options?: string[];
	count?: number;
	min?: number;
	max?: number;
	formatted_max?: string;
}

export interface DynamicFilters {
	commercial: Commercial;
	education: Education;
	health: Education;
}

export interface Commercial {
	type: string;
	filters: CommercialFilter[];
	total_filters: number;
	configured: boolean;
}

export interface CommercialFilter {
	field: string;
	label: string;
	item_name: string;
	occurrences: number;
	type: string;
	min: number;
	max: number;
}

export interface Education {
	type: string;
	filters: EducationFilter[];
	total_filters: number;
	configured: boolean;
}

export interface EducationFilter {
	field: string;
	label: string;
	item_name: string;
	occurrences: number;
	type: string;
	options: string[];
	options_count: number;
}

export interface Statistics {
	common_filters_count: number;
	dynamic_filter_types: number;
	total_dynamic_filters: number;
	total_dropdown_options: number;
}
