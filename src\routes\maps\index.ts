import express, { Router, NextFunction } from "express";
import { MapController } from "@/controllers/maps/map.controller";
import { MapService } from "@/services/maps/map.service";
import { MongoMapRepository } from "@/services/repositories/mongoMap.repository";

/**
 * Router for map endpoints
 */
const router = Router();
const mapRepository = new MongoMapRepository();
const mapService = new MapService(mapRepository);
const mapController = new MapController(mapService);

// Get available filters
router.get("/filters", async (req, res, next) => {
	await mapController.getFilters(req, res, next);
});

// Get filtered project locations
router.post("/filtered", async (req, res, next) => {
	await mapController.getFilteredProjectLocations(req, res, next);
});

// Get project by ID
router.get("/project/:projectId", async (req, res, next) => {
	await mapController.getProjectById(req, res, next);
});

// Download filtered project data
router.post("/download", async (req, res, next) => {
	await mapController.downloadFilteredData(req, res, next);
});

export default router;
