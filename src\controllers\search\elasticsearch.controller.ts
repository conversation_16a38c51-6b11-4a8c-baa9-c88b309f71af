import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import type ElasticSearchService from "../../services/elasticSearch/elasticSearch.service";
import { errors as esErrors } from "@elastic/elasticsearch";
import { z } from "zod";
import logger from "@/utils/logging";
import QueryParser from "@/utils/queryParser";

// === Zod Schemas for Validation ===

// Base filter schema (allowing string, number, boolean, or array of strings)
const filterValueSchema = z.union([
	z.string(),
	z.number(),
	z.boolean(),
	z.array(z.string()),
]);

// Schema for the POST /search request body
const postSearchBodySchema = z.object({
	indexes: z.array(z.string()).min(1, "At least one index must be provided"),
	query: z.string().optional(),
	filters: z.record(filterValueSchema).optional(),
	file_path_filter: z.string().optional(), // Wildcard pattern filter for file_path field
	field: z.string().optional(), // Keep optional for backwards compatibility but don't use for search limiting
	size: z.number().int().positive().max(100).optional().default(10),
	from: z.number().int().nonnegative().optional().default(0),
	includeContent: z.boolean().optional().default(false),
	highlight: z.boolean().optional().default(true),
});

export class ElasticSearchController {
	constructor(private readonly elasticSearchService: ElasticSearchService) {}

	// POST /search endpoint with advanced query support
	searchPOST: RequestHandler = async (req, res, next) => {
		try {
			// Validate request body
			const searchOptions = postSearchBodySchema.parse(req.body);

			// Validate query syntax if provided
			if (searchOptions.query) {
				const queryValidation = QueryParser.validateQuery(searchOptions.query);
				if (!queryValidation.valid) {
					logger.warn("Invalid query syntax", {
						query: searchOptions.query,
						error: queryValidation.error,
					});
					res.status(400).json({
						message: "Invalid query syntax",
						error: queryValidation.error,
						examples: [
							"Simple search: windfarm",
							"Boolean operators: wind AND farm",
							'Exact phrases: "wind farm project"',
							"Wildcards: wind*",
							"Field search: title:meeting",
							"Complex: (urgent OR priority) AND title:meeting",
						],
					});
					return;
				}
			}

			logger.info("Received POST /search request", {
				query: searchOptions.query,
				indexes: searchOptions.indexes,
				size: searchOptions.size,
				from: searchOptions.from,
			});

			// Execute search
			const result = await this.elasticSearchService.search(searchOptions);

			res.status(200).json({
				...result,
				meta: {
					query: searchOptions.query,
					indexes: searchOptions.indexes,
					size: searchOptions.size,
					from: searchOptions.from,
				},
			});
		} catch (error) {
			// Handle validation errors
			if (error instanceof z.ZodError) {
				logger.warn("Search validation error", { errors: error.errors });
				res.status(400).json({
					message: "Invalid search parameters",
					errors: error.errors,
				});
				return;
			}

			// Handle Elasticsearch errors
			if (error instanceof esErrors.ResponseError) {
				logger.error("Elasticsearch error", {
					status: error.statusCode,
					body: error.body,
				});

				if (error.statusCode === 400) {
					res.status(400).json({
						message: "Search request failed due to invalid query or parameters",
						details: error.body,
					});
				} else {
					res.status(500).json({
						message: "Search service encountered an error",
					});
				}
				return;
			}

			// Handle other errors
			logger.error("Search unexpected error", { error });
			next(error);
		}
	};

	/**
	 * Get query syntax help
	 */
	getQueryHelp: RequestHandler = (_req, res) => {
		res.status(200).json({
			message: "Advanced Search Query Syntax",
			syntax: {
				"Simple Search": "windfarm",
				"Boolean Operators": {
					AND: "wind AND farm",
					OR: "wind OR solar",
					NOT: "wind NOT coal",
					Minus: "wind -coal",
				},
				"Exact Phrases": '"wind farm project"',
				Wildcards: {
					Asterisk: "wind*",
					"Question Mark": "test?ing",
				},
				"Field Search": {
					Title: "title:meeting",
					Content: "content:budget",
					"File Type": "file_type:pdf",
					"Project Code": "project_code:ABC123",
				},
				Grouping: "(urgent OR priority) AND title:meeting",
				"Complex Query":
					'"quarterly report" AND (title:meeting OR content:budget) NOT draft',
			},
			searchable_fields: [
				"title",
				"content",
				"file_name",
				"file_type",
				"project_code",
				"url",
				"file_path",
			],
		});
	};
}
