import { z } from 'zod';

export const createMethodologySchema = z.object({
  name: z.string().min(1),
  parentId: z.string().nullable().optional(),
  order: z.number().optional(),
  description: z.string().optional(),
  nextLevelName: z.string().optional(),
  nodeType: z.enum(['regular', 'step']).optional(),
  experts: z
    .array(
      z.object({
        name: z.string().min(1), // Name is required
        email: z.preprocess(
          // Convert empty string to undefined
          (val) => (val === '' ? undefined : val),
          z.string().email().optional()
        ), // Email is optional
        title: z.preprocess(
          // Convert empty string to undefined
          (val) => (val === '' ? undefined : val),
          z.string().optional()
        ), // Title is optional
      })
    )
    .optional(),
});

export const getMethodologiesSchema = z.object({
  parentId: z.string().optional(),
});

export const createNodeSchema = z
  .object({
    name: z.string().min(1),
    parentId: z.string().optional(),
    nodeType: z.enum(['regular', 'step']),
    order: z.number().min(0).optional(),
    description: z.string().optional(),
    nextLevelName: z.string().optional(),
    experts: z
      .array(
        z.object({
          name: z.string().min(1), // Name is required
          email: z.preprocess(
            // Convert empty string to undefined
            (val) => (val === '' ? undefined : val),
            z.string().email().optional()
          ), // Email is optional
          title: z.preprocess(
            // Convert empty string to undefined
            (val) => (val === '' ? undefined : val),
            z.string().optional()
          ), // Title is optional
        })
      )
      .optional(),
  })
  .refine(
    (data) => {
      // If it's a regular node, nextLevelName should be present
      if (data.nodeType === 'regular') {
        return !!data.nextLevelName;
      }
      // If it's a step node, parentId should be present and nextLevelName, experts should not be present
      if (data.nodeType === 'step') {
        return !!data.parentId && !data.nextLevelName && !data.experts;
      }
      return true;
    },
    {
      message:
        'For step nodes: parentId is required, nextLevelName and experts not allowed. ' +
        'For regular nodes: nextLevelName is required',
    }
  );

export const createStepSchema = z.object({
  name: z.string().min(1),
  parentId: z.string(),
  order: z.number().min(0),
});

export const bulkUploadSchema = z.object({
  nodeIds: z.array(z.string()).optional(),
  stepIds: z.array(z.string()).optional(),
  docType: z.enum(['framework', 'template', 'example']),
});

// Create a schema for updating nodes that doesn't have the refinement
export const updateNodeSchema = z.object({
  name: z.string().min(1).optional(),
  parentId: z.string().optional(),
  nodeType: z.enum(['regular', 'step']).optional(),
  order: z.number().min(0).optional(),
  description: z.string().optional(),
  nextLevelName: z.string().optional(),
  experts: z
    .array(
      z.object({
        name: z.string().min(1), // Name is required
        email: z.preprocess(
          // Convert empty string to undefined
          (val) => (val === '' ? undefined : val),
          z.string().email().optional()
        ), // Email is optional
        title: z.preprocess(
          // Convert empty string to undefined
          (val) => (val === '' ? undefined : val),
          z.string().optional()
        ), // Title is optional
      })
    )
    .optional(),
});

export type CreateMethodologyInput = z.infer<typeof createMethodologySchema>;
export type GetMethodologiesInput = z.infer<typeof getMethodologiesSchema>;
export type CreateNodeInput = z.infer<typeof createNodeSchema>;
export type UpdateNodeInput = z.infer<typeof updateNodeSchema>;
export type CreateStepInput = z.infer<typeof createStepSchema>;
export type BulkUploadInput = z.infer<typeof bulkUploadSchema>;
