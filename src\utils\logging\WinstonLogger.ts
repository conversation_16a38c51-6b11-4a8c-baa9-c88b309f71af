/* eslint-disable @typescript-eslint/no-explicit-any */
import winston from 'winston';
import { developmentFormat, productionFormat } from './format';
import { ILogger } from '@/shared/interfaces/ILogger';
import 'winston-daily-rotate-file';

export class WinstonLogger implements ILogger {
  private logger: winston.Logger;

  constructor() {
    const nodeEnv = process.env.NODE_ENV || 'development';
    const logLevel = nodeEnv === 'development' ? 'debug' : 'info';
    const logPath = process.env.LOG_PATH || `./logs/${nodeEnv}.log`;
    
    if (nodeEnv === 'test') {
      // Simple console logger for tests
      this.logger = winston.createLogger({
        level: 'error',
        format: winston.format.simple(),
        transports: [
          new winston.transports.Console({
            format: winston.format.simple(),
          }),
        ],
      });
    } else {
      const format = nodeEnv === 'production' ? productionFormat : developmentFormat;
      this.logger = winston.createLogger({
        level: logLevel,
        format,
        defaultMeta: {},
        transports: [
          new winston.transports.Console({
            level: logLevel,
            format: nodeEnv === 'production' ? productionFormat : developmentFormat,
          }),
          new winston.transports.DailyRotateFile({
            level: logLevel,
            filename: logPath,
            datePattern: 'YYYY-MM-DD',
            zippedArchive: true,
            maxSize: '20m',
            maxFiles: '14d',
            format: productionFormat,
          }),
          new winston.transports.DailyRotateFile({
            level: 'error',
            filename: `${logPath}/error-%DATE%.log`,
            datePattern: 'YYYY-MM-DD',
            zippedArchive: true,
            maxSize: '20m',
            maxFiles: '14d',
            format: productionFormat,
          }),
        ],
      });
    }
  }

  error(message: string, meta?: any): void {
    try {
      this.logger.error(message, meta);
    } catch (err) {
      console.error('Logging failed:', err);
      console.error('Original message:', message);
    }
  }

  warn(message: string, meta?: Record<string, unknown>): void {
    this.logger.warn(message, meta);
  }

  info(message: string, meta?: Record<string, unknown>): void {
    this.logger.info(message, meta);
  }

  http(message: string, meta?: Record<string, unknown>): void {
    this.logger.http(message, meta);
  }

  debug(message: string, meta?: Record<string, unknown>): void {
    this.logger.debug(message, meta);
  }
}
