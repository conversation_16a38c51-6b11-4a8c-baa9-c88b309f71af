import {
	getProductivityModel,
	type ProductivityDocument,
} from "@/models/productivity/productivity.model";
import type { ProductivityFilter } from "@/services/productivity/productivity.service";

export interface IProductivityRepository {
	findByPhaseAndStage(
		filter: ProductivityFilter,
	): Promise<ProductivityDocument[]>;
	getDistinctPhases(): Promise<string[]>;
	getDistinctStagesByPhase(phase: string): Promise<string[]>;
	getDistinctCategoryTypesByPhaseAndStage(
		phase: string,
		stage: string,
	): Promise<string[]>;
	getDataByPhaseStageAndCategoryType(
		phase: string,
		stage: string,
		categoryType: string,
	): Promise<ProductivityDocument[]>;
}

export class ProductivityRepository implements IProductivityRepository {
	async findByPhaseAndStage(
		filter: ProductivityFilter,
	): Promise<ProductivityDocument[]> {
		const model = getProductivityModel();
		const query: Record<string, string> = {};
		if (filter.phase) query.phase = filter.phase;
		if (filter.stage) query.stage = filter.stage;
		return model.find(query).exec();
	}

	async getDistinctPhases(): Promise<string[]> {
		const model = getProductivityModel();
		const result = await model
			.aggregate([
				{
					$group: {
						_id: "$phase",
						order1: { $min: "$order1" },
					},
				},
				{
					$sort: { order1: 1 },
				},
				{
					$project: {
						_id: 1,
					},
				},
			])
			.exec();

		return result.map((item) => item._id);
	}

	async getDistinctStagesByPhase(phase: string): Promise<string[]> {
		const model = getProductivityModel();
		const result = await model
			.aggregate([
				{
					$match: { phase },
				},
				{
					$group: {
						_id: "$stage",
						order2: { $min: "$order2" },
					},
				},
				{
					$sort: { order2: 1 },
				},
				{
					$project: {
						_id: 1,
					},
				},
			])
			.exec();

		return result.map((item) => item._id);
	}

	async getDistinctCategoryTypesByPhaseAndStage(
		phase: string,
		stage: string,
	): Promise<string[]> {
		const model = getProductivityModel();
		return model.distinct("type", { phase, stage }).exec();
	}

	async getDataByPhaseStageAndCategoryType(
		phase: string,
		stage: string,
		categoryType: string,
	): Promise<ProductivityDocument[]> {
		const model = getProductivityModel();
		return model.find({ phase, stage, type: categoryType }).exec();
	}
}
