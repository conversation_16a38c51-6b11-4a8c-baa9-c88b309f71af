// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { prop, getModelForClass, modelOptions } from '@typegoose/typegoose';

@modelOptions({
  schemaOptions: {
    timestamps: true,
    collection: 'estimates',
  },
  options: {
    allowMixed: 0,
  },
})
export class Estimates {
  @prop({ required: true })
  public project_code!: string;

  @prop({ required: false, default: null })
  public item_number!: string | null;

  @prop({ required: false, default: null })
  public item_text!: string | null;

  @prop({ required: false, default: null })
  public description!: string | null;

  @prop({ required: false, default: null })
  public unit!: string | null;

  @prop({ required: false, default: null })
  public quantity!: number | null;

  @prop({ required: false, default: null })
  public categories_labour!: string | null;

  @prop({ required: false, default: null })
  public categories_material!: string | null;

  @prop({ required: false, default: null })
  public categories_plant!: string | null;

  @prop({ required: false, default: null })
  public categories_subcontract!: string | null;

  @prop({ required: false, default: null })
  public categories_other!: string | null;

  @prop({ required: false, default: null })
  public rate!: number | null;

  @prop({ required: false, default: null })
  public amount!: number | null;

  @prop({ required: false, default: null })
  public icms_code!: string | null;

  @prop({ required: false, default: false })
  public is_header!: boolean;
}

const EstimatesModel = getModelForClass(Estimates);

export default EstimatesModel;
