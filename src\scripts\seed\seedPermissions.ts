import PermissionModel from "@/models/permission.model";
import logger from "@/utils/logging";
import { seedConfig, seedOptions, type SeedPermission } from "./config";

/**
 * Seeds permissions into the database
 * This function is idempotent - it can be run multiple times safely
 */
export async function seedPermissions(): Promise<void> {
	const { permissions } = seedConfig;
	const { updateExisting, verbose, recreate, dryRun } = seedOptions;

	logger.info(`Starting to seed ${permissions.length} permissions...`);

	if (dryRun) {
		logger.info("🔍 DRY RUN MODE - No database changes will be made");
	}

	if (recreate && !dryRun) {
		logger.warn("⚠️  RECREATE mode: Deleting all existing permissions...");
		await PermissionModel.deleteMany({});
		logger.info("✅ All permissions deleted");
	} else if (recreate && dryRun) {
		logger.warn("🔍 DRY RUN: Would delete all existing permissions");
	}

	const results = {
		created: 0,
		updated: 0,
		skipped: 0,
		errors: 0,
	};

	for (const permission of permissions) {
		try {
			await seedPermission(
				permission,
				updateExisting,
				verbose,
				dryRun,
				results,
			);
		} catch (error) {
			results.errors++;
			logger.error(`❌ Error seeding permission "${permission.name}":`, error);
		}
	}

	// Summary
	logger.info(`📊 Permission seeding ${dryRun ? "(DRY RUN) " : ""}summary:`);
	logger.info(
		`   ✅ ${dryRun ? "Would create" : "Created"}: ${results.created}`,
	);
	logger.info(
		`   🔄 ${dryRun ? "Would update" : "Updated"}: ${results.updated}`,
	);
	logger.info(`   ⏭️  Skipped: ${results.skipped}`);
	if (results.errors > 0) {
		logger.error(`   ❌ Errors: ${results.errors}`);
	}

	logger.info(`✅ Permission seeding ${dryRun ? "(DRY RUN) " : ""}completed!`);
}

async function seedPermission(
	permission: SeedPermission,
	updateExisting: boolean,
	verbose: boolean,
	dryRun: boolean,
	results: {
		created: number;
		updated: number;
		skipped: number;
		errors: number;
	},
): Promise<void> {
	const existingPermission = await PermissionModel.findOne({
		name: permission.name,
	}).lean();

	if (existingPermission) {
		if (updateExisting) {
			// Check if update is needed
			const needsUpdate =
				existingPermission.description !== permission.description;

			if (needsUpdate) {
				if (!dryRun) {
					await PermissionModel.findOneAndUpdate(
						{ name: permission.name },
						{ description: permission.description },
						{ new: true },
					);
				}
				results.updated++;
				if (verbose) {
					logger.info(
						`🔄 ${dryRun ? "Would update" : "Updated"} permission: ${permission.name}`,
					);
				}
			} else {
				results.skipped++;
				if (verbose) {
					logger.info(`⏭️  Skipped permission (no changes): ${permission.name}`);
				}
			}
		} else {
			results.skipped++;
			if (verbose) {
				logger.info(`⏭️  Skipped existing permission: ${permission.name}`);
			}
		}
	} else {
		// Create new permission
		if (!dryRun) {
			await PermissionModel.create({
				name: permission.name,
				description: permission.description,
			});
		}
		results.created++;
		if (verbose) {
			logger.info(
				`✅ ${dryRun ? "Would create" : "Created"} permission: ${permission.name}`,
			);
		}
	}
}

// Allow running this script directly
if (require.main === module) {
	(async () => {
		try {
			// Import database connection
			const { connectMongo } = await import(
				"@/infrastructure/database/mongo.connection"
			);
			const config = await import("@/config/config");

			// Connect to MongoDB
			await connectMongo(config.default.database.mongo.uri);
			logger.info("📦 Connected to MongoDB");

			// Run the seeder
			await seedPermissions();

			process.exit(0);
		} catch (error) {
			logger.error("💥 Error in permission seeding:", error);
			process.exit(1);
		}
	})();
}
