import config from "@/config/config";
import logger from "@/utils/logging";
import type { Request, Response } from "express";
import rateLimit from "express-rate-limit";

// Create a custom key generator that includes user ID if available
const keyGenerator = (req: Request): string => {
	// Use user ID from token if available, otherwise use IP
	const userId = (req as any).user?.id;
	return userId ? `user_${userId}` : req.ip || "unknown";
};

// Custom handler for when rate limit is exceeded
const handler = (req: Request, res: Response) => {
	logger.warn("Rate limit exceeded", {
		ip: req.ip,
		path: req.path,
		method: req.method,
		userId: (req as any).user?.id,
	});

	res.status(429).json({
		status: 429,
		message: "Too many requests, please try again later.",
	});
};

// General rate limiter - applies to all requests
export const generalLimiter = rateLimit({
	windowMs: config.rateLimit.general.windowMs,
	max: config.rateLimit.general.max,
	message: "Too many requests from this IP, please try again later.",
	standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
	legacyHeaders: false, // Disable the `X-RateLimit-*` headers
	keyGenerator,
	handler,
	skip: (req) =>
		process.env.NODE_ENV === "development" || req.path === "/api/health", // Skip rate limiting for health checks and development
});

// Strict rate limiter for authentication endpoints
export const authLimiter = rateLimit({
	windowMs: config.rateLimit.auth.windowMs,
	max: config.rateLimit.auth.max,
	message: "Too many authentication attempts, please try again later.",
	standardHeaders: true,
	legacyHeaders: false,
	keyGenerator: (req) => req.ip || "unknown", // Use IP only for auth endpoints
	handler,
	skipSuccessfulRequests: true, // Don't count successful requests
});

// API endpoint specific rate limiter
export const apiLimiter = rateLimit({
	windowMs: config.rateLimit.api.windowMs,
	max: config.rateLimit.api.max,
	message: "Too many API requests, please slow down.",
	standardHeaders: true,
	legacyHeaders: false,
	keyGenerator,
	handler,
	skip: () => process.env.NODE_ENV === "development", // Skip in development
});

// File upload rate limiter
export const uploadLimiter = rateLimit({
	windowMs: config.rateLimit.upload.windowMs,
	max: config.rateLimit.upload.max,
	message: "Too many file uploads, please try again later.",
	standardHeaders: true,
	legacyHeaders: false,
	keyGenerator,
	handler,
});

// Search endpoint rate limiter
export const searchLimiter = rateLimit({
	windowMs: config.rateLimit.search.windowMs,
	max: config.rateLimit.search.max,
	message: "Too many search requests, please slow down.",
	standardHeaders: true,
	legacyHeaders: false,
	keyGenerator,
	handler,
});
