import ProjectInputDataModel from '@/models/abacus/projectInputData.model';
import { ReferenceListModel } from '@/models/abacus/referenceList.model';
import { SectorQuestionModel } from '@/models/abacus/sectorQuestion.model';
import { SubSectorQuestionModel } from '@/models/abacus/subSectorQuestion.model';
import { ProjectModel } from '@/models/project.model';

const getAllSectors = async () => {
  const result = await ReferenceListModel.aggregate([
    {
      $match: {
        sector: { $ne: null },
      },
    },
    {
      $group: {
        _id: '$sector',
        sector_code: { $first: '$sector_code' },
      },
    },
    {
      $project: {
        _id: 0,
        sector: '$_id',
        sector_code: 1,
      },
    },
    {
      $sort: {
        sector: 1,
      },
    },
  ]);
  return result;
};

const getSubSectorsForSector = async (sectorName: string) => {
  const result = await ReferenceListModel.find({ sector: sectorName }).distinct('sub_sector');
  return result;
};

const getConstructionCostSources = async () => {
  const result = await ReferenceListModel.find({ type: 'construction_cost_source' }).distinct(
    'source_of_construction_cost'
  );
  return result;
};

const getLevelOfEstimate = async () => {
  const result = await ReferenceListModel.find({ type: 'estimate_level' }).distinct(
    'level_of_estimate'
  );

  // Sort the results by class number
  return result.sort((a, b) => {
    // Extract the class number from strings like "Class 1 (-10% to +15%)"
    const classNumberA = parseInt(a.match(/Class (\d+)/)?.[1] || '0');
    const classNumberB = parseInt(b.match(/Class (\d+)/)?.[1] || '0');

    // Sort by the extracted numbers
    return classNumberA - classNumberB;
  });
};

const getLandTypes = async () => {
  const result = await ReferenceListModel.find({ type: 'land_type' }).distinct('land_type');
  return result;
};

const getProcurementModels = async () => {
  const result = await ReferenceListModel.find({ type: 'procurement_model' }).distinct(
    'procurement_model'
  );
  return result;
};

const getSectorSpecificQuestions = async (sectorCode: string) => {
  const result = await SectorQuestionModel.find({ sector_code: sectorCode });
  return result;
};

const getSubSectorSpecificQuestions = async (subSector: string) => {
  const result = await SubSectorQuestionModel.find({
    sub_sector: subSector,
  });
  return result;
};

const getProjectDetails = async (projectId: string) => {
  const result = await ProjectModel.findOne(
    { projectid: projectId },
    { projectid: 1, projectname: 1 }
  );
  return result;
};

const getSuggestedProjectIds = async (like: string) => {
  const result = await ProjectModel.find(
    {
      $or: [{ projectid: { $regex: like, $options: 'i' } }],
    },
    {
      projectid: 1,
      _id: 0,
    }
  )
    .limit(10)
    .then((items) => items.map((item) => item.projectid));
  return result;
};

const getSuggestedInputProjectsIds = async (like: string) => {
  // Use distinct to get unique project codes that match the search pattern
  const allResults = await ProjectInputDataModel.distinct('project_code', {
    project_code: { $regex: like, $options: 'i' },
  });

  // Limit the results to 10
  const result = allResults.slice(0, 10);

  return result;
};

/**
 * Get all unique sectors from the ProjectInputData collection
 * @returns Array of unique sector names
 */
const getAvailableSectors = async () => {
  // Use distinct to get unique sector values
  const sectors = await ProjectInputDataModel.distinct('Sector');

  // Sort alphabetically
  return sectors.sort();
};

export {
  getAllSectors,
  getSubSectorsForSector,
  getConstructionCostSources,
  getLevelOfEstimate,
  getLandTypes,
  getProcurementModels,
  getSectorSpecificQuestions,
  getSubSectorSpecificQuestions,
  getProjectDetails,
  getSuggestedProjectIds,
  getSuggestedInputProjectsIds,
  getAvailableSectors,
};
