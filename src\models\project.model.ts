// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { getModelForClass, modelOptions, prop } from "@typegoose/typegoose";

@modelOptions({
	schemaOptions: {
		collection: "projects",
		timestamps: true,
	},
	options: {
		allowMixed: 0, // 0 = allow mixed types
	},
})
class Project {
	@prop({ required: true, type: () => String })
	public projectid!: string;

	@prop({ required: true, type: () => String })
	public company!: string;

	@prop({ type: () => String })
	public profitcentre?: string;

	@prop({ type: () => String })
	public location?: string;

	@prop({ required: true, type: () => String })
	public projectname!: string;

	@prop({ type: () => String })
	public projectdirector?: string;

	@prop({ type: () => String })
	public projectmanager?: string;

	@prop({ type: () => Number })
	public estconsfees?: number;

	@prop({ type: () => Number, default: 0 })
	public totalrevenues!: number;

	@prop({ type: () => String })
	public customername?: string;

	@prop({ type: () => String })
	public contactperson?: string;

	@prop({ type: () => String })
	public servicename?: string;

	@prop({ type: () => String })
	public primarymarket?: string;

	@prop({ type: () => String })
	public clienttype?: string;

	@prop({ type: () => String })
	public projecttype?: string;

	@prop({ type: () => String })
	public activedate?: string;

	@prop({ type: () => Number })
	public estprojectvalue?: number;

	@prop({ type: () => String })
	public salescurrency?: string;

	@prop({ type: () => String })
	public region?: string;

	@prop({ type: () => String, default: null })
	public startdate?: string | null;

	@prop({ type: () => String, default: null })
	public enddate?: string | null;

	@prop({ type: () => Object, default: null })
	public latlng?: { type: string; coordinates: number[] } | null;

	@prop({ type: () => String, default: null })
	public projectaddress?: string | null;

	@prop({ type: () => String, default: null })
	public jobSheetLink?: string | null;
}

const ProjectModel = getModelForClass(Project);
export { ProjectModel, Project };
