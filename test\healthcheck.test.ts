import { describe, it, expect, beforeAll } from 'vitest';
import request from 'supertest';
import { Application } from 'express';
import app from '../src/infrastructure/app';
import logger from '../src/utils/logging';
import config from '@/config/config';

describe('API Tests', () => {
  beforeAll(async () => {
    // await setupTestData();
  });

  describe('Health Check', () => {
    it('should return 200 OK', async () => {
      const response = await request(app).get('/api/health').expect(200);

      expect(response.body).toHaveProperty('status', 'ok');
    });
  });
});
