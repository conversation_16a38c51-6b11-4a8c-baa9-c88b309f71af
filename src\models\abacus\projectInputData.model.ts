// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { prop, getModelForClass, modelOptions, index } from "@typegoose/typegoose";
import { v4 as uuidv4 } from "uuid";

@index({ project_code: 1, "Level of Estimate": 1 })
@modelOptions({
	schemaOptions: {
		timestamps: true,
		collection: "project_input_data",
	},
	options: {
		allowMixed: 0,
	},
})
export class ProjectInputData {
	@prop({ required: true, type: () => String, default: () => uuidv4() })
	public entry_id?: string; // Make optional in TypeScript but required in MongoDB with default

	@prop({ required: true, type: () => String })
	public project_code!: string;

	@prop({ required: true, type: () => String })
	public ["Project Code"]!: string;

	@prop({ required: true, type: () => String })
	public ["Project Name"]!: string;

	@prop({ required: true, type: () => String })
	public ["Brief Project Description"]!: string;

	@prop({ required: true, type: () => String })
	public Sector!: string;

	@prop({ required: false, type: () => String })
	public ["Sub-Sector (Leave Blank if TBA)"]!: string;

	@prop({ required: true, type: () => Number })
	public ["Construction Cost"]!: number;

	@prop({ required: true, type: () => String })
	public ["Source of Construction Cost"]!: string;

	@prop({ required: true, type: () => String })
	public ["Level of Estimate"]!: string;

	@prop({ required: false, type: () => Number })
	public ["Year of Head Contract Execution (Leave Blank if not executed)"]!: number;

	@prop({ required: true, type: () => String })
	public ["Procurement Model"]!: string;

	@prop({ required: false, type: () => String })
	public ["Land Type"]!: string;

	@prop({ required: false, type: () => Number })
	public ["Site area (m2)"]!: number;

	@prop({ required: false, type: () => Number })
	public ["Fully Enclosed Covered Area (FECA)"]!: number;

	@prop({ required: false, type: () => Number })
	public ["Unenclosed Covered Area (UCA)"]!: number;

	@prop({ type: () => Object, required: false })
	public uploadEstimate?: {
		type: string;
		value: string | Record<string, unknown>;
	};

	@prop({ type: () => Object, required: false })
	public uploadOriginalEstimate?: {
		type: string;
		value: string | Record<string, unknown>;
	};

	@prop({ type: () => [Object], required: false })
	public attachments?: Array<{
		type: string;
		value: string;
		fileName: string;
		fileType?: string;
		fileSize?: number;
	}>;
}

const ProjectInputDataModel = getModelForClass(ProjectInputData);

export default ProjectInputDataModel;
