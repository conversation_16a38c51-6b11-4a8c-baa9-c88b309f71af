import { BlobSASPermissions, BlobServiceClient } from "@azure/storage-blob";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { AzureBlobService } from "../azureBlob.service";

// Define permission types explicitly
interface SasPermissions {
	read: boolean;
	write?: boolean;
	create?: boolean;
}

// Create a mock SAS permissions object
const mockSasPermissionsObject: SasPermissions = { read: true };
const mockSasPermissionsResult = { toString: () => "r" };

// Mocking Azure Storage SDK
vi.mock("@azure/storage-blob", () => ({
	// Mock BlobServiceClient as a class/constructor function
	BlobServiceClient: vi.fn().mockImplementation(() => ({
		getContainerClient: vi.fn().mockReturnValue({
			getBlockBlobClient: vi.fn().mockReturnValue({
				uploadData: vi.fn().mockResolvedValue({}),
				url: "https://test-storage.blob.core.windows.net/test-container/test-file.txt",
				generateSasUrl: vi
					.fn()
					.mockResolvedValue(
						"https://test-storage.blob.core.windows.net/test-container/test-file.txt?sv=2020-08-04&st=2023-01-01T00%3A00%3A00Z&se=2023-01-02T00%3A00%3A00Z&sr=b&sp=r&sig=XXXXX",
					),
			}),
		}),
	})),
	BlobSASPermissions: {
		from: vi.fn().mockImplementation((permissions: SasPermissions) => {
			Object.assign(mockSasPermissionsObject, permissions);
			return mockSasPermissionsResult;
		}),
	},
}));

// Define interfaces for our mocks with proper vitest types
interface MockBlockBlobClient {
	uploadData: ReturnType<typeof vi.fn>;
	url: string;
	generateSasUrl: ReturnType<typeof vi.fn>;
}

interface MockContainerClient {
	getBlockBlobClient: ReturnType<typeof vi.fn>;
}

interface MockBlobServiceClient {
	getContainerClient: ReturnType<typeof vi.fn>;
}

describe("AzureBlobService", () => {
	let azureBlobService: AzureBlobService;
	let mockBlobServiceClientInstance: MockBlobServiceClient;
	// We don't need mockContainerClient and mockBlockBlobClient as separate variables here
	// if BlobServiceClient mock directly returns the nested structure.

	const containerName = "test-container";

	beforeEach(() => {
		// Reset mocks before each test if they are module-level like vi.mock
		vi.clearAllMocks();

		// The BlobServiceClient constructor itself is mocked by vi.mock above.
		// Calling it will execute the mockImplementation.
		// Provide arguments that satisfy one of the original constructor's signatures.
		const bscInstance = new BlobServiceClient(
			"connection-string",
			undefined, // The credential parameter is optional in the original constructor
		);

		// The bscInstance is the object returned by the mockImplementation.
		// Assert it to our mock interface using 'as unknown as' for type safety.
		mockBlobServiceClientInstance =
			bscInstance as unknown as MockBlobServiceClient;

		azureBlobService = new AzureBlobService(
			// Pass the mock, cast to the SDK type that AzureBlobService expects.
			mockBlobServiceClientInstance as unknown as BlobServiceClient,
			containerName,
		);
	});

	// No afterEach needed to reset vi.mock mocks if vi.clearAllMocks() is in beforeEach

	describe("uploadFile", () => {
		it("should upload a file to Azure Blob Storage", async () => {
			const buffer = Buffer.from("test content");
			const originalName = "test.txt";
			const mimeType = "text/plain";

			const result = await azureBlobService.uploadFile(
				buffer,
				originalName,
				mimeType,
			);

			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();

			expect(
				mockBlobServiceClientInstance.getContainerClient,
			).toHaveBeenCalledWith(containerName);
			expect(containerClientMock.getBlockBlobClient).toHaveBeenCalledWith(
				expect.stringContaining(originalName),
			);
			expect(blockBlobClientMock.uploadData).toHaveBeenCalledWith(buffer, {
				blobHTTPHeaders: { blobContentType: mimeType },
			});
			expect(result.url).toEqual(blockBlobClientMock.url);
			expect(result.storageName).toEqual(expect.stringContaining(originalName));
		});
	});

	describe("generateSASToken", () => {
		it("should generate a SAS token for a blob", async () => {
			const blobName = "test-file.txt";
			const expectedSasUrl =
				"https://test-storage.blob.core.windows.net/test-container/test-file.txt?sv=2020-08-04&st=2023-01-01T00%3A00%3A00Z&se=2023-01-02T00%3A00%3A00Z&sr=b&sp=r&sig=XXXXX";

			const result = await azureBlobService.generateSASToken(blobName);

			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();

			expect(
				mockBlobServiceClientInstance.getContainerClient,
			).toHaveBeenCalledWith(containerName);
			expect(containerClientMock.getBlockBlobClient).toHaveBeenCalledWith(
				blobName,
			);
			expect(blockBlobClientMock.generateSasUrl).toHaveBeenCalled();

			// Check the arguments passed to BlobSASPermissions.from
			expect(vi.mocked(BlobSASPermissions.from).mock.calls[0][0]).toEqual({
				read: true,
			});
			expect(result).toBe(expectedSasUrl);
		});

		it("should throw an error if SAS token generation fails", async () => {
			const blobName = "test-file.txt";
			const mockError = new Error("SAS generation failed");

			// Get the deeply nested mock to change its behavior for this test
			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.generateSasUrl.mockRejectedValueOnce(mockError);

			await expect(azureBlobService.generateSASToken(blobName)).rejects.toThrow(
				mockError,
			);
		});
	});

	describe("generateUploadPresignedUrl", () => {
		it("should generate a presigned URL for uploading a file", async () => {
			const originalName = "test-upload.txt";
			const mimeType = "text/plain";
			const expiryMinutes = 30;

			const expectedPresignedUrl =
				"https://test-storage.blob.core.windows.net/test-container/test-file.txt?sv=2020-08-04&st=2023-01-01T00%3A00%3A00Z&se=2023-01-02T00%3A00%3A00Z&sr=b&sp=rcw&sig=XXXXX";

			// Mock the generateSasUrl to return the expected presigned URL
			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.generateSasUrl.mockResolvedValueOnce(
				expectedPresignedUrl,
			);

			const result = await azureBlobService.generateUploadPresignedUrl(
				originalName,
				mimeType,
				expiryMinutes,
			);

			expect(
				mockBlobServiceClientInstance.getContainerClient,
			).toHaveBeenCalledWith(containerName);
			expect(containerClientMock.getBlockBlobClient).toHaveBeenCalledWith(
				expect.stringContaining(originalName),
			);
			expect(blockBlobClientMock.generateSasUrl).toHaveBeenCalledWith({
				permissions: expect.any(Object),
				expiresOn: expect.any(Date),
			});

			// Check the permissions include read, write, and create
			expect(vi.mocked(BlobSASPermissions.from).mock.calls[0][0]).toEqual({
				read: true,
				write: true,
				create: true,
			});

			expect(result).toEqual({
				presignedUrl: expectedPresignedUrl,
				storageName: expect.stringContaining(originalName),
				blobUrl: blockBlobClientMock.url,
			});
		});

		it("should use default expiry time when not specified", async () => {
			const originalName = "test-upload.txt";
			const mimeType = "text/plain";

			const expectedPresignedUrl =
				"https://test-storage.blob.core.windows.net/test-container/test-file.txt?sv=2020-08-04&st=2023-01-01T00%3A00%3A00Z&se=2023-01-02T00%3A00%3A00Z&sr=b&sp=rcw&sig=XXXXX";

			// Mock the generateSasUrl to return the expected presigned URL
			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.generateSasUrl.mockResolvedValueOnce(
				expectedPresignedUrl,
			);

			const result = await azureBlobService.generateUploadPresignedUrl(
				originalName,
				mimeType,
			);

			expect(blockBlobClientMock.generateSasUrl).toHaveBeenCalledWith({
				permissions: expect.any(Object),
				expiresOn: expect.any(Date),
			});

			// Check that the expiry date is about 60 minutes from now (default)
			const callArgs = blockBlobClientMock.generateSasUrl.mock.calls[0][0];
			const expiryDate = callArgs.expiresOn;
			const expectedExpiryTime = Date.now() + 60 * 60 * 1000;
			const timeDifference = Math.abs(
				expiryDate.getTime() - expectedExpiryTime,
			);

			// Allow for some variation in timing (within 1 second)
			expect(timeDifference).toBeLessThan(1000);

			expect(result).toEqual({
				presignedUrl: expectedPresignedUrl,
				storageName: expect.stringContaining(originalName),
				blobUrl: blockBlobClientMock.url,
			});
		});

		it("should throw an error if presigned URL generation fails", async () => {
			const originalName = "test-upload.txt";
			const mimeType = "text/plain";
			const mockError = new Error("Presigned URL generation failed");

			// Mock the generateSasUrl to throw an error
			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.generateSasUrl.mockRejectedValueOnce(mockError);

			await expect(
				azureBlobService.generateUploadPresignedUrl(originalName, mimeType),
			).rejects.toThrow(mockError);
		});
	});

	describe("generateBatchUploadPresignedUrls", () => {
		it("should generate presigned URLs for multiple files", async () => {
			const files = [
				{ originalName: "file1.txt", mimeType: "text/plain" },
				{ originalName: "file2.pdf", mimeType: "application/pdf" },
			];
			const expiryMinutes = 45;

			const expectedPresignedUrls = [
				"https://test-storage.blob.core.windows.net/test-container/file1.txt?sv=2020-08-04&st=2023-01-01T00%3A00%3A00Z&se=2023-01-02T00%3A00%3A00Z&sr=b&sp=rcw&sig=XXXXX",
				"https://test-storage.blob.core.windows.net/test-container/file2.pdf?sv=2020-08-04&st=2023-01-01T00%3A00%3A00Z&se=2023-01-02T00%3A00%3A00Z&sr=b&sp=rcw&sig=YYYYY",
			];

			// Mock the generateSasUrl to return different URLs for each call
			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.generateSasUrl
				.mockResolvedValueOnce(expectedPresignedUrls[0])
				.mockResolvedValueOnce(expectedPresignedUrls[1]);

			const result = await azureBlobService.generateBatchUploadPresignedUrls(
				files,
				expiryMinutes,
			);

			expect(result).toHaveLength(2);

			// Check first file result
			expect(result[0]).toEqual({
				presignedUrl: expectedPresignedUrls[0],
				storageName: expect.stringContaining("file1.txt"),
				blobUrl: blockBlobClientMock.url,
				originalName: "file1.txt",
			});

			// Check second file result
			expect(result[1]).toEqual({
				presignedUrl: expectedPresignedUrls[1],
				storageName: expect.stringContaining("file2.pdf"),
				blobUrl: blockBlobClientMock.url,
				originalName: "file2.pdf",
			});

			// Verify the service was called for each file
			expect(blockBlobClientMock.generateSasUrl).toHaveBeenCalledTimes(2);
		});

		it("should use default expiry time when not specified for batch upload", async () => {
			const files = [{ originalName: "file1.txt", mimeType: "text/plain" }];

			const expectedPresignedUrl =
				"https://test-storage.blob.core.windows.net/test-container/file1.txt?sv=2020-08-04&st=2023-01-01T00%3A00%3A00Z&se=2023-01-02T00%3A00%3A00Z&sr=b&sp=rcw&sig=XXXXX";

			// Mock the generateSasUrl to return the expected presigned URL
			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.generateSasUrl.mockResolvedValueOnce(
				expectedPresignedUrl,
			);

			const result =
				await azureBlobService.generateBatchUploadPresignedUrls(files);

			expect(result).toHaveLength(1);
			expect(result[0]).toEqual({
				presignedUrl: expectedPresignedUrl,
				storageName: expect.stringContaining("file1.txt"),
				blobUrl: blockBlobClientMock.url,
				originalName: "file1.txt",
			});
		});

		it("should throw an error if batch presigned URL generation fails", async () => {
			const files = [{ originalName: "file1.txt", mimeType: "text/plain" }];
			const mockError = new Error("Batch presigned URL generation failed");

			// Mock the generateSasUrl to throw an error
			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.generateSasUrl.mockRejectedValueOnce(mockError);

			await expect(
				azureBlobService.generateBatchUploadPresignedUrls(files),
			).rejects.toThrow(mockError);
		});
	});

	describe("deleteFileByUrl", () => {
		it("should delete a file by URL successfully", async () => {
			const testUrl =
				"https://test-storage.blob.core.windows.net/test-container/uuid-test-file.txt";

			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.deleteIfExists = vi
				.fn()
				.mockResolvedValue({ succeeded: true });

			const result = await azureBlobService.deleteFileByUrl(testUrl);

			expect(result).toBe(true);
			expect(containerClientMock.getBlockBlobClient).toHaveBeenCalledWith(
				"uuid-test-file.txt",
			);
			expect(blockBlobClientMock.deleteIfExists).toHaveBeenCalled();
		});

		it("should return false when file doesn't exist", async () => {
			const testUrl =
				"https://test-storage.blob.core.windows.net/test-container/non-existent-file.txt";

			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.deleteIfExists = vi
				.fn()
				.mockResolvedValue({ succeeded: false });

			const result = await azureBlobService.deleteFileByUrl(testUrl);

			expect(result).toBe(false);
			expect(containerClientMock.getBlockBlobClient).toHaveBeenCalledWith(
				"non-existent-file.txt",
			);
			expect(blockBlobClientMock.deleteIfExists).toHaveBeenCalled();
		});

		it("should throw error for invalid URL", async () => {
			const invalidUrl = "invalid-url";

			await expect(
				azureBlobService.deleteFileByUrl(invalidUrl),
			).rejects.toThrow("Invalid blob URL: Unable to extract blob name");
		});

		it("should throw error when delete operation fails", async () => {
			const testUrl =
				"https://test-storage.blob.core.windows.net/test-container/test-file.txt";
			const mockError = new Error("Delete operation failed");

			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.deleteIfExists = vi.fn().mockRejectedValue(mockError);

			await expect(azureBlobService.deleteFileByUrl(testUrl)).rejects.toThrow(
				mockError,
			);
		});

		it("should handle URL-encoded blob names correctly", async () => {
			const testUrl =
				"https://test-storage.blob.core.windows.net/test-container/b76c3df0-97b7-4ffc-b74f-e27b3edb32c7-Screenshot%202025-05-27%20203835.png";

			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.deleteIfExists = vi
				.fn()
				.mockResolvedValue({ succeeded: true });

			const result = await azureBlobService.deleteFileByUrl(testUrl);

			expect(result).toBe(true);
			// Verify the blob name is properly URL-decoded (spaces instead of %20)
			expect(containerClientMock.getBlockBlobClient).toHaveBeenCalledWith(
				"b76c3df0-97b7-4ffc-b74f-e27b3edb32c7-Screenshot 2025-05-27 203835.png",
			);
			expect(blockBlobClientMock.deleteIfExists).toHaveBeenCalled();
		});
	});

	describe("deleteFileByStorageName", () => {
		it("should delete a file by storage name successfully", async () => {
			const storageName = "uuid-test-file.txt";

			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.deleteIfExists = vi
				.fn()
				.mockResolvedValue({ succeeded: true });

			const result =
				await azureBlobService.deleteFileByStorageName(storageName);

			expect(result).toBe(true);
			expect(containerClientMock.getBlockBlobClient).toHaveBeenCalledWith(
				storageName,
			);
			expect(blockBlobClientMock.deleteIfExists).toHaveBeenCalled();
		});

		it("should return false when file doesn't exist", async () => {
			const storageName = "non-existent-file.txt";

			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.deleteIfExists = vi
				.fn()
				.mockResolvedValue({ succeeded: false });

			const result =
				await azureBlobService.deleteFileByStorageName(storageName);

			expect(result).toBe(false);
			expect(containerClientMock.getBlockBlobClient).toHaveBeenCalledWith(
				storageName,
			);
			expect(blockBlobClientMock.deleteIfExists).toHaveBeenCalled();
		});

		it("should throw error when delete operation fails", async () => {
			const storageName = "test-file.txt";
			const mockError = new Error("Delete operation failed");

			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.deleteIfExists = vi.fn().mockRejectedValue(mockError);

			await expect(
				azureBlobService.deleteFileByStorageName(storageName),
			).rejects.toThrow(mockError);
		});
	});

	describe("deleteMultipleFiles", () => {
		it("should delete multiple files successfully", async () => {
			const urls = [
				"https://test-storage.blob.core.windows.net/test-container/file1.txt",
				"https://test-storage.blob.core.windows.net/test-container/file2.txt",
			];

			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.deleteIfExists = vi
				.fn()
				.mockResolvedValueOnce({ succeeded: true })
				.mockResolvedValueOnce({ succeeded: true });

			const result = await azureBlobService.deleteMultipleFiles(urls);

			expect(result).toHaveLength(2);
			expect(result[0]).toEqual({ url: urls[0], success: true });
			expect(result[1]).toEqual({ url: urls[1], success: true });
			expect(blockBlobClientMock.deleteIfExists).toHaveBeenCalledTimes(2);
		});

		it("should handle mixed success and failure results", async () => {
			const urls = [
				"https://test-storage.blob.core.windows.net/test-container/file1.txt",
				"https://test-storage.blob.core.windows.net/test-container/file2.txt",
			];

			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.deleteIfExists = vi
				.fn()
				.mockResolvedValueOnce({ succeeded: true })
				.mockResolvedValueOnce({ succeeded: false });

			const result = await azureBlobService.deleteMultipleFiles(urls);

			expect(result).toHaveLength(2);
			expect(result[0]).toEqual({ url: urls[0], success: true });
			expect(result[1]).toEqual({ url: urls[1], success: false });
		});

		it("should handle errors for individual files", async () => {
			const urls = [
				"https://test-storage.blob.core.windows.net/test-container/file1.txt",
				"invalid-url",
			];

			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.deleteIfExists = vi
				.fn()
				.mockResolvedValue({ succeeded: true });

			const result = await azureBlobService.deleteMultipleFiles(urls);

			expect(result).toHaveLength(2);
			expect(result[0]).toEqual({ url: urls[0], success: true });
			expect(result[1]).toEqual({
				url: urls[1],
				success: false,
				error: "Invalid blob URL: Unable to extract blob name",
			});
		});
	});

	describe("fileExists", () => {
		it("should return true when file exists", async () => {
			const testUrl =
				"https://test-storage.blob.core.windows.net/test-container/existing-file.txt";

			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.exists = vi.fn().mockResolvedValue(true);

			const result = await azureBlobService.fileExists(testUrl);

			expect(result).toBe(true);
			expect(containerClientMock.getBlockBlobClient).toHaveBeenCalledWith(
				"existing-file.txt",
			);
			expect(blockBlobClientMock.exists).toHaveBeenCalled();
		});

		it("should return false when file doesn't exist", async () => {
			const testUrl =
				"https://test-storage.blob.core.windows.net/test-container/non-existent-file.txt";

			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.exists = vi.fn().mockResolvedValue(false);

			const result = await azureBlobService.fileExists(testUrl);

			expect(result).toBe(false);
			expect(containerClientMock.getBlockBlobClient).toHaveBeenCalledWith(
				"non-existent-file.txt",
			);
			expect(blockBlobClientMock.exists).toHaveBeenCalled();
		});

		it("should return false for invalid URL", async () => {
			const invalidUrl = "invalid-url";

			const result = await azureBlobService.fileExists(invalidUrl);

			expect(result).toBe(false);
		});

		it("should return false when exists check fails", async () => {
			const testUrl =
				"https://test-storage.blob.core.windows.net/test-container/test-file.txt";
			const mockError = new Error("Exists check failed");

			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.exists = vi.fn().mockRejectedValue(mockError);

			const result = await azureBlobService.fileExists(testUrl);

			expect(result).toBe(false);
		});
	});

	describe("extractBlobNameFromUrl (private method testing via public methods)", () => {
		it("should extract blob name from valid Azure Blob Storage URL", async () => {
			const testUrl =
				"https://test-storage.blob.core.windows.net/test-container/folder/subfolder/file.txt";

			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.exists = vi.fn().mockResolvedValue(true);

			// Test through fileExists which uses extractBlobNameFromUrl
			await azureBlobService.fileExists(testUrl);

			// Verify the extracted blob name includes the full path
			expect(containerClientMock.getBlockBlobClient).toHaveBeenCalledWith(
				"folder/subfolder/file.txt",
			);
		});

		it("should handle URLs with query parameters", async () => {
			const testUrl =
				"https://test-storage.blob.core.windows.net/test-container/file.txt?sv=2020-08-04&sig=XXXXX";

			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.exists = vi.fn().mockResolvedValue(true);

			// Test through fileExists which uses extractBlobNameFromUrl
			await azureBlobService.fileExists(testUrl);

			// Verify the extracted blob name excludes query parameters
			expect(containerClientMock.getBlockBlobClient).toHaveBeenCalledWith(
				"file.txt",
			);
		});

		it("should handle URLs with URL-encoded characters in blob names", async () => {
			const testUrl =
				"https://test-storage.blob.core.windows.net/test-container/b76c3df0-97b7-4ffc-b74f-e27b3edb32c7-Screenshot%202025-05-27%20203835.png";

			const containerClientMock =
				mockBlobServiceClientInstance.getContainerClient();
			const blockBlobClientMock = containerClientMock.getBlockBlobClient();
			blockBlobClientMock.exists = vi.fn().mockResolvedValue(true);

			// Test through fileExists which uses extractBlobNameFromUrl
			await azureBlobService.fileExists(testUrl);

			// Verify the extracted blob name is properly URL-decoded (spaces instead of %20)
			expect(containerClientMock.getBlockBlobClient).toHaveBeenCalledWith(
				"b76c3df0-97b7-4ffc-b74f-e27b3edb32c7-Screenshot 2025-05-27 203835.png",
			);
		});

		it("should handle invalid URLs gracefully", async () => {
			const invalidUrl = "not-a-valid-url";

			const result = await azureBlobService.fileExists(invalidUrl);

			expect(result).toBe(false);
		});
	});
});
