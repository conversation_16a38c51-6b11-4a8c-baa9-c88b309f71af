import "reflect-metadata";

/**
 * This module defines the Permission model for managing access control in the system.
 * It uses Typegoose for type-safe MongoDB interactions and provides a schema for
 * storing and managing user permissions.
 */

/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */

// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { getModelForClass, modelOptions, prop } from "@typegoose/typegoose";

/**
 * Configuration for the Permission model
 * - Collection name: permissions
 * - Includes timestamps for document creation and updates
 */
@modelOptions({
	schemaOptions: {
		collection: "permissions",
		timestamps: true,
	},
})
/**
 * Class representing a permission in the system.
 * Each permission has a unique name and description defining what access it grants.
 *
 * @example
 * ```typescript
 * // Example permission
 * {
 *   name: "project.read",
 *   description: "Allows reading project details"
 * }
 * ```
 */
export class Permission {
	/**
	 * Unique identifier name for the permission
	 * @prop required - Permission must have a name
	 * @prop unique - No two permissions can have the same name
	 * @prop trim - Whitespace will be trimmed from the name
	 * @example "project.read", "methodology.write"
	 */
	@prop({ required: true, unique: true, trim: true, type: () => String })
	public name!: string;

	/**
	 * Human-readable description of what the permission allows
	 * @prop required - Permission must have a description
	 * @prop trim - Whitespace will be trimmed from the description
	 * @example "Allows reading project details"
	 */
	@prop({ required: true, trim: true, type: () => String })
	public description!: string;
}

// Create and export the Mongoose model for Permission
const PermissionModel = getModelForClass(Permission);
export default PermissionModel;
