import { Router } from 'express';
import multer from 'multer';
import {
  calculateBenchmarkForProject,
  createProject,
  filterProjectsController,
  getBenchmark,
  getProjectFiles,
} from '@/controllers/abacus/project/project.controller';
import {
  getAllSectorsController,
  getAllSubSectorsController,
  getConstructionCostSourcesController,
  getLandTypesController,
  getLevelOfEstimateController,
  getProcurementModelsController,
  getSectorSpecificQuestionsController,
  getSubSectorSpecificQuestionsController,
  getProjectDetailsController,
  getSuggestedProjectIdsController,
  getSuggestedInputProjectIdsController,
  getAvailableSectorsController,
} from '@/controllers/abacus/referenceList/referenceList.controller';

const router = Router();

// Configure multer
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
});

// Helper function to create dynamic field configuration for multer
const createMulterFieldsConfig = () => {
  // Start with the main estimate upload field
  const fields = [{ name: 'uploadEstimate', maxCount: 1 }];

  // Add fields for up to 10 attachments (can be adjusted as needed)
  for (let i = 0; i < 10; i++) {
    fields.push({ name: `attachment_${i}`, maxCount: 1 });
  }

  return fields;
};

router.post('/', upload.fields(createMulterFieldsConfig()), createProject);
router.post('/filtered', filterProjectsController);
router.post('/benchmark', getBenchmark);
router.post('/benchmark-for-project', calculateBenchmarkForProject);
router.get('/sectors', getAllSectorsController);
router.get('/sectors/available', getAvailableSectorsController);
router.get('/sectors/:sector/subsectors', getAllSubSectorsController);
router.get('/construction-cost-sources', getConstructionCostSourcesController);
router.get('/level-of-estimate', getLevelOfEstimateController);
router.get('/land-types', getLandTypesController);
router.get('/procurement-models', getProcurementModelsController);
router.get('/sector-specific-questions/:sectorCode', getSectorSpecificQuestionsController);
router.get('/sub-sector-specific-questions/:subSector', getSubSectorSpecificQuestionsController);
router.get('/project-details/:projectId', getProjectDetailsController);
router.get('/projectInputData/:projectId', getSuggestedInputProjectIdsController);
router.get('/:projectId', getSuggestedProjectIdsController);
router.get('/:projectId/files', getProjectFiles);

export default router;
