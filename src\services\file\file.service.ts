import type { AzureBlobService } from "./azureBlob.service";
import type { File } from "./file.model";
import type {
	BlobStreamResult,
	FileUploadInput,
	GrantAccessInput,
} from "./file.types";
import type { MongoFileRepository } from "./mongoFile.repository";

export class FileService {
	constructor(
		private fileRepo: MongoFileRepository,
		private azureBlobService: AzureBlobService,
	) {}

	async uploadFile({
		files,
		userId,
		metadata,
		isPrivate,
		sharedWith,
	}: FileUploadInput): Promise<File[]> {
		const results = await Promise.all(
			files.map(async (file) => {
				const { url, storageName } = await this.azureBlobService.uploadFile(
					file.buffer,
					file.originalname,
					file.mimetype,
				);
				const createdFile = await this.fileRepo.create({
					url,
					storageName,
					userId: userId,
					originalName: file.originalname,
					mimeType: file.mimetype,
					size: file.size,
					isPrivate: isPrivate || false,
					sharedWith: sharedWith || [],
					metadata: {
						...metadata,
					},
				});
				return createdFile;
			}),
		);
		return results;
	}

	async getUserFiles(userId: string): Promise<File[] | null> {
		const files = await this.fileRepo.findByUserId(userId);
		return files;
	}

	async getFilesByUserId(userId: string): Promise<File[] | null> {
		const files = await this.fileRepo.findByUserId(userId);
		return files;
	}

	async accessFile(fileName: string, userId: string): Promise<string> {
		const file = await this.fileRepo.findByStorageName(fileName);
		if (!file) {
			throw new Error("File not found");
		}
		if (userId === file.userId) {
			const sasToken = await this.azureBlobService.generateSASToken(fileName);
			return sasToken;
		}
		if (file.isPrivate && !file.sharedWith.includes(userId)) {
			throw new Error("File is private and not shared with user");
		}
		const sasToken = await this.azureBlobService.generateSASToken(fileName);
		return sasToken;
	}

	/**
	 * Stream blob content directly by container and blob name
	 * @param containerName - The container name
	 * @param blobName - The blob name
	 * @returns Promise with stream and metadata
	 */
	async streamBlob(
		containerName: string,
		blobName: string,
	): Promise<BlobStreamResult> {
		return await this.azureBlobService.downloadBlobStream(
			blobName,
			containerName,
		);
	}

	/**
	 * Stream blob content with access control validation
	 * @param containerName - The container name
	 * @param blobName - The blob name (should match storageName in database)
	 * @param userId - The requesting user ID
	 * @returns Promise with stream and metadata
	 */
	async streamBlobWithAccessControl(
		containerName: string,
		blobName: string,
		userId: string,
	): Promise<BlobStreamResult> {
		// Check if file exists in database and user has access
		const file = await this.fileRepo.findByStorageName(blobName);
		if (!file) {
			throw new Error("File not found");
		}

		// Check access permissions
		const hasAccess =
			file.userId === userId || // Owner access
			!file.isPrivate || // Public file
			file.sharedWith.includes(userId); // Shared with user

		if (!hasAccess) {
			throw new Error(
				"Access denied: File is private and not shared with user",
			);
		}

		return await this.azureBlobService.downloadBlobStream(
			blobName,
			containerName,
		);
	}

	async grantAccessToFile({
		fileName,
		userId,
		ownerId,
	}: GrantAccessInput): Promise<string> {
		const file = await this.fileRepo.findByStorageName(fileName);
		if (!file) {
			throw new Error("File not found");
		}
		if (file.userId !== ownerId) {
			throw new Error("File is not owned by the user");
		}
		const result = await this.fileRepo.updateByUserId(ownerId, {
			sharedWith: [...file.sharedWith, userId],
		});
		if (!result) {
			throw new Error("Failed to update file");
		}
		const sasToken = await this.azureBlobService.generateSASToken(fileName);
		return sasToken;
	}
}
