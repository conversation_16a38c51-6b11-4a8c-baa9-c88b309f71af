export interface BlobStreamResult {
	stream: NodeJS.ReadableStream;
	contentType?: string;
	contentLength?: number;
	lastModified?: Date;
}

export interface FileUploadInput {
	files: Express.Multer.File[];
	userId: string;
	metadata?: Record<string, unknown>;
	isPrivate?: boolean;
	sharedWith?: string[];
}

export interface GrantAccessInput {
	fileName: string;
	userId: string;
	ownerId: string;
}

export interface FileStreamParams {
	containerName: string;
	blob: string;
}

export interface RangeRequest {
	start: number;
	end: number;
	contentLength: number;
}
