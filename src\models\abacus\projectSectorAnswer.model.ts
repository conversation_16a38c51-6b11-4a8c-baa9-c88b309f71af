// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { prop, getModelForClass, modelOptions } from '@typegoose/typegoose';

@modelOptions({
  schemaOptions: {
    timestamps: true,
    collection: 'project_sector_answers',
  },
  options: {
    allowMixed: 0,
  },
})
export class ProjectSectorAnswer {
  @prop({ required: true, type: () => String })
  public project_code!: string;

  @prop({ required: false, type: () => String })
  public question!: string;

  @prop({ required: false, type: () => String })
  public answer!: string;
}

const ProjectSectorAnswerModel = getModelForClass(ProjectSectorAnswer);

export default ProjectSectorAnswerModel;
