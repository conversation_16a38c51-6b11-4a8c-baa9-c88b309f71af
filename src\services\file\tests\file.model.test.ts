/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { FileModel, type File as FileSchemaInterface } from "../file.model";
import { describe, it, expect, afterEach } from "vitest";
import mongoose, { type Document, type Model } from "mongoose";

/**
 * Represents the structure of a File document once it's retrieved from MongoDB.
 * It combines your base schema interface with Mongoose Document properties and timestamps.
 * Ensure FileSchemaInterface includes optional createdAt and updatedAt if you expect them from timestamps.
 */
interface HydratedFileDocument extends FileSchemaInterface, Document {
	// If FileSchemaInterface has { timestamps: true } and includes createdAt?, updatedAt?,
	// these will be correctly typed. Otherwise, you might need to assert them more carefully.
	// For safety, we can explicitly list them, assuming they will exist on hydrated documents.
	createdAt: Date;
	updatedAt: Date;
	// This MUST match the definition in FileSchemaInterface for deletedAt
	deletedAt?: Date;
}

// Helper to correctly type the Mongoose model if using Typegoose or complex setups.
const TypedFileModel = FileModel as Model<FileSchemaInterface>;

describe("File Model Integration Tests", () => {
	const defaultUserId = new mongoose.Types.ObjectId().toString();

	const getBaseFileData = (overrides: Partial<FileSchemaInterface> = {}) => ({
		url: "https://test.com/test.txt",
		storageName: `test-storage-${new mongoose.Types.ObjectId().toString()}.txt`,
		userId: defaultUserId,
		originalName: "test.txt",
		mimeType: "text/plain",
		size: 100,
		...overrides,
	});

	afterEach(async () => {
		await TypedFileModel.deleteMany({});
	});

	it("should create and save a valid file document", async () => {
		const fileData = getBaseFileData({
			isPrivate: false,
			sharedWith: [new mongoose.Types.ObjectId().toString()],
			metadata: { customKey: "customValue" },
		});

		const file = new TypedFileModel(fileData);
		const savedFile = await file.save();
		// Using toObject() can simplify assertions by providing a plain JS object.
		// The cast to HydratedFileDocument assumes toObject() will include necessary fields (like timestamps).
		const fileToTest = savedFile.toObject<HydratedFileDocument>();

		expect(fileToTest._id).toBeDefined();
		expect(fileToTest.url).toBe(fileData.url);
		expect(fileToTest.storageName).toBe(fileData.storageName);
		expect(fileToTest.userId).toBe(fileData.userId);
		expect(fileToTest.isPrivate).toBe(fileData.isPrivate);
		expect(fileToTest.sharedWith).toEqual(
			expect.arrayContaining(fileData.sharedWith as string[]),
		);
		expect(fileToTest.metadata).toBeDefined();
		// Ensure correct casting for nested unknown/any types if metadata is loosely typed
		expect((fileToTest.metadata as { customKey: string }).customKey).toBe(
			"customValue",
		);
		expect(fileToTest.createdAt).toBeInstanceOf(Date);
		expect(fileToTest.updatedAt).toBeInstanceOf(Date);
		expect(fileToTest.deletedAt).toBeUndefined();
	});

	async function expectValidationError(
		invalidData: Partial<FileSchemaInterface>,
		fieldName: keyof FileSchemaInterface,
	) {
		let error: unknown;
		try {
			const file = new TypedFileModel(invalidData);
			await file.save();
		} catch (e) {
			error = e;
		}
		expect(
			error,
			`Test for field '${String(fieldName)}': Mongoose ValidationError was expected but not thrown. Check schema for 'required:true'.`,
		).toBeInstanceOf(mongoose.Error.ValidationError);
		if (error instanceof mongoose.Error.ValidationError) {
			expect(
				error.errors[fieldName as string],
				`Test for field '${String(fieldName)}': Expected error details for this field.`,
			).toBeDefined();
		}
	}

	it("should require the url field", async () => {
		const { url, ...invalidData } = getBaseFileData(); // Create data missing the 'url'
		await expectValidationError(invalidData, "url");
	});

	it("should require the storageName field", async () => {
		const { storageName, ...invalidData } = getBaseFileData();
		await expectValidationError(invalidData, "storageName");
	});

	it("should require the userId field", async () => {
		const { userId, ...invalidData } = getBaseFileData();
		await expectValidationError(invalidData, "userId");
	});

	it("should set default values correctly", async () => {
		const minimalData = getBaseFileData(); // Provides only required fields, relying on schema for defaults
		const file = new TypedFileModel(minimalData);
		const savedFile = await file.save();
		const fileToTest = savedFile.toObject<HydratedFileDocument>();

		expect(fileToTest.isPrivate).toBe(false); // Assumes schema default is false
		expect(fileToTest.sharedWith).toEqual([]); // Assumes schema default is an empty array
	});

	it("should set timestamps automatically", async () => {
		const fileData = getBaseFileData({
			metadata: {
				customKey: "customValue",
			},
		});
		const file = new TypedFileModel(fileData);
		const savedFile = await file.save();
		const fileToTest = savedFile.toObject<HydratedFileDocument>();

		expect(fileToTest.createdAt).toBeInstanceOf(Date);
		expect(fileToTest.updatedAt).toBeInstanceOf(Date);
		expect(fileToTest.updatedAt.getTime()).toBeGreaterThanOrEqual(
			fileToTest.createdAt.getTime(),
		);
	});
});
