#!/usr/bin/env node

import { connectMongo } from "@/infrastructure/database/mongo.connection";
import config from "@/config/config";
import logger from "@/utils/logging";
import {
	displaySeedStats,
	validateSeedData,
	cleanupSeedData,
	exportSeedData,
} from "./utils";
import { runAllSeeders } from "./index";
import { seedOptions } from "./config";

/**
 * CLI utility for managing seeded data
 *
 * Usage:
 *   yarn seed:stats      - Display statistics about seeded data
 *   yarn seed:validate   - Validate data integrity
 *   yarn seed:export     - Export seeded data to JSON
 *   yarn seed:cleanup    - Delete all seeded data (DANGEROUS!)
 *   yarn seed:dry-run    - Preview what would happen without making changes
 */

async function connectToDatabase(): Promise<void> {
	logger.info("📦 Connecting to MongoDB...");
	await connectMongo(config.database.mongo.uri);
	logger.info("✅ Connected to MongoDB");
	logger.info("");
}

async function showStats(): Promise<void> {
	await connectToDatabase();
	await displaySeedStats();
}

async function validateData(): Promise<void> {
	await connectToDatabase();
	const result = await validateSeedData();

	if (!result.isValid) {
		process.exit(1);
	}
}

async function runDryRun(): Promise<void> {
	await connectToDatabase();

	// Temporarily enable dry run mode
	const originalDryRun = seedOptions.dryRun;
	seedOptions.dryRun = true;

	try {
		logger.info("🔍 Running seed preview (DRY RUN MODE)");
		logger.info("=".repeat(50));
		await runAllSeeders();
	} finally {
		// Restore original setting
		seedOptions.dryRun = originalDryRun;
	}
}

async function exportData(): Promise<void> {
	await connectToDatabase();
	const data = await exportSeedData();

	// Write to file
	const fs = await import("node:fs/promises");
	const outputFile = `seed-export-${new Date().toISOString().slice(0, 10)}.json`;

	await fs.writeFile(outputFile, JSON.stringify(data, null, 2));
	logger.info(`📁 Exported seed data to: ${outputFile}`);
}

async function cleanup(): Promise<void> {
	await connectToDatabase();

	// Safety confirmation
	logger.warn("⚠️  This will permanently delete ALL seeded data!");
	logger.warn("⚠️  Type 'DELETE' to confirm, or press Ctrl+C to cancel:");

	// In a real CLI, you'd want to use readline for input
	// For now, we'll just proceed if NODE_ENV allows it
	if (process.env.FORCE_CLEANUP !== "true") {
		logger.error(
			"❌ Cleanup cancelled for safety. Set FORCE_CLEANUP=true to override.",
		);
		process.exit(1);
	}

	await cleanupSeedData();
}

async function showHelp(): Promise<void> {
	logger.info("🌱 Einstein Seed CLI Utilities");
	logger.info("=".repeat(40));
	logger.info("");
	logger.info("Available commands:");
	logger.info("  stats      Display statistics about seeded data");
	logger.info("  validate   Validate data integrity");
	logger.info("  export     Export seeded data to JSON file");
	logger.info("  cleanup    Delete all seeded data (DANGEROUS!)");
	logger.info(
		"  dry-run    Preview what seeding would do without making changes",
	);
	logger.info("  help       Show this help message");
	logger.info("");
	logger.info("Usage:");
	logger.info("  yarn seed:stats");
	logger.info("  yarn seed:validate");
	logger.info("  yarn seed:export");
	logger.info("  yarn seed:cleanup");
	logger.info("  yarn seed:dry-run");
	logger.info("");
	logger.info("Examples:");
	logger.info("  # Check what's in the database");
	logger.info("  yarn seed:stats");
	logger.info("");
	logger.info("  # Validate all relationships are correct");
	logger.info("  yarn seed:validate");
	logger.info("");
	logger.info("  # Preview what seeding would do");
	logger.info("  yarn seed:dry-run");
	logger.info("");
	logger.info("  # Export current data to JSON file");
	logger.info("  yarn seed:export");
	logger.info("");
	logger.info("  # Clean up all data (use with extreme caution!)");
	logger.info("  FORCE_CLEANUP=true yarn seed:cleanup");
	logger.info("");
}

async function main(): Promise<void> {
	const command = process.argv[2] || "help";

	try {
		switch (command) {
			case "stats":
				await showStats();
				break;
			case "validate":
				await validateData();
				break;
			case "export":
				await exportData();
				break;
			case "cleanup":
				await cleanup();
				break;
			case "dry-run":
				await runDryRun();
				break;
			case "help":
			case "--help":
			case "-h":
				await showHelp();
				break;
			default:
				logger.error(`❌ Unknown command: ${command}`);
				logger.info("Run 'yarn seed:help' to see available commands");
				process.exit(1);
		}

		process.exit(0);
	} catch (error) {
		logger.error("💥 CLI error:", error);
		process.exit(1);
	}
}

// Run if this file is executed directly
if (require.main === module) {
	main();
}
