import type { IProductivityRepository } from "@/services/repositories/productivity.repository";
import type { ProductivityDocument } from "@/models/productivity/productivity.model";

export interface ProductivityFilter {
	phase?: string;
	stage?: string;
}

export interface IProductivityService {
	getByPhaseAndStage(
		filter: ProductivityFilter,
	): Promise<ProductivityDocument[]>;
	getPhases(): Promise<string[]>;
	getStagesByPhase(phase: string): Promise<string[]>;
	getCategoryTypesByPhaseAndStage(
		phase: string,
		stage: string,
	): Promise<string[]>;
	getDataByPhaseStageAndCategoryType(
		phase: string,
		stage: string,
		categoryType: string,
	): Promise<ProductivityDocument[]>;
}

export class ProductivityService implements IProductivityService {
	constructor(
		private readonly productivityRepository: IProductivityRepository,
	) {}

	async getByPhaseAndStage(
		filter: ProductivityFilter,
	): Promise<ProductivityDocument[]> {
		return this.productivityRepository.findByPhaseAndStage(filter);
	}

	async getPhases(): Promise<string[]> {
		return this.productivityRepository.getDistinctPhases();
	}

	async getStagesByPhase(phase: string): Promise<string[]> {
		return this.productivityRepository.getDistinctStagesByPhase(phase);
	}

	async getCategoryTypesByPhaseAndStage(
		phase: string,
		stage: string,
	): Promise<string[]> {
		return this.productivityRepository.getDistinctCategoryTypesByPhaseAndStage(
			phase,
			stage,
		);
	}

	async getDataByPhaseStageAndCategoryType(
		phase: string,
		stage: string,
		categoryType: string,
	): Promise<ProductivityDocument[]> {
		return this.productivityRepository.getDataByPhaseStageAndCategoryType(
			phase,
			stage,
			categoryType,
		);
	}
}
