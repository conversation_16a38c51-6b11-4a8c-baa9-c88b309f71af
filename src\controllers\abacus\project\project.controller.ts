import {
	calculateBenchmarkForAProject,
	createProjectInput,
	filterProjects,
	getBenchmarkData,
	getProjectFilesWithSasUrls,
	getUniqueProjectInputData,
} from "@/services/abacus/projectInput.service";
import type { Request, RequestHandler, Response } from "express";
import {
	benchmarkForProjectRequestSchema,
	benchmarkRequestSchema,
	createProjectInputSchema,
	filterProjectsSchema,
} from "./project.schema";
import ProjectSectorAnswerModel from "@/models/abacus/projectSectorAnswer.model";
import ProjectSubSectorAnswerModel from "@/models/abacus/projectSubSectorAnswer.model";
import z from "zod";
import { BlobServiceClient, type ContainerClient } from "@azure/storage-blob";
import config from "@/config/config";
import { v4 as uuidv4 } from "uuid";

interface FileData {
	type: "file" | "url";
	value: string;
	fileName: string | null;
	fileType: string | null;
	fileSize: number | null;
	isUrl: boolean;
}

// First, let's define proper interfaces for our metadata
interface FileMetadata {
	type: "file" | "url";
	value: string;
	fileName?: string;
	fileType?: string;
	fileSize?: number;
	isUrl: boolean;
}

interface RequestBody {
	formData: string;
	uploadEstimateMetadata?: string;
	attachmentCount?: string;
	[key: string]: string | undefined; // For dynamic attachment metadata fields
}

/**
 * Handles file upload to Azure Blob Storage
 */
async function handleFileUpload(
	file: Express.Multer.File,
	projectId: string,
	fileData: FileData,
	containerClient: ContainerClient,
): Promise<string> {
	// Sanitize the file name to avoid encoding issues
	const sanitizedFileName =
		fileData.fileName?.replace(/[%\s+]/g, "_") || "unnamed-file";

	const blobName = `projects/${projectId}/${uuidv4()}-${sanitizedFileName}`;
	const blockBlobClient = containerClient.getBlockBlobClient(blobName);

	console.log("Uploading file to blob storage:", {
		originalFileName: fileData.fileName,
		sanitizedFileName,
		blobName,
		blobUrl: blockBlobClient.url,
	});

	await blockBlobClient.uploadData(file.buffer, {
		blobHTTPHeaders: { blobContentType: fileData.fileType || file.mimetype },
	});

	return blockBlobClient.url;
}

/**
 * Create a New Project Input Data for Abacus
 * @param req - Request object
 * @param res - Response object
 */
const createProject = async (
	req: Request<unknown, unknown, RequestBody>,
	res: Response,
) => {
	try {
		const files = req.files as { [fieldname: string]: Express.Multer.File[] };

		// Parse the main form data with type assertion
		const formData = JSON.parse(req.body.formData) as z.infer<
			typeof createProjectInputSchema
		>;

		console.log("formData", formData);

		// Validate the input data first before processing files
		try {
			const validatedInputData = createProjectInputSchema.parse(formData);

			// Parse the file metadata with proper typing - uploadEstimate is required
			const uploadEstimateMetadata = req.body.uploadEstimateMetadata
				? (JSON.parse(req.body.uploadEstimateMetadata) as FileMetadata)
				: null;

			console.log("uploadEstimateMetadata", uploadEstimateMetadata);
			// Get attachment count
			const attachmentCount = req.body.attachmentCount
				? Number.parseInt(req.body.attachmentCount, 10)
				: 0;

			// Parse attachment metadata
			const attachmentMetadata: FileMetadata[] = [];
			for (let i = 0; i < attachmentCount; i++) {
				const metadataKey = `attachmentMetadata_${i}`;
				if (req.body[metadataKey]) {
					attachmentMetadata.push(
						JSON.parse(req.body[metadataKey] as string) as FileMetadata,
					);
				}
			}
			console.log("attachmentMetadata", attachmentMetadata);

			if (!config.azure.storageConnectionString) {
				throw new Error("Azure storage connection string is not configured");
			}

			const blobServiceClient = BlobServiceClient.fromConnectionString(
				config.azure.storageConnectionString,
			);
			const containerClient = blobServiceClient.getContainerClient("abacus");

			// Process uploadEstimate (required)
			let processedUploadEstimate: FileMetadata | null = null;
			if (uploadEstimateMetadata) {
				console.log("uploadEstimateMetadata", uploadEstimateMetadata);
				if (
					uploadEstimateMetadata.type === "file" &&
					files?.uploadEstimate?.[0]
				) {
					const file = files.uploadEstimate[0];
					const uploadedUrl = await handleFileUpload(
						file,
						validatedInputData.projectId,
						{
							type: "file",
							value: file.originalname,
							fileName: file.originalname,
							fileType: file.mimetype,
							fileSize: file.size,
							isUrl: false,
						},
						containerClient,
					);

					// Create a Blob from the buffer
					const blob = new Blob([file.buffer], { type: file.mimetype });

					const flaskServiceUrl =
						"https://costing-staging-service-bndjdth7dvb9efhj.australiasoutheast-01.azurewebsites.net/api/process-estimates-new";

					const formData = new FormData();
					formData.append("file", blob, file.originalname);
					formData.append("project_code", validatedInputData.projectId);
					formData.append("level", validatedInputData.levelOfEstimate);
					formData.append(
						"source",
						validatedInputData.sourceOfConstructionCost,
					);

					const response = await fetch(flaskServiceUrl, {
						method: "POST",
						headers: {
							"X-API-Key": process.env.X_API_KEY || "",
						},
						body: formData,
					});

					if (!response.ok) {
						res.status(400).json({ error: "Failed to process file" });
						return;
					}

					const result = await response.json();
					console.log("File processing result:", result);

					processedUploadEstimate = {
						type: "url",
						value: uploadedUrl,
						isUrl: true,
					};
				} else if (uploadEstimateMetadata.type === "url") {
					processedUploadEstimate = {
						type: "url",
						value: uploadEstimateMetadata.value,
						isUrl: true,
					};
				}
			}

			// Process attachments
			const processedAttachments: Array<{
				type: string;
				value: string;
				fileName: string;
				fileType?: string;
				fileSize?: number;
			}> = [];

			for (let i = 0; i < attachmentCount; i++) {
				const fileKey = `attachment_${i}`;
				const metadata = attachmentMetadata[i];

				if (metadata && metadata.type === "file" && files?.[fileKey]?.[0]) {
					const file = files[fileKey][0];
					const uploadedUrl = await handleFileUpload(
						file,
						validatedInputData.projectId,
						{
							type: "file",
							value: file.originalname,
							fileName: file.originalname,
							fileType: file.mimetype,
							fileSize: file.size,
							isUrl: false,
						},
						containerClient,
					);

					processedAttachments.push({
						type: "url",
						value: uploadedUrl,
						fileName: file.originalname,
						fileType: file.mimetype,
						fileSize: file.size,
					});
				} else if (metadata && metadata.type === "url") {
					processedAttachments.push({
						type: "url",
						value: metadata.value,
						fileName: metadata.fileName || `attachment-${i}`,
						fileType: metadata.fileType,
						fileSize: metadata.fileSize,
					});
				}
			}

			// Transform the processed upload data to match ProjectInputData expectations
			const transformedData = {
				project_code: validatedInputData.projectId,
				"Project Code": validatedInputData.projectId,
				"Project Name": validatedInputData.projectName,
				"Brief Project Description": validatedInputData.projectDescription,
				Sector: validatedInputData.sector,
				"Sub-Sector (Leave Blank if TBA)": validatedInputData?.subSector || "",
				"Construction Cost": Number(validatedInputData.siteArea || 0),
				"Source of Construction Cost":
					validatedInputData.sourceOfConstructionCost,
				"Level of Estimate": validatedInputData.levelOfEstimate,
				"Year of Head Contract Execution (Leave Blank if not executed)": Number(
					validatedInputData.yearOfHeadContractNUM || 0,
				),
				"Procurement Model":
					validatedInputData.procurementModel || "Not Specified",
				"Land Type": validatedInputData.landType || "Not Specified",
				"Site area (m2)": Number(validatedInputData.siteArea || 0),
				"Fully Enclosed Covered Area (FECA)": Number(
					validatedInputData.fullyEnclosedCoveredArea || 0,
				),
				"Unenclosed Covered Area (UCA)": Number(
					validatedInputData.unenclosedCoveredArea || 0,
				),
				uploadEstimate: processedUploadEstimate
					? {
							type: processedUploadEstimate.type,
							value: processedUploadEstimate.value,
						}
					: undefined,
				attachments:
					processedAttachments.length > 0 ? processedAttachments : undefined,
			};

			const result = await createProjectInput(transformedData);

			// Handle sector specific questions - delete existing and create new
			if (validatedInputData.sectorSpecificQuestions.length > 0) {
				// Delete existing answers for this project
				await ProjectSectorAnswerModel.deleteMany({
					project_code: validatedInputData.projectId,
				});

				// Create new answers
				await ProjectSectorAnswerModel.create(
					validatedInputData.sectorSpecificQuestions.map((q) => ({
						project_code: validatedInputData.projectId,
						question: q.question,
						answer: q.answer,
					})),
				);
			}

			// Handle sub-sector specific questions - delete existing and create new
			if (validatedInputData.subSectorSpecificQuestions.length > 0) {
				// Delete existing answers for this project
				await ProjectSubSectorAnswerModel.deleteMany({
					project_code: validatedInputData.projectId,
				});

				// Create new answers
				await ProjectSubSectorAnswerModel.create(
					validatedInputData.subSectorSpecificQuestions.map((q) => ({
						project_code: validatedInputData.projectId,
						question: q.question,
						answer: q.answer,
					})),
				);
			}

			// Since we're always creating a new entry, we don't need to check isNew
			res.status(200).json({
				message: "Project input entry created successfully",
				result,
				entry_id: result.entry_id,
			});
		} catch (validationError) {
			if (validationError instanceof z.ZodError) {
				res.status(400).json({
					error: "Validation error",
					details: validationError.errors.map((err) => err.message),
				});
			} else {
				throw validationError; // Re-throw for the outer catch block
			}
		}
	} catch (error) {
		console.error("Error creating/updating project:", error);
		res.status(500).json({ error: "Failed to create/update project input" });
	}
};

/**
 * Filter Projects based on various criteria
 * @param req - Request object
 * @param res - Response object
 */
const filterProjectsController = async (req: Request, res: Response) => {
	try {
		const filters = filterProjectsSchema.parse(req.body);
		const result = await filterProjects(filters);
		res
			.status(200)
			.json({ message: "Projects filtered successfully", ...result });
	} catch (error) {
		if (error instanceof z.ZodError) {
			res.status(400).json({
				error: "Validation error",
				details: error.errors.map((err) => err.message),
			});
		} else {
			console.error("Error filtering projects:", error);
			res.status(500).json({ error: "Failed to filter projects" });
		}
	}
};

/**
 * Get benchmark data for specified projects
 * @param req - Request object containing array of project IDs
 * @param res - Response object
 */
const getBenchmark = async (req: Request, res: Response) => {
	try {
		const { projects } = benchmarkRequestSchema.parse(req.body);
		const { benchmarks, codes } = await getBenchmarkData(projects);
		res.status(200).json({
			message: "Benchmark data retrieved successfully",
			data: {
				benchmarks,
				codes,
			},
		});
	} catch (error) {
		if (error instanceof z.ZodError) {
			res.status(400).json({
				error: "Validation error",
				details: error.errors.map((err) => err.message),
			});
		} else {
			console.error("Error getting benchmark data:", error);
			res.status(500).json({ error: "Failed to get benchmark data" });
		}
	}
};

/**
 * Calculate benchmark for a specific project
 * @param req - Request object containing project ID
 * @param res - Response object
 */
const calculateBenchmarkForProject = async (req: Request, res: Response) => {
	try {
		const { projectId } = benchmarkForProjectRequestSchema.parse(req.body);
		const benchmarkData = await calculateBenchmarkForAProject(projectId);
		res.status(200).json({
			message: "Benchmark data retrieved successfully",
			data: benchmarkData,
		});
	} catch (error) {
		if (error instanceof z.ZodError) {
			res.status(400).json({
				error: "Validation error",
				details: error.errors.map((err) => err.message),
			});
		} else {
			console.error("Error calculating benchmark:", error);
			res.status(500).json({ error: "Failed to calculate benchmark" });
		}
	}
};

export interface ProjectFiles {
	entry_id?: string;
	uploadEstimate?: {
		fileName: string;
		url: string;
	};
	attachments?: Array<{
		fileName: string;
		url: string;
	}>;
}

/**
 * Get project files with SAS URLs for access
 * @param req - Request object with projectId parameter
 * @param res - Response object
 */
const getProjectFiles: RequestHandler = async (req, res) => {
	try {
		const { projectId } = req.params;
		const { entryId } = req.query;

		if (!projectId) {
			res.status(400).json({ error: "Project ID is required" });
			return;
		}

		const files = await getProjectFilesWithSasUrls(
			projectId,
			entryId as string | undefined,
		);

		if (!files) {
			res.status(404).json({ error: "Project not found or has no files" });
			return;
		}

		// Log the files being returned for debugging
		console.log("Returning project files:", {
			hasUploadEstimate: !!files.uploadEstimate,
			attachmentsCount: files.attachments?.length || 0,
		});

		res.status(200).json({
			message: "Project files retrieved successfully",
			data: files,
		});
	} catch (error) {
		console.error("Error retrieving project files:", error);
		res.status(500).json({ error: "Failed to retrieve project files" });
	}
};

/**
 * Get unique project input data for a specific project ID
 * @param req - Request object with projectId parameter
 * @param res - Response object
 */
const getProjectInputData: RequestHandler = async (req, res) => {
	try {
		const { projectId } = req.params;

		if (!projectId) {
			res.status(400).json({ error: "Project ID is required" });
			return;
		}

		const projectData = await getUniqueProjectInputData(projectId);

		if (!projectData || projectData.length === 0) {
			res
				.status(404)
				.json({ error: "No project input data found for this project ID" });
			return;
		}

		res.status(200).json({
			message: "Project input data retrieved successfully",
			count: projectData.length,
			data: projectData,
		});
	} catch (error) {
		console.error("Error retrieving project input data:", error);
		res.status(500).json({ error: "Failed to retrieve project input data" });
	}
};

export {
	calculateBenchmarkForProject,
	createProject,
	filterProjectsController,
	getBenchmark,
	getProjectFiles,
	getProjectInputData,
};
