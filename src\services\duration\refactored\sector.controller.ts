import type { Request, Response, NextFunction } from "express";
import { DurationSectorsModel } from "./durationSectors.model";

export class SectorController {
	// Get all sectors
	public getAllSectors = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const sectors = await DurationSectorsModel.find({}).lean();
			res.json({
				success: true,
				data: sectors,
				total: sectors.length,
			});
		} catch (error) {
			next(error);
		}
	};

	// Get sectors by type
	public getSectorsByType = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const { type } = req.query;

			if (!type) {
				return res.status(400).json({
					success: false,
					error: "Type parameter is required",
				});
			}

			const sectors = await DurationSectorsModel.find({ sector: type }).lean();

			res.json({
				success: true,
				data: sectors,
				total: sectors.length,
			});
		} catch (error) {
			next(error);
		}
	};

	// Create a new sector entry
	public createSector = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const { sector, sub_sector } = req.body;

			// Validate required fields
			if (!sector || !sub_sector) {
				return res.status(400).json({
					success: false,
					error: "Both sector and sub_sector are required",
				});
			}

			// Check if the sector-sub_sector combination already exists
			const existingSector = await DurationSectorsModel.findOne({
				sector,
				sub_sector,
			});

			if (existingSector) {
				return res.status(409).json({
					success: false,
					error: "Sector with this sub_sector already exists",
				});
			}

			// Create new sector entry
			const newSector = new DurationSectorsModel({
				sector,
				sub_sector,
			});

			const savedSector = await newSector.save();

			res.status(201).json({
				success: true,
				data: savedSector,
				message: "Sector created successfully",
			});
		} catch (error) {
			next(error);
		}
	};
}
