// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { prop, getModelForClass, modelOptions } from '@typegoose/typegoose';

@modelOptions({
  schemaOptions: {
    collection: 'sector_specific_questions',
  },
})
class SectorQuestion {
  @prop({ required: true, type: () => String })
  public sector_code!: string;

  @prop({ required: true, type: () => String })
  public question!: string;
}

export const SectorQuestionModel = getModelForClass(SectorQuestion);
export default SectorQuestion;
