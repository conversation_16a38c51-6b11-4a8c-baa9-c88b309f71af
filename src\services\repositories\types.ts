/**
 * The interface for the paginated result from the database.
 * - data: The actual data in the page
 * - total: The total number of items in the database
 * - page: The current page number
 * - limit: The number of items per page
 * - totalPages: The total number of pages
 */
export interface PaginatedResult<T> {
	data: T[];
	total: number;
	page: number;
	limit: number;
	totalPages: number;
}

/**
 * The status of a project
 * - ACTIVE: The project is currently active
 * - PLANNED: The project is planned but not active yet
 * - COMPLETED: The project is completed
 */
export enum ProjectStatus {
	ACTIVE = "Active",
	PLANNED = "Planned",
	COMPLETED = "Completed",
}

/**
 * The filters for the projects
 * - company: The company that the project belongs to
 * - location: The location of the project
 * - servicename: The service name of the project
 * - primarymarket: The primary market of the project
 * - consfees: The construction fees of the project
 * - status: The status of the project
 * - projectIds: The project ids of the projects
 */
export interface ProjectFilters {
	company?: string;
	location?: string;
	servicename?: string;
	primarymarket?: string;
	consfees?: string;
	status?: ProjectStatus;
	projectIds?: string[];
}

/**
 * The mongo query condition interface
 * - estconsfees: The estimated construction fees
 * - activedate: The active date of the project
 * - enddate: The end date of the project
 */
export interface MongoQueryCondition {
	estconsfees?: { $lt?: number; $gte?: number };
	activedate?: null | { $ne: null };
	enddate?: { $gt?: Date; $lte?: Date };
}

/**
 * The mongo query interface
 * - projectid: The id of the project
 * - company: The company of the project
 * - location: The location of the project
 * - servicename: The service name of the project
 * - primarymarket: The primary market of the project
 * - $or: The or conditions of the query
 */
export interface MongoQuery {
	projectid?: { $in: string[] };
	company?: { $in: string[] };
	location?: { $in: string[] };
	servicename?: { $in: string[] };
	primarymarket?: { $in: string[] };
	$or?: MongoQueryCondition[];
}

/**
 * The filter mapping interface
 * - customername: The customer name field in the frontend
 * - projectname: The project name field in the frontend
 * - city: The city field in the frontend
 * - industry: The industry field in the frontend
 * - serviceOffering: The service offering field in the frontend
 * - plannedRevenue: The planned revenue field in the frontend
 * - status: The status field in the frontend
 * - gfa: The gfa field in the frontend
 */
export interface FilterMapping {
	customername: "customername";
	projectname: "projectname";
	projectid: "projectid";
	city: "location";
	industry: "primarymarket";
	serviceOffering: "servicename";
	plannedRevenue: "estconsfees";
	status: "status";
	gfa: "gfa";
	projectManager: "projectmanager";
	projectDirector: "projectdirector";
}

export const FILTER_MAPPING: FilterMapping = {
	customername: "customername",
	projectname: "projectname",
	city: "location",
	industry: "primarymarket",
	serviceOffering: "servicename",
	plannedRevenue: "estconsfees",
	status: "status",
	gfa: "gfa",
	projectid: "projectid",
	projectManager: "projectmanager",
	projectDirector: "projectdirector",
} as const;

export interface FrontendFieldMapping {
	customername: "customerName";
	projectname: "projectName";
	location: "city";
	primarymarket: "industry";
	servicename: "serviceOffering";
	estconsfees: "plannedRevenue";
	status: "status";
}

/**
 * The frontend mapping object
 * - customerName: The customer name field in the frontend
 * - projectName: The project name field in the frontend
 * - city: The city field in the frontend
 * - industry: The industry field in the frontend
 * - serviceOffering: The service offering field in the frontend
 * - plannedRevenue: The planned revenue field in the frontend
 * - status: The status field in the frontend
 */
export const FRONTEND_MAPPING: FrontendFieldMapping = {
	customername: "customerName",
	projectname: "projectName",
	location: "city",
	primarymarket: "industry",
	servicename: "serviceOffering",
	estconsfees: "plannedRevenue",
	status: "status",
} as const;

/**
 * Interface for the processed market share data
 * - label: The label of the market share
 * - value: The value of the market share
 * - color: The color of the market share
 * - percentage: The percentage of the market share
 */
export interface MarketShareData {
	label: string;
	value: number;
	color: string;
	percentage: number;
}

/**
 * Interface for the raw market share data
 * - primarymarket: The primary market of the project (e.g. 'Office')
 * - market_count: The number of projects in the market (e.g. 10)
 * - percentage: The percentage of the market share (e.g. 50)
 */
export interface MarketShareRawData {
	primarymarket: string;
	market_count: number;
	percentage: number;
}

/**
 * Interface for the phase timeline data
 * - phase: The phase of the project (e.g. design, planning, etc.)
 * - label: The label for the given phase (e.g. 'Design', 'Planning', etc.)
 * - color: The color for the given phase
 * - planned: An object containing the planned start and end dates and duration for the given phase
 * - actual: An object containing the actual start and end dates and duration for the given phase
 */
export interface PhaseTimelineData {
	phase: string;
	label: string;
	color: string;
	planned: {
		start: string;
		end: string;
		duration: number;
	};
	actual: {
		start: string;
		end: string;
		duration: number;
	};
}

/**
 * The interface for the phase cost data
 * - stage: The stage of the project (e.g. design, planning, etc.)
 * - planned: The planned cost for the given stage
 * - actual: The actual cost for the given stage
 * - label: The label for the given stage (e.g. 'Design', 'Planning', etc.)
 * - color: The color for the given stage
 * - metric: An optional metric for the given stage (e.g. GFA, duration, etc.)
 */
export interface CostStageData {
	stage: string;
	planned: number;
	actual: number;
	label: string;
	color: string;
	metric: string;
}

export const buildFilterQuery = (
	filters: Partial<Record<keyof FilterMapping, string[]>>,
	deselectedProjects?: Array<{ projectId: string; index: string }>,
): Record<string, unknown> => {
	const query: Record<string, unknown> = {};
	const andConditions: Array<Record<string, unknown>> = [];

	// Add filter conditions
	for (const [key, values] of Object.entries(filters)) {
		if (!values?.length) continue;

		const mongoField = FILTER_MAPPING[key as keyof FilterMapping];

		if (key === "plannedRevenue") {
			const revenueConditions: Record<string, unknown>[] = [];
			for (const range of values) {
				switch (range) {
					case "OVER_1M":
						revenueConditions.push({ [mongoField]: { $gte: 1000000 } });
						break;
					case "500K_1M":
						revenueConditions.push({
							[mongoField]: { $gte: 500000, $lt: 1000000 },
						});
						break;
					case "250K_500K":
						revenueConditions.push({
							[mongoField]: { $gte: 250000, $lt: 500000 },
						});
						break;
					case "UNDER_250K":
						revenueConditions.push({ [mongoField]: { $lt: 250000 } });
						break;
				}
			}
			if (revenueConditions.length > 0) {
				andConditions.push({ $or: revenueConditions });
			}
		} else if (key === "gfa") {
			// Handle GFA as a numeric range [low, high]
			if (Array.isArray(values) && values.length === 2) {
				const [lowValue, highValue] = values.map(Number);
				andConditions.push({
					gfa: {
						$gte: lowValue,
						$lte: highValue,
					},
				});
			}
		} else if (key === "status") {
			const now = new Date();
			for (const status of values) {
				switch (status) {
					case ProjectStatus.ACTIVE:
						andConditions.push({
							$or: [
								{ startdate: { $ne: null } },
								{ activedate: { $ne: null } },
							],
							enddate: { $gt: now },
						});
						break;
					case ProjectStatus.PLANNED:
						andConditions.push({
							startdate: null,
							activedate: null,
						});
						break;
					case ProjectStatus.COMPLETED:
						andConditions.push({
							$or: [
								{ startdate: { $ne: null } },
								{ activedate: { $ne: null } },
							],
							enddate: { $lte: now },
						});
						break;
				}
			}
		} else {
			andConditions.push({ [mongoField]: { $in: values } });
		}
	}

	// Add deselected projects condition
	if (deselectedProjects && deselectedProjects.length > 0) {
		const deselectedIds = deselectedProjects.map(
			(project) => project.projectId,
		);
		andConditions.push({
			projectid: { $nin: deselectedIds },
		});
	}

	// Add $and to query if we have any conditions
	if (andConditions.length > 0) {
		query.$and = andConditions;
	}

	return query;
};
