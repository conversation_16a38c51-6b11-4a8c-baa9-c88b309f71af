import {
	getModelForClass,
	modelOptions,
	prop,
	Severity,
} from "@typegoose/typegoose";

@modelOptions({
	schemaOptions: {
		timestamps: true,
	},
	options: {
		allowMixed: Severity.ALLOW,
	},
})
export class File {
	@prop({ type: String, required: true })
	public userId!: string;

	@prop({ type: String, required: true })
	public originalName!: string;

	@prop({ type: String, required: true })
	public storageName!: string;

	@prop({ type: Number, required: true })
	public size!: number;

	@prop({ type: String, required: true })
	public mimeType!: string;

	@prop({ type: String, required: true })
	public url!: string;

	@prop({ type: () => Object, default: {}, required: true })
	public metadata!: Record<string, unknown>;

	@prop({ type: Boolean, required: true, default: false })
	public isPrivate!: boolean;

	@prop({ type: [String], required: true, default: [] })
	public sharedWith!: string[];

	@prop({ type: Date, required: false })
	public deletedAt?: Date;
}

export const FileModel = getModelForClass(File);
