import { format } from 'winston';
import os from 'os';
const { combine, timestamp, printf, colorize, json } = format;

// Add error stack traces to logs
const errorFormat = format((info) => {
  if (info.error instanceof Error) {
    return {
      ...info,
      stack: info.error.stack,
      error: {
        message: info.error.message,
        name: info.error.name,
      },
    };
  }
  return info;
});

const maskSensitiveData = format((info) => {
  const masked = { ...info } as typeof info;

  // Function to recursively mask sensitive data in any value
  const maskValue = (value: any): any => {
    if (typeof value === 'string') {
      // Mask email addresses
      value = value.replace(
        /\b[\w._%+-]+@[\w.-]+\.[A-Z]{2,}\b/gi,
        '[EMAIL REDACTED]'
      );
      
      // Mask Azure Storage connection strings
      value = value.replace(
        /DefaultEndpointsProtocol=https;AccountName=[\w-]+;AccountKey=[A-Za-z0-9+/=]+;EndpointSuffix=[\w.]+/gi,
        'DefaultEndpointsProtocol=https;AccountName=[REDACTED];AccountKey=[REDACTED];EndpointSuffix=[REDACTED]'
      );
      
      // Mask MongoDB connection strings
      value = value.replace(
        /mongodb(?:\+srv)?:\/\/([^:]+):([^@]+)@([^/]+)/gi,
        'mongodb$1://[USERNAME]:[PASSWORD]@$3'
      );
      
      // Mask generic passwords in URLs
      value = value.replace(
        /:\/\/([^:]+):([^@]+)@/g,
        '://[USERNAME]:[PASSWORD]@'
      );
      
      // Mask API keys and tokens (common patterns)
      value = value.replace(
        /\b(api[_-]?key|apikey|api[_-]?token|token|secret|password|pwd|passwd|auth[_-]?token|access[_-]?token|client[_-]?secret)\s*[:=]\s*["']?([^"'\s,}]+)["']?/gi,
        '$1=[REDACTED]'
      );
      
      // Mask JWT tokens
      value = value.replace(
        /Bearer\s+[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+/g,
        'Bearer [JWT_REDACTED]'
      );
      
      // Mask base64 encoded secrets (AccountKey pattern)
      value = value.replace(
        /AccountKey=([A-Za-z0-9+/]{80,}={0,2})/g,
        'AccountKey=[REDACTED]'
      );
    } else if (typeof value === 'object' && value !== null) {
      // Recursively mask objects
      const maskedObj: any = Array.isArray(value) ? [] : {};
      for (const key in value) {
        // Skip certain sensitive keys entirely
        if (/password|secret|token|key|auth|credential/i.test(key)) {
          maskedObj[key] = '[REDACTED]';
        } else {
          maskedObj[key] = maskValue(value[key]);
        }
      }
      return maskedObj;
    }
    return value;
  };

  // Mask the main message
  if (masked.message) {
    masked.message = maskValue(masked.message);
  }

  // Mask all other properties
  for (const key in masked) {
    if (key !== 'message' && key !== 'level' && key !== 'timestamp') {
      masked[key] = maskValue(masked[key]);
    }
  }

  return masked;
});

const developmentFormat = combine(
  errorFormat(),
  maskSensitiveData(),
  colorize({ all: true }),
  timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  printf((info) => {
    const { timestamp, level, message, ...meta } = info;
    const metaString = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} ${level}: ${message} ${metaString}`;
  })
);

const productionFormat = combine(
  errorFormat(),
  maskSensitiveData(),
  timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  format((info) => ({
    ...info,
    environment: process.env.NODE_ENV,
    service: process.env.SERVICE_NAME || 'einstein-backend',
    hostname: os.hostname(),
    pid: process.pid,
  }))(),
  json()
);

export { developmentFormat, productionFormat };
