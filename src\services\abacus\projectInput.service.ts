/* eslint-disable @typescript-eslint/no-non-null-assertion */
import ProjectInputDataModel, { ProjectInputData } from '@/models/abacus/projectInputData.model';
import ProjectSectorAnswerModel from '@/models/abacus/projectSectorAnswer.model';
import ProjectSubSectorAnswerModel from '@/models/abacus/projectSubSectorAnswer.model';
import { prisma } from '@/infrastructure/server';
import { BlobServiceClient, BlobSASPermissions } from '@azure/storage-blob';
import config from '@/config/config';

interface FilterOptions {
  sector?: string;
  subSector?: string;
  location?: string;
  earliestYear?: string;
  sourceOfConstructionCost?: string;
  classEstimate?: string;
  projectCode?: string;
  entryId?: string;
  sectorSpecificAnswers?: Array<{
    _id: string;
    sector_code: string;
    question: string;
    answer: string;
  }>;
  subSectorSpecificAnswers?: Array<{
    _id: string;
    sub_sector: string;
    question: string;
    answer: string;
  }>;
}

interface ProjectQuery {
  Sector?: string;
  ['Sub-Sector (Leave Blank if TBA)']?: string;
  ['Source of Construction Cost']?: string;
  ['Level of Estimate']?: string;
  ['Year of Head Contract Execution (Leave Blank if not executed)']?: { $gte: number };
  project_code?: { $in: string[] } | { $regex: string; $options: string };
  entry_id?: string;
}

interface IcmsAmountSum {
  icms_code: string;
  total_amount: number;
  total_rate: number;
}

interface ProjectFile {
  fileName: string;
  url: string;
}

interface ProjectFiles {
  entry_id?: string;
  uploadEstimate?: ProjectFile;
  uploadOriginalEstimate?: ProjectFile;
  attachments?: ProjectFile[];
}

interface ProjectFileUpload {
  type: string;
  value: string;
}

interface ProjectAttachment {
  type: string;
  value: string;
  fileName: string;
  fileType?: string;
  fileSize?: number;
}

interface IProject extends ProjectInputData {
  entry_id?: string;
  uploadEstimate?: ProjectFileUpload;
  uploadOriginalEstimate?: ProjectFileUpload;
  attachments?: ProjectAttachment[];
}

interface MasterConsolidatedCode {
  master_code: string;
  master_description: string | null;
  master_grouping: string | null;
}

/**
 * Create or update a Project Input Data entry for Abacus
 * @param projectData - Data for creating or updating the project input
 * @returns The created or updated project
 */
const createProjectInput = async (projectData: ProjectInputData) => {
  // Upsert based on project_code and Level of Estimate combination
  const result = await ProjectInputDataModel.findOneAndUpdate(
    {
      project_code: projectData.project_code,
      "Level of Estimate": projectData["Level of Estimate"]
    },
    projectData,
    {
      new: true, // Return the updated document
      upsert: true, // Create if doesn't exist
      setDefaultsOnInsert: true // Apply default values (like entry_id) only on insert
    }
  );

  return result;
};

/**
 * Filter Projects based on various criteria
 * @param filters - Filter options for projects
 */
const filterProjects = async (filters: FilterOptions) => {
  const projectQuery: ProjectQuery = {};

  // Add direct project code filter
  if (filters.projectCode) {
    projectQuery.project_code = { $regex: filters.projectCode, $options: 'i' };
  }

  // Add entry_id filter if provided
  if (filters.entryId) {
    projectQuery.entry_id = filters.entryId;
  }

  // Apply basic filters
  if (filters.sector) {
    projectQuery.Sector = filters.sector;
  }
  if (filters.subSector) {
    projectQuery['Sub-Sector (Leave Blank if TBA)'] = filters.subSector;
  }
  if (filters.sourceOfConstructionCost) {
    projectQuery['Source of Construction Cost'] = filters.sourceOfConstructionCost;
  }
  if (filters.classEstimate) {
    projectQuery['Level of Estimate'] = filters.classEstimate;
  }
  if (filters.earliestYear && !isNaN(Number(filters.earliestYear))) {
    const year = Number(filters.earliestYear);
    if (year > 0) {
      projectQuery['Year of Head Contract Execution (Leave Blank if not executed)'] = {
        $gte: year,
      };
    }
  }

  // Get matching project codes from sector specific answers
  let sectorMatchingProjects: string[] = [];
  if (filters.sectorSpecificAnswers && filters.sectorSpecificAnswers.length > 0) {
    const sectorAnswers = await ProjectSectorAnswerModel.find({
      $or: filters.sectorSpecificAnswers.map((q) => ({
        question: q.question,
        answer: q.answer,
      })),
    }).distinct('project_code');
    sectorMatchingProjects = sectorAnswers;
  }

  // Get matching project codes from sub-sector specific answers
  let subSectorMatchingProjects: string[] = [];
  if (filters.subSectorSpecificAnswers && filters.subSectorSpecificAnswers.length > 0) {
    const subSectorAnswers = await ProjectSubSectorAnswerModel.find({
      $or: filters.subSectorSpecificAnswers.map((q) => ({
        question: q.question,
        answer: q.answer,
      })),
    }).distinct('project_code');
    subSectorMatchingProjects = subSectorAnswers;
  }

  // Combine all filters
  if (sectorMatchingProjects.length > 0) {
    if (!projectQuery.project_code) {
      projectQuery.project_code = { $in: sectorMatchingProjects };
    }
  }
  if (subSectorMatchingProjects.length > 0) {
    if (projectQuery.project_code && '$in' in projectQuery.project_code) {
      const filteredCodes = projectQuery.project_code.$in.filter((code) =>
        subSectorMatchingProjects.includes(code)
      );
      projectQuery.project_code = { $in: filteredCodes };
    } else if (!projectQuery.project_code) {
      projectQuery.project_code = { $in: subSectorMatchingProjects };
    }
  }

  // Get and return the filtered projects
  // Sort by createdAt in descending order to get newest entries first
  const projects = await ProjectInputDataModel.find(projectQuery).sort({ createdAt: -1 });

  return {
    count: projects.length,
    results: projects,
  };
};

/**
 * Get benchmark ICMS codes from project estimates
 * @param projects - Array of objects containing projectCode and level
 * @returns A record mapping project codes to their associated ICMS codes with amounts and rates
 */
const getBenchmarkData = async (
  projects: Array<{ projectCode: string; level: string }>
): Promise<{
  benchmarks: Record<
    string,
    Array<{ icms_code: string; total_amount: number; total_rate: number }>
  >;
  codes: MasterConsolidatedCode[];
}> => {
  // Build OR conditions for each project+level combination
  const whereConditions = projects.map(p => ({
    project_code: p.projectCode,
    level: p.level
  }));

  // Get estimates data
  const estimates = await prisma.estimate.groupBy({
    by: ['project_code', 'level', 'icms_code'],
    where: {
      OR: whereConditions,
      icms_code: {
        not: null,
      },
      amount: {
        not: null,
      },
    },
    _sum: {
      amount: true,
      rate: true, // Sum of rates
    },
  });

  // Get all master consolidated codes
  const masterCodes = await prisma.$queryRaw<MasterConsolidatedCode[]>`
    SELECT master_code, master_description, master_grouping
    FROM master_consolidated_codes
    ORDER BY master_code
  `;
  // Process estimates data - first collect all data
  const allData = estimates.reduce(
    (acc, curr) => {
      if (curr.project_code && curr.level && curr.icms_code && curr._sum.amount) {
        // Create a composite key with project_code and level
        const key = `${curr.project_code}|${curr.level}`;
        if (!acc[key]) {
          acc[key] = [];
        }

        acc[key].push({
          icms_code: curr.icms_code,
          total_amount: Number(curr._sum.amount),
          total_rate: Number(curr._sum.rate || 0), // Default to 0 if rate is null
        });
      }
      return acc;
    },
    {} as Record<string, Array<{ icms_code: string; total_amount: number; total_rate: number }>>
  );

  // Process the data to aggregate to L3 level and filter out other levels
  const benchmarks: Record<
    string,
    Array<{ icms_code: string; total_amount: number; total_rate: number }>
  > = {};

  for (const compositeKey in allData) {
    const projectItems = allData[compositeKey];

    // Create a map to aggregate L3 level codes
    const l3Aggregation = new Map<string, { total_amount: number; total_rate: number }>();

    // Process each item
    projectItems.forEach((item) => {
      const parts = item.icms_code.split('.');

      // Check if this is an L4 code (has 4 parts) and convert to L3
      if (parts.length === 4) {
        // Convert L4 to L3 by removing the last part
        const l3Code = parts.slice(0, 3).join('.');

        // Add to L3 aggregation
        if (!l3Aggregation.has(l3Code)) {
          l3Aggregation.set(l3Code, {
            total_amount: item.total_amount,
            total_rate: item.total_rate,
          });
        } else {
          const existing = l3Aggregation.get(l3Code)!;
          existing.total_amount += item.total_amount;
          existing.total_rate += item.total_rate;
        }
      }
      // If it's already an L3 code (has 3 parts), add it directly
      else if (parts.length === 3) {
        if (!l3Aggregation.has(item.icms_code)) {
          l3Aggregation.set(item.icms_code, {
            total_amount: item.total_amount,
            total_rate: item.total_rate,
          });
        } else {
          const existing = l3Aggregation.get(item.icms_code)!;
          existing.total_amount += item.total_amount;
          existing.total_rate += item.total_rate;
        }
      }
      // Ignore L1 and L2 codes
    });

    // Convert the map to array and sort
    benchmarks[compositeKey] = Array.from(l3Aggregation.entries())
      .map(([icms_code, data]) => ({
        icms_code,
        total_amount: data.total_amount,
        total_rate: data.total_rate,
      }))
      .sort((a, b) => a.icms_code.localeCompare(b.icms_code));
  }

  return {
    benchmarks,
    codes: masterCodes,
  };
};

const calculateBenchmarkForAProject = async (projectId: string): Promise<IcmsAmountSum[]> => {
  const estimates = await prisma.estimate.groupBy({
    by: ['icms_code'],
    where: {
      project_code: projectId,
      icms_code: {
        not: null,
      },
      amount: {
        not: null,
      },
    },
    _sum: {
      amount: true,
      rate: true, // Sum rates instead of averaging
    },
  });

  // Convert the raw data to our expected format
  const allResults = estimates
    .filter(
      (item): item is { icms_code: string; _sum: { amount: number; rate: number | null } } =>
        item.icms_code !== null && item._sum.amount !== null
    )
    .map((item) => ({
      icms_code: item.icms_code,
      total_amount: Number(item._sum.amount),
      total_rate: Number(item._sum.rate || 0), // Default to 0 if rate is null
    }));

  // Create a map to aggregate L3 level codes
  const l3Aggregation = new Map<string, { total_amount: number; total_rate: number }>();

  // Process each item
  allResults.forEach((item) => {
    const parts = item.icms_code.split('.');

    // Check if this is an L4 code (has 4 parts) and convert to L3
    if (parts.length === 4) {
      // Convert L4 to L3 by removing the last part
      const l3Code = parts.slice(0, 3).join('.');

      // Add to L3 aggregation
      if (!l3Aggregation.has(l3Code)) {
        l3Aggregation.set(l3Code, {
          total_amount: item.total_amount,
          total_rate: item.total_rate,
        });
      } else {
        const existing = l3Aggregation.get(l3Code)!;
        existing.total_amount += item.total_amount;
        existing.total_rate += item.total_rate;
      }
    }
    // If it's already an L3 code (has 3 parts), add it directly
    else if (parts.length === 3) {
      if (!l3Aggregation.has(item.icms_code)) {
        l3Aggregation.set(item.icms_code, {
          total_amount: item.total_amount,
          total_rate: item.total_rate,
        });
      } else {
        const existing = l3Aggregation.get(item.icms_code)!;
        existing.total_amount += item.total_amount;
        existing.total_rate += item.total_rate;
      }
    }
    // Ignore L1 and L2 codes
  });

  // Convert the map to array and sort
  return Array.from(l3Aggregation.entries())
    .map(([icms_code, data]) => ({
      icms_code,
      total_amount: data.total_amount,
      total_rate: data.total_rate,
    }))
    .sort((a, b) => a.icms_code.localeCompare(b.icms_code));
};

/**
 * Generates a SAS URL for a blob
 */
async function generateSasUrl(blobPath: string): Promise<string> {
  if (!config.azure.storageConnectionString) {
    throw new Error('Azure storage connection string is not configured');
  }

  try {
    // Ensure the blobPath is properly decoded to handle any special characters
    const decodedBlobPath = decodeURIComponent(blobPath);

    const blobServiceClient = BlobServiceClient.fromConnectionString(
      config.azure.storageConnectionString
    );
    const containerClient = blobServiceClient.getContainerClient('abacus');
    const blockBlobClient = containerClient.getBlockBlobClient(decodedBlobPath);

    // Log the blob details for debugging
    console.log('Generating SAS URL for blob:', {
      originalPath: blobPath,
      decodedPath: decodedBlobPath,
      blobUrl: blockBlobClient.url,
    });

    const now = new Date();
    const startsOn = new Date(now.getTime() - 2 * 60 * 1000);
    const expiresOn = new Date(now.getTime());
    expiresOn.setHours(expiresOn.getHours() + 1);

    const sasUrl = await blockBlobClient.generateSasUrl({
      permissions: BlobSASPermissions.parse('r'),
      startsOn,
      expiresOn,
    });

    return sasUrl;
  } catch (error) {
    console.error('Error generating SAS URL:', error);
    throw new Error(`Failed to generate SAS URL for blob: ${blobPath}`);
  }
}

/**
 * Get project files with SAS URLs for access
 */
export async function getProjectFilesWithSasUrls(
  projectId: string,
  entryId?: string
): Promise<ProjectFiles | null> {
  try {
    // If entryId is provided, find that specific entry
    // Otherwise, find the most recent entry for the project
    let query: Record<string, string> = { project_code: projectId };
    if (entryId) {
      query.entry_id = entryId;
    }

    const project = await ProjectInputDataModel.findOne(query)
      .sort({ createdAt: -1 }) // Sort by creation date descending to get the most recent
      .lean();

    if (!project) {
      console.log('Project not found:', projectId);
      return null;
    }

    const typedProject = project as IProject;
    const result: ProjectFiles = {
      entry_id: typedProject.entry_id,
    };

    // Process upload estimate if exists
    if (typedProject.uploadEstimate?.value) {
      try {
        // Properly decode the URL to handle any encoding issues
        const urlString = decodeURIComponent(typedProject.uploadEstimate.value);
        const url = new URL(urlString);

        // Extract path after abacus
        const blobPath = url.pathname.split('/abacus/')[1];

        if (!blobPath) {
          console.error('Failed to extract blob path from URL:', url.pathname);
          throw new Error('Invalid blob path');
        }

        console.log('Processing Upload Estimate:', {
          originalUrl: typedProject.uploadEstimate.value,
          decodedUrl: urlString,
          blobPath: blobPath,
        });

        const fileName = blobPath.split('/').pop() || '';
        const sasUrl = await generateSasUrl(blobPath);
        result.uploadEstimate = {
          fileName,
          url: sasUrl,
        };
      } catch (error) {
        console.error('Error processing upload estimate:', error);
      }
    }

    // Process upload original estimate if exists
    if (typedProject.uploadOriginalEstimate?.value) {
      try {
        // Properly decode the URL to handle any encoding issues
        const urlString = decodeURIComponent(typedProject.uploadOriginalEstimate.value);
        const url = new URL(urlString);

        // Extract path after abacus
        const blobPath = url.pathname.split('/abacus/')[1];

        if (!blobPath) {
          console.error('Failed to extract blob path from URL:', url.pathname);
          throw new Error('Invalid blob path');
        }

        console.log('Processing Upload Original Estimate:', {
          originalUrl: typedProject.uploadOriginalEstimate.value,
          decodedUrl: urlString,
          blobPath: blobPath,
        });

        const fileName = blobPath.split('/').pop() || '';
        const sasUrl = await generateSasUrl(blobPath);
        result.uploadOriginalEstimate = {
          fileName,
          url: sasUrl,
        };
      } catch (error) {
        console.error('Error processing upload original estimate:', error);
      }
    }

    // Process attachments if they exist
    if (typedProject.attachments && typedProject.attachments.length > 0) {
      result.attachments = [];

      for (const attachment of typedProject.attachments) {
        try {
          if (attachment.value) {
            // Properly decode the URL to handle any encoding issues
            const urlString = decodeURIComponent(attachment.value);
            const url = new URL(urlString);

            // Extract path after abacus
            const blobPath = url.pathname.split('/abacus/')[1];

            if (!blobPath) {
              console.error('Failed to extract blob path from URL:', url.pathname);
              continue; // Skip this attachment and move to the next
            }

            console.log('Processing Attachment:', {
              originalUrl: attachment.value,
              decodedUrl: urlString,
              fileName: attachment.fileName,
              blobPath: blobPath,
            });

            const fileName = attachment.fileName || blobPath.split('/').pop() || '';
            const sasUrl = await generateSasUrl(blobPath);

            result.attachments.push({
              fileName,
              url: sasUrl,
            });
          }
        } catch (error) {
          console.error(`Error processing attachment ${attachment.fileName}:`, error);
          // Continue with other attachments even if one fails
        }
      }
    }

    return result;
  } catch (error) {
    console.error('Error getting project files:', error);
    throw error;
  }
}

/**
 * Get unique project input data for a specific project ID
 * @param projectId - The project ID to get data for
 * @returns An array of unique project input data entries
 */
export async function getUniqueProjectInputData(projectId: string): Promise<ProjectInputData[]> {
  try {
    // Find all entries for this project ID, sorted by creation date (newest first)
    const allEntries = await ProjectInputDataModel.find({ project_code: projectId })
      .sort({ createdAt: -1 })
      .lean();

    // If no entries found, return empty array
    if (!allEntries || allEntries.length === 0) {
      return [];
    }

    // Return all entries - each one is now unique because we're using entry_id
    return allEntries;
  } catch (error) {
    console.error('Error getting unique project input data:', error);
    throw error;
  }
}

export { createProjectInput, filterProjects, getBenchmarkData, calculateBenchmarkForAProject };
