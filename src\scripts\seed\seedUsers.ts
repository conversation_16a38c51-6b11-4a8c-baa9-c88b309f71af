import UserModel from "@/models/user.model";
import RoleModel from "@/models/role.model";
import logger from "@/utils/logging";
import { Types } from "mongoose";
import { seedConfig, seedOptions, type SeedUser } from "./config";

/**
 * Seeds users into the database and assigns roles to them
 * This function is idempotent - it can be run multiple times safely
 */
export async function seedUsers(): Promise<void> {
	const { users } = seedConfig;
	const { updateExisting, verbose, recreate, createUsers, dryRun } =
		seedOptions;

	if (!createUsers) {
		logger.info("⏭️  User creation is disabled in seed options, skipping...");
		return;
	}

	logger.info(`Starting to seed ${users.length} users...`);

	if (dryRun) {
		logger.info("🔍 DRY RUN MODE - No database changes will be made");
	}

	if (recreate && !dryRun) {
		logger.warn("⚠️  RECREATE mode: Deleting all existing users...");
		await UserModel.deleteMany({});
		logger.info("✅ All users deleted");
	} else if (recreate && dryRun) {
		logger.warn("🔍 DRY RUN: Would delete all existing users");
	}

	// First, ensure all roles exist and get their IDs
	const roleMap = await getRoleMap();

	const results = {
		created: 0,
		updated: 0,
		skipped: 0,
		errors: 0,
	};

	for (const userData of users) {
		try {
			await seedUser(
				userData,
				roleMap,
				updateExisting,
				verbose,
				dryRun,
				results,
			);
		} catch (error) {
			results.errors++;
			logger.error(
				`❌ Error seeding user "${userData.azureAdObjectId}":`,
				error,
			);
		}
	}

	// Summary
	logger.info(`📊 User seeding ${dryRun ? "(DRY RUN) " : ""}summary:`);
	logger.info(
		`   ✅ ${dryRun ? "Would create" : "Created"}: ${results.created}`,
	);
	logger.info(
		`   🔄 ${dryRun ? "Would update" : "Updated"}: ${results.updated}`,
	);
	logger.info(`   ⏭️  Skipped: ${results.skipped}`);
	if (results.errors > 0) {
		logger.error(`   ❌ Errors: ${results.errors}`);
	}

	logger.info(`✅ User seeding ${dryRun ? "(DRY RUN) " : ""}completed!`);
}

async function getRoleMap(): Promise<Map<string, Types.ObjectId>> {
	const { dryRun } = seedOptions;

	if (dryRun) {
		// In dry run mode, simulate the roles that would exist
		// by using the roles from the config
		const { roles } = seedConfig;
		const roleMap = new Map<string, Types.ObjectId>();

		for (const role of roles) {
			// Create a fake ObjectId for simulation
			const fakeId = new Types.ObjectId();
			roleMap.set(role.name, fakeId);
		}

		logger.info(`👥 Simulating ${roleMap.size} roles (DRY RUN)`);
		return roleMap;
	}

	// Normal mode - query the database
	const roles = await RoleModel.find({}).lean();
	const roleMap = new Map<string, Types.ObjectId>();

	for (const role of roles) {
		roleMap.set(role.name, role._id);
	}

	logger.info(`👥 Found ${roleMap.size} existing roles`);
	return roleMap;
}

async function seedUser(
	userData: SeedUser,
	roleMap: Map<string, Types.ObjectId>,
	updateExisting: boolean,
	verbose: boolean,
	dryRun: boolean,
	results: {
		created: number;
		updated: number;
		skipped: number;
		errors: number;
	},
): Promise<void> {
	// Validate that all roles exist
	const missingRoles = userData.roles.filter(
		(roleName) => !roleMap.has(roleName),
	);

	if (missingRoles.length > 0) {
		throw new Error(
			`User "${userData.azureAdObjectId}" references non-existent roles: ${missingRoles.join(", ")}`,
		);
	}

	// Convert role names to ObjectIds
	const roleIds = userData.roles.map((roleName) => {
		const roleId = roleMap.get(roleName);
		if (!roleId) {
			throw new Error(`Role "${roleName}" not found in map`);
		}
		return roleId;
	});

	// Find assignedBy user if specified and not "system"
	let assignedByUser: Types.ObjectId | undefined;
	if (userData.assignedBy && userData.assignedBy !== "system") {
		const assignedByDoc = await UserModel.findOne({
			azureAdObjectId: userData.assignedBy,
		}).lean();

		if (assignedByDoc) {
			assignedByUser = assignedByDoc._id;
		} else {
			logger.warn(
				`⚠️  AssignedBy user "${userData.assignedBy}" not found for user "${userData.azureAdObjectId}"`,
			);
		}
	}

	const existingUser = await UserModel.findOne({
		azureAdObjectId: userData.azureAdObjectId,
	}).lean();

	if (existingUser) {
		if (updateExisting) {
			// Check if update is needed
			const existingRoleIds = (existingUser.roles || [])
				.map((r) => r.toString())
				.sort();
			const newRoleIds = roleIds.map((r) => r.toString()).sort();

			const needsUpdate =
				!arraysEqual(existingRoleIds, newRoleIds) ||
				(assignedByUser &&
					existingUser.assignedBy?.toString() !== assignedByUser.toString());

			if (needsUpdate) {
				if (!dryRun) {
					await UserModel.findOneAndUpdate(
						{ azureAdObjectId: userData.azureAdObjectId },
						{
							roles: roleIds,
							...(assignedByUser && { assignedBy: assignedByUser }),
						},
						{ new: true },
					);
				}
				results.updated++;
				if (verbose) {
					if (!dryRun) {
						logger.info(
							`🔄 Updated user: ${userData.azureAdObjectId} (${userData.roles.length} roles)`,
						);
					} else {
						logger.info(
							`🔍 DRY RUN: Would update user: ${userData.azureAdObjectId} (${userData.roles.length} roles)`,
						);
					}
				}
			} else {
				results.skipped++;
				if (verbose) {
					logger.info(
						`⏭️  Skipped user (no changes): ${userData.azureAdObjectId}`,
					);
				}
			}
		} else {
			results.skipped++;
			if (verbose) {
				logger.info(`⏭️  Skipped existing user: ${userData.azureAdObjectId}`);
			}
		}
	} else {
		// Create new user
		if (!dryRun) {
			const newUser = {
				azureAdObjectId: userData.azureAdObjectId,
				roles: roleIds,
				...(assignedByUser && { assignedBy: assignedByUser }),
			};

			await UserModel.create(newUser);
		}
		results.created++;
		if (verbose) {
			if (!dryRun) {
				logger.info(
					`✅ Created user: ${userData.azureAdObjectId} (${userData.roles.length} roles)`,
				);
			} else {
				logger.info(
					`🔍 DRY RUN: Would create user: ${userData.azureAdObjectId} (${userData.roles.length} roles)`,
				);
			}
		}
	}
}

function arraysEqual(a: string[], b: string[]): boolean {
	return a.length === b.length && a.every((val, i) => val === b[i]);
}

// Allow running this script directly
if (require.main === module) {
	(async () => {
		try {
			// Import database connection
			const { connectMongo } = await import(
				"@/infrastructure/database/mongo.connection"
			);
			const config = await import("@/config/config");

			// Connect to MongoDB
			await connectMongo(config.default.database.mongo.uri);
			logger.info("📦 Connected to MongoDB");

			// Run the seeder
			await seedUsers();

			process.exit(0);
		} catch (error) {
			logger.error("💥 Error in user seeding:", error);
			process.exit(1);
		}
	})();
}
