import { Pool, type QueryResultRow } from "pg";
import logger from "../../utils/logging";
import { poolConfig } from "./config";

class DatabaseConnection {
	private static instance: DatabaseConnection;
	private pool: Pool;

	private constructor() {
		this.pool = new Pool(poolConfig);

		// Log pool events
		this.pool.on("connect", () => {
			logger.info("New client connected to the pool");
		});

		this.pool.on("error", (err) => {
			logger.error("Unexpected error on idle client", { error: err });
		});
	}

	public static getInstance(): DatabaseConnection {
		if (!DatabaseConnection.instance) {
			DatabaseConnection.instance = new DatabaseConnection();
			logger.info("Database connection pool initialized");
		}
		return DatabaseConnection.instance;
	}

	public async query<T extends QueryResultRow>(
		text: string,
		params?: unknown[],
	): Promise<T[]> {
		const client = await this.pool.connect();
		try {
			const result = await client.query<T>(text, params);
			return result.rows;
		} finally {
			client.release();
		}
	}

	public async end(): Promise<void> {
		await this.pool.end();
		logger.info("Database connection pool closed");
	}
}

// Export singleton instance once
export const db = DatabaseConnection.getInstance();
