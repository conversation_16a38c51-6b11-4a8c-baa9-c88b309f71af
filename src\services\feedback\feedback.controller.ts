import { Request, Response, NextFunction } from 'express';
import { FeedbackService } from './feedback.service';
import { z } from 'zod';
import {
  FeedbackInput,
  createFeedbackSchema,
  CreateFeedbackRequest,
  FeedbackIdParams,
} from './feedback.types';

export class FeedbackController {
  constructor(private feedbackService: FeedbackService) {}

  /**
   * Submit new feedback
   */
  public submitFeedback = async (
    req: Request<{}, {}, CreateFeedbackRequest>,
    res: Response,
    next: NextFunction
  ) => {
    let validatedData: CreateFeedbackRequest;
    try {
      validatedData = createFeedbackSchema.parse(req.body);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: 'Validation error', details: error.errors });
      }
      throw error;
    }

    // Get the user ID from the token
    const azureAdObjectId = req.headers['x-azure-ad-object-id'] as string;

    if (!azureAdObjectId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    // Create the feedback
    const feedbackInput: FeedbackInput = {
      message: validatedData.message,
      azureAdObjectId,
      page_url: validatedData.page_url,
      page_title: validatedData.page_title,
    };

    const feedback = await this.feedbackService.createFeedback(feedbackInput);

    return res.status(201).json({
      message: 'Feedback submitted successfully',
      data: feedback,
    });
  };

  /**
   * Get all feedback (admin only)
   */
  public getAllFeedback = async (req: Request, res: Response, next: NextFunction) => {
    const feedback = await this.feedbackService.getAllFeedback();
    return res.status(200).json({ data: feedback });
  };

  /**
   * Get feedback by ID (admin only)
   */
  public getFeedbackById = async (
    req: Request<FeedbackIdParams>,
    res: Response,
    next: NextFunction
  ) => {
    const { id } = req.params;
    const feedback = await this.feedbackService.getFeedbackById(id);

    if (!feedback) {
      return res.status(404).json({ error: 'Feedback not found' });
    }

    return res.status(200).json({ data: feedback });
  };

  /**
   * Get feedback by user (admin or own feedback)
   */
  public getFeedbackByUser = async (req: Request, res: Response, next: NextFunction) => {
    // Get the requesting user's ID
    const requestingUserId = req.headers['x-azure-ad-object-id'] as string;

    const feedback = await this.feedbackService.getFeedbackByUser(requestingUserId);
    return res.status(200).json({ data: feedback });
  };
}
