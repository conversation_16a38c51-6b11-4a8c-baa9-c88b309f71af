import {
	Severity,
	getModelForClass,
	index,
	modelOptions,
	prop,
} from "@typegoose/typegoose";
import { Types } from "mongoose";

// =====================================================
// SIMPLE EMBEDDED CLASSES
// =====================================================

class TaxonomyItem {
	@prop({ required: true, lowercase: true, trim: true, type: () => String })
	public id!: string;

	@prop({ required: true, trim: true, type: () => String })
	public label!: string;

	@prop({ trim: true, type: () => String })
	public description?: string;
}

class AttachedFile {
	@prop({ required: true, type: () => String })
	public url!: string;

	@prop({ required: true, trim: true, type: () => String })
	public label!: string;

	@prop({ auto: true, type: () => Types.ObjectId })
	public _id!: Types.ObjectId;
}

class Content {
	@prop({ type: () => [AttachedFile], default: [] })
	public attachedFiles!: AttachedFile[];
}

class Taxonomy {
	@prop({ type: () => TaxonomyItem })
	public sector?: TaxonomyItem;

	@prop({ type: () => TaxonomyItem })
	public subsector?: TaxonomyItem;

	@prop({ type: () => TaxonomyItem })
	public buildType?: TaxonomyItem;

	@prop({ type: () => [String], default: [] })
	public hierarchyPath!: string[];
}

class Metadata {
	@prop({ type: Number })
	public order?: number;

	@prop({ type: Types.ObjectId })
	public parentId?: Types.ObjectId;

	@prop({ required: true, default: Date.now, type: () => Date })
	public createdAt!: Date;

	@prop({ required: true, default: Date.now, type: () => Date })
	public updatedAt!: Date;
}

// =====================================================
// MAIN MODEL - SIMPLE AND CLEAN
// =====================================================

@modelOptions({
	schemaOptions: {
		collection: "how_to_guides",
		timestamps: {
			createdAt: "metadata.createdAt",
			updatedAt: "metadata.updatedAt",
		},
		toJSON: {
			transform: (doc, ret) => {
				const { __v, ...cleanRet } = ret;
				return cleanRet;
			},
		},
	},
	options: {
		allowMixed: Severity.ALLOW,
	},
})
// Essential indexes only
@index({ "taxonomy.sector.id": 1 })
@index({ "taxonomy.subsector.id": 1 })
@index({ "taxonomy.buildType.id": 1 })
@index({ "taxonomy.sector.id": 1, "taxonomy.subsector.id": 1 })
@index({ "taxonomy.hierarchyPath": 1 })
@index({ "metadata.order": 1 })
@index({ title: "text", description: "text" })
export class HowToGuide {
	public _id!: Types.ObjectId;

	@prop({ required: true, trim: true, type: () => String })
	public title!: string;

	@prop({ type: () => String })
	public description?: string;

	@prop({ type: () => String })
	public overview?: string;

	@prop({ type: () => Taxonomy, required: true })
	public taxonomy!: Taxonomy;

	@prop({ type: () => Content, required: true })
	public content!: Content;

	@prop({ type: () => Metadata, required: true })
	public metadata!: Metadata;

	// =====================================================
	// STATIC METHODS FOR CRUD OPERATIONS
	// =====================================================

	// Update title
	static async updateTitle(id: string, title: string) {
		return await HowToGuideModel.findByIdAndUpdate(
			id,
			{ title },
			{ new: true },
		);
	}

	// Update description
	static async updateDescription(id: string, description: string) {
		return await HowToGuideModel.findByIdAndUpdate(
			id,
			{ description },
			{ new: true },
		);
	}

	// Update overview
	static async updateOverview(id: string, overview: string) {
		return await HowToGuideModel.findByIdAndUpdate(
			id,
			{ overview },
			{ new: true },
		);
	}

	// Update taxonomy and rebuild hierarchy path
	static async updateTaxonomy(
		id: string,
		sector?: TaxonomyItem,
		subsector?: TaxonomyItem,
		buildType?: TaxonomyItem,
	) {
		const update: any = {};

		if (sector) update["taxonomy.sector"] = sector;
		if (subsector) update["taxonomy.subsector"] = subsector;
		if (buildType) update["taxonomy.buildType"] = buildType;

		// Rebuild hierarchy path
		const hierarchyPath: string[] = [];
		if (sector?.id) hierarchyPath.push(sector.id);
		if (subsector?.id) hierarchyPath.push(subsector.id);
		if (buildType?.id) hierarchyPath.push(buildType.id);

		update["taxonomy.hierarchyPath"] = hierarchyPath;

		return await HowToGuideModel.findByIdAndUpdate(id, update, { new: true });
	}

	// Add attached file
	static async addAttachedFile(id: string, url: string, label: string) {
		return await HowToGuideModel.findByIdAndUpdate(
			id,
			{
				$push: {
					"content.attachedFiles": {
						url,
						label,
						_id: new Types.ObjectId(),
					},
				},
			},
			{ new: true },
		);
	}

	// Remove attached file
	static async removeAttachedFile(id: string, fileId: string) {
		return await HowToGuideModel.findByIdAndUpdate(
			id,
			{
				$pull: {
					"content.attachedFiles": { _id: fileId },
				},
			},
			{ new: true },
		);
	}

	// Replace all attached files
	static async replaceAttachedFiles(
		id: string,
		files: Array<{ url: string; label: string }>,
	) {
		const attachedFiles = files.map((file) => ({
			url: file.url,
			label: file.label,
			_id: new Types.ObjectId(),
		}));

		return await HowToGuideModel.findByIdAndUpdate(
			id,
			{
				$set: {
					"content.attachedFiles": attachedFiles,
				},
			},
			{ new: true },
		);
	}

	// Update order
	static async updateOrder(id: string, order: number) {
		return await HowToGuideModel.findByIdAndUpdate(
			id,
			{ "metadata.order": order },
			{ new: true },
		);
	}

	// Update parent
	static async updateParent(id: string, parentId: string | null) {
		return await HowToGuideModel.findByIdAndUpdate(
			id,
			{ "metadata.parentId": parentId },
			{ new: true },
		);
	}

	// =====================================================
	// STATIC METHODS FOR FILTERING (For Dropdowns)
	// =====================================================

	// Get all sectors
	static async getSectors() {
		return await HowToGuideModel.aggregate([
			{
				$group: {
					_id: "$taxonomy.sector.id",
					label: { $first: "$taxonomy.sector.label" },
					description: { $first: "$taxonomy.sector.description" },
					count: { $sum: 1 },
				},
			},
			{ $match: { _id: { $ne: null } } },
			{ $sort: { label: 1 } },
		]);
	}

	// Get subsectors for a sector
	static async getSubsectors(sectorId: string) {
		return await HowToGuideModel.aggregate([
			{ $match: { "taxonomy.sector.id": sectorId } },
			{
				$group: {
					_id: "$taxonomy.subsector.id",
					label: { $first: "$taxonomy.subsector.label" },
					description: { $first: "$taxonomy.subsector.description" },
					count: { $sum: 1 },
				},
			},
			{ $match: { _id: { $ne: null } } },
			{ $sort: { label: 1 } },
		]);
	}

	// Get build types for a sector and subsector
	static async getBuildTypes(sectorId: string, subsectorId: string) {
		return await HowToGuideModel.aggregate([
			{
				$match: {
					"taxonomy.sector.id": sectorId,
					"taxonomy.subsector.id": subsectorId,
				},
			},
			{
				$group: {
					_id: "$taxonomy.buildType.id",
					label: { $first: "$taxonomy.buildType.label" },
					description: { $first: "$taxonomy.buildType.description" },
					count: { $sum: 1 },
				},
			},
			{ $match: { _id: { $ne: null } } },
			{ $sort: { label: 1 } },
		]);
	}

	// =====================================================
	// STATIC METHODS FOR FINDING DOCUMENTS
	// =====================================================

	// Find by filters
	static async findByFilters(
		sectorId?: string,
		subsectorId?: string,
		buildTypeId?: string,
		page = 1,
		limit = 20,
	) {
		const filters: any = {};

		if (sectorId) filters["taxonomy.sector.id"] = sectorId;
		if (subsectorId) filters["taxonomy.subsector.id"] = subsectorId;
		if (buildTypeId) filters["taxonomy.buildType.id"] = buildTypeId;

		const skip = (page - 1) * limit;

		const [documents, total] = await Promise.all([
			HowToGuideModel.find(filters)
				.sort({ "metadata.order": 1, "metadata.createdAt": -1 })
				.skip(skip)
				.limit(limit)
				.lean(),
			HowToGuideModel.countDocuments(filters),
		]);

		return {
			documents,
			pagination: {
				page,
				limit,
				total,
				pages: Math.ceil(total / limit),
			},
		};
	}

	// Find by hierarchy path
	static async findByHierarchy(hierarchyLevel: string) {
		return await HowToGuideModel.find({
			"taxonomy.hierarchyPath": hierarchyLevel,
		}).lean();
	}

	// Text search
	static async search(searchTerm: string, page = 1, limit = 20) {
		const skip = (page - 1) * limit;

		const [documents, total] = await Promise.all([
			HowToGuideModel.find(
				{ $text: { $search: searchTerm } },
				{ score: { $meta: "textScore" } },
			)
				.sort({ score: { $meta: "textScore" } })
				.skip(skip)
				.limit(limit)
				.lean(),
			HowToGuideModel.countDocuments({ $text: { $search: searchTerm } }),
		]);

		return {
			documents,
			pagination: {
				page,
				limit,
				total,
				pages: Math.ceil(total / limit),
			},
		};
	}

	// Find with attachments
	static async findWithAttachments() {
		return await HowToGuideModel.find({
			"content.attachedFiles.0": { $exists: true },
		}).lean();
	}

	// Find by parent
	static async findByParent(parentId: string) {
		return await HowToGuideModel.find({
			"metadata.parentId": parentId,
		})
			.sort({ "metadata.order": 1 })
			.lean();
	}

	// Find recent documents
	static async findRecent(limit = 10) {
		return await HowToGuideModel.find({})
			.sort({ "metadata.createdAt": -1 })
			.limit(limit)
			.lean();
	}
}

// =====================================================
// SIMPLE TYPES FOR API
// =====================================================

export interface CreateHowToGuideData {
	title: string;
	description?: string;
	overview?: string;
	taxonomy: {
		sector?: TaxonomyItem;
		subsector?: TaxonomyItem;
		buildType?: TaxonomyItem;
	};
	metadata?: {
		order?: number;
		parentId?: string;
	};
	attachedFiles?: Array<{
		url: string;
		label: string;
	}>;
}

export interface UpdateHowToGuideData {
	title?: string;
	description?: string;
	overview?: string;
	taxonomy?: {
		sector?: TaxonomyItem;
		subsector?: TaxonomyItem;
		buildType?: TaxonomyItem;
	};
	metadata?: {
		order?: number;
		parentId?: string;
	};
	attachedFiles?: Array<{
		url: string;
		label: string;
	}>;
}

export const HowToGuideModel = getModelForClass(HowToGuide);
