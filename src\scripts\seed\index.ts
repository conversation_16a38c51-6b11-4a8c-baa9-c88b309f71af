import logger from "@/utils/logging";
import { connectMongo } from "@/infrastructure/database/mongo.connection";
import config from "@/config/config";
import { seedPermissions } from "./seedPermissions";
import { seedRoles } from "./seedRoles";
import { seedUsers } from "./seedUsers";
import { seedOptions } from "./config";

/**
 * Main seeding script that runs all seeders in the correct order
 *
 * Order of execution:
 * 1. Permissions (base level)
 * 2. Roles (depend on permissions)
 * 3. Users (depend on roles)
 *
 * This script is idempotent and can be run multiple times safely.
 */
async function runAllSeeders(): Promise<void> {
	const startTime = Date.now();

	logger.info("🌱 Starting Einstein database seeding...");
	logger.info("=".repeat(50));

	// Log configuration
	logger.info("📋 Seed Configuration:");
	logger.info(`   Update Existing: ${seedOptions.updateExisting}`);
	logger.info(`   Create Users: ${seedOptions.createUsers}`);
	logger.info(`   Verbose Logging: ${seedOptions.verbose}`);
	logger.info(
		`   Recreate Mode: ${seedOptions.recreate ? "⚠️  ENABLED" : "Disabled"}`,
	);
	logger.info(
		`   Dry Run Mode: ${seedOptions.dryRun ? "🔍 ENABLED" : "Disabled"}`,
	);
	logger.info("");

	if (seedOptions.dryRun) {
		logger.info("🔍 DRY RUN MODE ENABLED");
		logger.info("   No actual database changes will be made");
		logger.info(
			"   This will show you what would happen if you ran the script normally",
		);
		logger.info("");
	}

	try {
		// Step 1: Seed Permissions
		logger.info("📜 Step 1: Seeding Permissions");
		logger.info("-".repeat(30));
		await seedPermissions();
		logger.info("");

		// Step 2: Seed Roles
		logger.info("👥 Step 2: Seeding Roles");
		logger.info("-".repeat(30));
		await seedRoles();
		logger.info("");

		// Step 3: Seed Users (if enabled)
		if (seedOptions.createUsers) {
			logger.info("🧑‍💼 Step 3: Seeding Users");
			logger.info("-".repeat(30));
			await seedUsers();
			logger.info("");
		} else {
			logger.info("⏭️  Step 3: Skipping Users (disabled in config)");
			logger.info("");
		}

		const endTime = Date.now();
		const duration = ((endTime - startTime) / 1000).toFixed(2);

		logger.info("=".repeat(50));
		logger.info(
			`🎉 Database seeding ${seedOptions.dryRun ? "(DRY RUN) " : ""}completed successfully!`,
		);
		logger.info(`⏱️  Total time: ${duration} seconds`);
		logger.info("");

		if (seedOptions.dryRun) {
			logger.info("🔍 DRY RUN SUMMARY:");
			logger.info("   This was a preview of what would happen");
			logger.info("   To apply these changes, set dryRun: false in config.ts");
			logger.info("");
		} else {
			logger.info("🔍 Next steps:");
			logger.info("   1. Verify the seeded data using the API endpoints");
			logger.info(
				"   2. Update the azureAdObjectIds in config.ts with real Azure AD Object IDs",
			);
			logger.info("   3. Re-run this script to update users with correct IDs");
			logger.info("");
		}
	} catch (error) {
		logger.error("💥 Error during database seeding:", error);
		throw error;
	}
}

/**
 * Connects to the database and runs all seeders
 */
async function main(): Promise<void> {
	try {
		// Connect to MongoDB
		logger.info("📦 Connecting to MongoDB...");
		await connectMongo(config.database.mongo.uri);
		logger.info("✅ Connected to MongoDB");
		logger.info("");

		// Run all seeders
		await runAllSeeders();

		process.exit(0);
	} catch (error) {
		logger.error("💥 Fatal error during seeding:", error);
		process.exit(1);
	}
}

// Export functions for use in other scripts
export { runAllSeeders, seedPermissions, seedRoles, seedUsers };

// Run the script if executed directly
if (require.main === module) {
	main();
}
