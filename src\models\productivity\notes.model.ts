import { databaseManager } from "@/infrastructure/database/database-manager";
import {
	prop,
	getModelForClass,
	modelOptions,
	Severity,
} from "@typegoose/typegoose";
import { Types, type Connection } from "mongoose";

interface NotesData {
	memPageData?: string;
	memPageData2?: string;
	memPageData3?: string;
	memPageData4?: string;
	memPageData5?: string;
	memPageData6?: string;
}

@modelOptions({
	schemaOptions: { collection: "notes" },
	options: {
		allowMixed: Severity.ALLOW,
	},
})
export class Notes {
	@prop({ required: true, type: () => Types.ObjectId })
	public _id!: Types.ObjectId;

	@prop({ required: true })
	public TradeInfoID!: number;

	@prop({ required: true })
	public TradeID!: number;

	@prop()
	public Description?: string | null;

	@prop()
	public imgFile?: string | null;

	@prop({ required: true })
	public RateID!: number;

	@prop({ required: true })
	public NotShow!: number;

	@prop({ required: true })
	public NotActive!: number;

	@prop({ type: () => Object })
	public notes_data?: NotesData;

	@prop({ type: () => [String] })
	public img_tags?: string[];

	@prop({ type: () => [String] })
	public href_tags?: string[];

	@prop({ type: () => [String] })
	public einstein_img_tags?: string[];

	@prop({ type: () => [String] })
	public einstein_href_tags?: string[];
}

export function getNotesModel() {
	const conn: Connection = databaseManager.getConnection("productivity");
	return getModelForClass(Notes, { existingConnection: conn });
}

export type NotesDocument = Notes;
