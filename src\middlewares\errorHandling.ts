import logger from "@/utils/logging";
import type { NextFunction, Request, Response } from "express";
import { ZodError } from "zod";
import type { HttpException } from "../utils/exceptions/http.exception";

export const errorHandler = (
	error: HttpException,
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	const status = error.status || 500;
	const message = error.message || "Something went wrong";

	// Log the error with request context
	logger.error("Request error occurred", {
		error: {
			message: error.message,
			stack: error.stack,
			status: error.status,
		},
		request: {
			method: req.method,
			url: req.url,
			path: req.path,
			query: req.query,
			body: req.body,
			headers: {
				"user-agent": req.headers["user-agent"],
				"content-type": req.headers["content-type"],
			},
			ip: req.ip,
		},
	});

	if (error instanceof ZodError) {
		res.status(400).json({ errors: error.errors });
	} else {
		res.status(status).json({
			status,
			message,
		});
	}
	next(error);
};
