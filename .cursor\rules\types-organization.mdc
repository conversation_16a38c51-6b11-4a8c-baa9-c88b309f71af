---
description: 
globs: 
alwaysApply: true
---
# Types Organization Pattern

This project follows a consistent approach to organizing TypeScript types and interfaces to improve code clarity, maintainability, and reusability.

## Dedicated Types Files

Each domain service should have a dedicated types file that follows this naming convention:
- `domain.types.ts` - Contains all types, interfaces, and enums for the domain

Example structure:
```
services/
└── feedback/
    ├── feedback.service.ts       - Business logic
    ├── feedback.controller.ts    - HTTP handling
    ├── feedback.repository.ts    - Repository interface
    ├── feedback.model.ts         - Data model
    ├── feedback.types.ts         - Type definitions
    ├── feedback.routes.ts        - Route definitions
    └── mongoFeedback.repository.ts - Implementation
```

## Types File Content

The types file should contain:

1. **Request/Response Interfaces** - Data structures for API requests and responses
2. **DTOs (Data Transfer Objects)** - Shapes of data passed between layers
3. **Enums** - Type-safe enumerations for the domain
4. **Utility Types** - Helper types specific to the domain

Example `feedback.types.ts`:
```typescript
// Input types - used for service parameters
export interface FeedbackInput {
  message: string;
  azureAdObjectId: string;
  page_url?: string;
  page_title?: string;
}

// Response types - returned from service methods
export interface FeedbackResponse {
  id: string;
  message: string;
  azureAdObjectId: string;
  createdAt: Date;
  page_url?: string;
  page_title?: string;
}

// Enums
export enum FeedbackStatus {
  PENDING = 'pending',
  REVIEWED = 'reviewed',
  RESOLVED = 'resolved'
}

// Parameter types for controllers
export type FeedbackParams = {
  id: string;
};

// Query types for endpoints with query parameters
export interface FeedbackQuery {
  status?: FeedbackStatus;
  limit?: number;
  offset?: number;
}
```

## Best Practices

1. **Don't Duplicate Model Types** - Reference model types directly rather than duplicating them
2. **Export Everything** - Make all types exportable for use in other files
3. **Use Type Inference When Possible** - Leverage TypeScript's inference with Zod or similar tools:
   ```typescript
   import { z } from 'zod';
   
   export const createFeedbackSchema = z.object({
     message: z.string().min(1),
     page_url: z.string().optional(),
     page_title: z.string().optional(),
   });
   
   export type CreateFeedbackInput = z.infer<typeof createFeedbackSchema>;
   ```
4. **Keep Related Types Together** - Group related types in the same file

## Importing Types

When importing types, use the following pattern for clarity:
```typescript
import { FeedbackInput, FeedbackResponse } from './feedback.types';
```

This pattern ensures that all types are centralized, reusable, and not scattered across implementation files, improving maintainability and clarity.


