import type { Project } from "@/models/project.model";
import type {
	FilterMapping,
	MarketShareData,
	PaginatedResult,
} from "./types";

/**
 * ProjectRepository interface that defines the contract for project data operations.
 * This interface allows for different implementations (MongoDB, PostgreSQL, etc.)
 * while maintaining a consistent API.
 */
export interface ProjectRepository {
	/**
	 * Retrieve paginated projects.
	 * @param page The page number (starts at 1).
	 * @param limit The number of projects per page.
	 * @returns The paginated result with projects, total count, page number, limit, and total pages.
	 */
	getProjects(page: number, limit: number): Promise<PaginatedResult<Project>>;

	/**
	 * Retrieve a project by id.
	 * @param projectId The project id.
	 * @returns The project or null if not found.
	 */
	getProjectById(projectId: string): Promise<Project | null>;

	/**
	 * Get available project filters
	 * @returns The available project filters including count, company, city, industry, service offering, planned revenue, and status.
	 */
	getAvailableProjectFilters(): Promise<{
		count: number;
		company: string[];
		city: string[];
		industry: string[];
		serviceOffering: string[];
		plannedRevenue: { value: string; label: string }[];
		status: string[];
		projectManager: string[];
		projectDirector: string[];
	}>;

	/**
	 * Searches for projects, returning distinct results based on a specified field,
	 * while including associated project details. Supports pagination.
	 *
	 * @param params - The search parameters.
	 * @param params.fieldName - The name of the field to ensure distinctness on.
	 * @param params.page - The page number (starts at 1). Default is 1.
	 * @param params.limit - The number of distinct items per page. Default is 30.
	 * @param params.queryTerm - Optional search query to filter results based on the fieldName.
	 * @returns A promise resolving to an array of objects, each containing the distinct field value,
	 *          project director, and project name.
	 */
	searchItemFilter(params: {
		fieldName: string;
		page?: number;
		limit?: number;
		queryTerm?: string;
	}): Promise<
		Array<{
			[key: string]: any;
			projectdirector?: string | null;
			projectname?: string | null;
		}>
	>;

	/**
	 * Retrieves projects filtered by the provided filter with pagination support.
	 * @param filters The filter to apply.
	 * @param page The page number (starts at 1).
	 * @param limit The number of projects per page.
	 * @returns The paginated filtered projects.
	 */
	getProjectsByFilters(
		filters: Partial<Record<keyof FilterMapping, string[]>>,
		page?: number,
		limit?: number,
	): Promise<PaginatedResult<Project>>;

	/**
	 * Gets the market share percentages for the provided filters.
	 * @param filters The filter to apply.
	 * @param deselectedProjects The projects to exclude.
	 * @returns The market share percentages.
	 */
	getPercentageByMarketShare(
		filters: Partial<Record<keyof FilterMapping, string[]>>,
		deselectedProjects?: Array<{ projectId: string; index: string }>,
	): Promise<MarketShareData[]>;

	/**
	 * Downloads filtered projects data as an Excel file
	 * @param filters The filter to apply
	 * @returns The Excel file data and filename
	 */
	downloadFilteredProjects(
		filters: Partial<Record<keyof FilterMapping, string[]>>,
	): Promise<{ filename: string; data: Buffer }>;
}