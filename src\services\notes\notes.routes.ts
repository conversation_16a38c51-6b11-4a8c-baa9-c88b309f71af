import { Router } from "express";
import { MongoNotesRepository } from "@/services/repositories/mongoNotes.repository";
import { NotesService } from "./notes.service";
import { NotesController } from "./notes.controller";

const router = Router();

const notesRepository = new MongoNotesRepository();
const notesService = new NotesService(notesRepository);
const notesController = new NotesController(notesService);

router.get("/", notesController.getAllNotes);
router.get("/active", notesController.getActiveNotes);
router.get("/visible", notesController.getVisibleNotes);
router.get("/trade/:tradeId", notesController.getNotesByTradeId);
router.get("/tradeinfo/:tradeInfoId", notesController.getNotesByTradeInfoId);
router.get("/:id", notesController.getNoteById);

router.patch("/bulk", notesController.bulkUpdateNotes);

export default router;