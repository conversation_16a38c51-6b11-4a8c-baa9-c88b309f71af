{"compilerOptions": {"target": "es2020", "module": "commonjs", "lib": ["es2020"], "outDir": "./dist", "rootDir": ".", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["vitest/globals"]}, "include": ["src/**/*", "test/**/*"], "exclude": ["node_modules", "dist"]}