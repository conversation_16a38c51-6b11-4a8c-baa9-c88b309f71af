import { AccumulatedDurationModel } from "./accumulatedDuration.model";
import type { FilterConfig } from "./duration.types.v3";
import filterConfig from "./filter_config.json";

/**
 * This service is responsible for the accumulated duration data.
 * It is used to get the filters, search projects, get benchmark data, and get projects by type.
 * It is also used to get the accumulated duration data for the Gantt chart and scatterplot.
 * It is also used to get the accumulated duration data for the projects by type.
 * It is also used to get the accumulated duration data for the projects by type.
 */
export class AccumulatedDurationService {
	// constructor() {}
	private readonly DAYS_PER_MONTH = 30.44; // Average days per month

	// Convert days to months
	private daysToMonths(days: number | null): number | null {
		if (days === null || days === undefined) return null;
		return Math.round((days / this.DAYS_PER_MONTH) * 10) / 10; // Round to 1 decimal place
	}

	// Get the filters
	async getFilters(): Promise<FilterConfig> {
		return {
			...filterConfig,
			generated_at: new Date(filterConfig.generated_at),
		} as FilterConfig;
	}

	// Search projects
	async searchProjects(
		filters: any,
		page: number,
		limit: number,
		search = "",
		sortBy = "project_code",
		sortOrder = "asc",
	): Promise<any> {
		try {
			const skip = (page - 1) * limit;
			let query = this.buildQuery(filters);

			// Add search functionality if search term is provided
			if (search?.trim()) {
				const searchRegex = new RegExp(search.trim(), "i");
				query = {
					...query,
					$or: [
						{ project_code: searchRegex },
						{ project_sheet_name: searchRegex },
						{ sub_project_name: searchRegex },
						{ "TBH PM": searchRegex },
						{ "Project City": searchRegex },
						// Search in unique_items array for Project_Name
						{
							unique_items: {
								$elemMatch: {
									item_name: "Project_Name",
									item_value: searchRegex,
								},
							},
						},
					],
				};
			}

			// Build sort object
			const sortDirection = sortOrder.toLowerCase() === "desc" ? -1 : 1;
			const sortObject: any = {};

			// Map sort fields to actual database fields
			const sortFieldMap: Record<string, string> = {
				project_code: "project_code",
				project_name: "project_name_sort", // We'll add this in the pipeline
				type: "type",
				project_sheet_name: "project_sheet_name",
				sub_project_name: "sub_project_name",
				"TBH Project Number": "TBH Project Number",
				"Total Project Value": "total_project_value.planned",
				total_project_value_planned: "total_project_value.planned",
				total_project_value_actual: "total_project_value.actual",
				"TBH PM": "TBH PM",
				"Project City": "Project City",
			};

			const actualSortField = sortFieldMap[sortBy] || "project_code";

			// Create a counting aggregation pipeline that matches the filtering logic
			const countPipeline = [{ $match: query }, { $count: "total" }];

			const [projects, countResult] = await Promise.all([
				AccumulatedDurationModel.aggregate([
					// Match documents with filters
					{ $match: query },

					// Project fields and extract specific unique_items values
					{
						$project: {
							project_code: 1,
							project_sheet_name: 1,
							type: 1,
							sub_project_name: 1,
							"TBH Project Number": 1,
							"Total Project Value": 1,
							"TBH PM": 1,
							"Project City": 1,
							"Derived Data.stage_groups": 1,
							"Derived Data.transition_groups": 1,
							total_project_value: 1,
							// Extract Project_Name from unique_items array
							project_name: {
								$let: {
									vars: {
										projectNameItem: {
											$arrayElemAt: [
												{
													$filter: {
														input: "$unique_items",
														cond: { $eq: ["$$this.item_name", "Project_Name"] },
													},
												},
												0,
											],
										},
									},
									in: "$$projectNameItem.item_value",
								},
							},
							// Add a field for sorting by project_name
							project_name_sort: {
								$toLower: {
									$let: {
										vars: {
											projectNameItem: {
												$arrayElemAt: [
													{
														$filter: {
															input: "$unique_items",
															cond: {
																$eq: ["$$this.item_name", "Project_Name"],
															},
														},
													},
													0,
												],
											},
										},
										in: { $ifNull: ["$$projectNameItem.item_value", ""] },
									},
								},
							},
						},
					},

					// Sort
					{ $sort: { [actualSortField]: sortDirection } },

					// Pagination
					{ $skip: skip },
					{ $limit: limit },

					// Remove the temporary sort field if it was added
					{
						$project: {
							project_name_sort: 0,
						},
					},
				]).exec(),
				AccumulatedDurationModel.aggregate(countPipeline).exec(),
			]);

			// Extract total from count result
			const total = countResult.length > 0 ? countResult[0].total : 0;

			return {
				projects,
				pagination: {
					page,
					limit,
					total,
					totalPages: Math.ceil(total / limit),
				},
			};
		} catch (error) {
			throw new Error(`Failed to search projects: ${error}`);
		}
	}

	// Build the query
	private buildQuery(filters: any): any {
		const query: any = {};
		const uniqueItemsConditions: any[] = [];

		if (!filters || Object.keys(filters).length === 0) {
			return query;
		}

		// Handle project selections first
		if (filters.select_all === true) {
			// Select all projects, but exclude deselected ones
			if (
				filters.deselected_projects &&
				filters.deselected_projects.length > 0
			) {
				const deselectedConditions = filters.deselected_projects.map(
					(p: any) => ({
						project_code: p.project_code,
						project_sheet_name: p.project_sheet_name,
					}),
				);
				query.$nor = deselectedConditions;
			}
		} else if (
			filters.project_selections &&
			filters.project_selections.length > 0
		) {
			// Only include specifically selected projects
			const selectedConditions = filters.project_selections.map((p: any) => ({
				project_code: p.project_code,
				project_sheet_name: p.project_sheet_name,
			}));
			query.$or = selectedConditions;
		}

		// Process each filter
		for (const [key, value] of Object.entries(filters)) {
			// Skip pagination and search fields and project selection fields
			if (
				key === "page" ||
				key === "limit" ||
				key === "search" ||
				key === "project_selections" ||
				key === "select_all" ||
				key === "deselected_projects"
			) {
				continue;
			}

			// Handle simple string filters (type, project_city, etc.)
			if (typeof value === "string") {
				// Map common field names
				if (key === "project_city" || key === "city") {
					query["Project City"] = value;
				} else if (key === "contractType") {
					query["contract_type.planned"] = {
						$in: Array.isArray(value) ? value : [value],
					};
				} else {
					query[key] = value;
				}
			}
			// Handle array filters (for dropdown multi-select)
			else if (Array.isArray(value)) {
				if (key === "city") {
					query["Project City"] = { $in: value };
				} else if (key === "contractType") {
					query["contract_type.planned"] = { $in: value };
				} else if (key === "education_level") {
					// For education type filters
					uniqueItemsConditions.push({
						$elemMatch: {
							item_name: "Education_Level",
							item_value: { $in: value },
						},
					});
				} else if (key.startsWith("unique_items.")) {
					// For unique_items fields, we need to search in the array
					const itemName = key.replace("unique_items.", "");
					uniqueItemsConditions.push({
						$elemMatch: {
							item_name: itemName,
							item_value: { $in: value },
						},
					});
				} else {
					query[key] = { $in: value };
				}
			}
			// Handle range filters (min/max)
			else if (
				typeof value === "object" &&
				value !== null &&
				("min" in value || "max" in value)
			) {
				const rangeValue = value as { min?: number; max?: number };
				const rangeQuery: any = {};
				if (rangeValue.min !== undefined && rangeValue.min !== null) {
					rangeQuery.$gte = rangeValue.min;
				}
				if (rangeValue.max !== undefined && rangeValue.max !== null) {
					rangeQuery.$lte = rangeValue.max;
				}
				if (Object.keys(rangeQuery).length > 0) {
					// Special handling for total_project_value which is nested
					if (key === "total_project_value" || key === "projectValue") {
						query["total_project_value.planned"] = rangeQuery;
					} else if (key.startsWith("unique_items.")) {
						// For unique_items fields, we need to search in the array
						const itemName = key.replace("unique_items.", "");
						uniqueItemsConditions.push({
							$elemMatch: {
								item_name: itemName,
								item_value: rangeQuery,
							},
						});
					} else {
						query[key] = rangeQuery;
					}
				}
			}
		}

		// Apply all unique_items conditions using $all
		if (uniqueItemsConditions.length > 0) {
			query.unique_items = { $all: uniqueItemsConditions };
		}

		return query;
	}

	// Get the benchmark data for the Gantt chart
	async getBenchmarkGanttData(filters: any): Promise<any> {
		try {
			const query = this.buildQuery(filters);

			// Use aggregation pipeline to calculate averages
			const result = await AccumulatedDurationModel.aggregate([
				// Match documents with filters and ensure stage_groups exists
				{
					$match: { ...query, "Derived Data.stage_groups": { $exists: true } },
				},

				// Convert stage_groups object to array for processing
				{
					$project: {
						stageArray: { $objectToArray: "$Derived Data.stage_groups" },
					},
				},

				// Unwind the array to process each stage separately
				{ $unwind: "$stageArray" },

				// Group by stage name and calculate averages
				{
					$group: {
						_id: "$stageArray.k",
						average_planned_duration: {
							$avg: {
								$cond: [
									{ $ne: ["$stageArray.v.planned_duration", null] },
									"$stageArray.v.planned_duration",
									null,
								],
							},
						},
						average_actual_duration: {
							$avg: {
								$cond: [
									{ $ne: ["$stageArray.v.actual_duration", null] },
									"$stageArray.v.actual_duration",
									null,
								],
							},
						},
					},
				},

				// Format the output
				{
					$project: {
						_id: 0,
						stage: "$_id",
						average_planned_duration: { $round: "$average_planned_duration" },
						average_actual_duration: { $round: "$average_actual_duration" },
					},
				},
			]).exec();

			// Calculate transition averages
			const transitionResult = await AccumulatedDurationModel.aggregate([
				// Match documents with filters and ensure transition_groups exists
				{
					$match: {
						...query,
						"Derived Data.transition_groups": { $exists: true },
					},
				},

				// Convert transition_groups object to array for processing
				{
					$project: {
						transitionArray: {
							$objectToArray: "$Derived Data.transition_groups",
						},
					},
				},

				// Unwind the array to process each transition separately
				{ $unwind: "$transitionArray" },

				// Group by transition name and calculate averages
				{
					$group: {
						_id: "$transitionArray.k",
						average_planned_gap: {
							$avg: {
								$cond: [
									{ $ne: ["$transitionArray.v.planned_gap", null] },
									"$transitionArray.v.planned_gap",
									null,
								],
							},
						},
						average_actual_gap: {
							$avg: {
								$cond: [
									{ $ne: ["$transitionArray.v.actual_gap", null] },
									"$transitionArray.v.actual_gap",
									null,
								],
							},
						},
						from_group: { $first: "$transitionArray.v.from_group" },
						to_group: { $first: "$transitionArray.v.to_group" },
					},
				},

				// Format the output
				{
					$project: {
						_id: 0,
						transition: "$_id",
						average_planned_gap: { $round: "$average_planned_gap" },
						average_actual_gap: { $round: "$average_actual_gap" },
						from_group: 1,
						to_group: 1,
					},
				},
			]).exec();

			// Get total count of projects analyzed
			const totalCount = await AccumulatedDurationModel.countDocuments({
				...query,
				"Derived Data.stage_groups": { $exists: true },
			});

			// Transform array result to object format and convert to months
			const stageBenchmarks: Record<string, any> = {};
			for (const item of result) {
				stageBenchmarks[item.stage] = {
					average_planned_duration: this.daysToMonths(
						item.average_planned_duration,
					),
					average_actual_duration: this.daysToMonths(
						item.average_actual_duration,
					),
				};
			}

			// Transform transition results to object format and convert to months
			const transitionBenchmarks: Record<string, any> = {};
			for (const item of transitionResult) {
				transitionBenchmarks[item.transition] = {
					average_planned_gap: this.daysToMonths(item.average_planned_gap),
					average_actual_gap: this.daysToMonths(item.average_actual_gap),
					from_group: item.from_group,
					to_group: item.to_group,
				};
			}

			return {
				total_projects_analyzed: totalCount,
				stage_benchmarks: stageBenchmarks,
				transition_benchmarks: transitionBenchmarks,
			};
		} catch (error) {
			throw new Error(`Failed to get benchmark data: ${error}`);
		}
	}

	// Get the benchmark data for the scatterplot
	async getBenchmarkScatterplotData(filters: any): Promise<any> {
		try {
			const query = this.buildQuery(filters);

			// First pass: Get projects with calculated durations
			const projects = await AccumulatedDurationModel.aggregate([
				// Match documents with filters and ensure stage_groups exists
				{
					$match: {
						...query,
						"Derived Data.stage_groups": { $exists: true },
					},
				},

				// Convert stage_groups object to array and extract project_name from unique_items
				{
					$project: {
						project_code: 1,
						project_sheet_name: 1,
						type: 1,
						sub_project_name: 1,
						"TBH Project Number": 1,
						"Total Project Value": 1,
						"TBH PM": 1,
						"Project City": 1,
						unique_items: 1,
						total_project_value_planned: "$total_project_value.planned",
						total_project_value_actual: "$total_project_value.actual",
						// Extract Project_Name from unique_items array
						project_name: {
							$let: {
								vars: {
									projectNameItem: {
										$arrayElemAt: [
											{
												$filter: {
													input: "$unique_items",
													cond: { $eq: ["$$this.item_name", "Project_Name"] },
												},
											},
											0,
										],
									},
								},
								in: "$$projectNameItem.item_value",
							},
						},
						stageArray: { $objectToArray: "$Derived Data.stage_groups" },
					},
				},

				// Unwind the stage array
				{ $unwind: "$stageArray" },

				// Group by project and sum durations, while preserving individual stage data
				{
					$group: {
						_id: {
							project_code: "$project_code",
							project_sheet_name: "$project_sheet_name",
							type: "$type",
							sub_project_name: "$sub_project_name",
							"TBH Project Number": "$TBH Project Number",
							"Total Project Value": "$Total Project Value",
							total_project_value_planned: "$total_project_value_planned",
							total_project_value_actual: "$total_project_value_actual",
							project_name: "$project_name",
							"TBH PM": "$TBH PM",
							"Project City": "$Project City",
						},
						total_planned_duration: {
							$sum: {
								$cond: [
									{ $ne: ["$stageArray.v.planned_duration", null] },
									"$stageArray.v.planned_duration",
									0,
								],
							},
						},
						total_actual_duration: {
							$sum: {
								$cond: [
									{ $ne: ["$stageArray.v.actual_duration", null] },
									"$stageArray.v.actual_duration",
									0,
								],
							},
						},
						stages: {
							$push: {
								stage_name: "$stageArray.k",
								planned_duration: "$stageArray.v.planned_duration",
								actual_duration: "$stageArray.v.actual_duration",
							},
						},
					},
				},

				// Reshape the output
				{
					$project: {
						_id: 0,
						project_code: "$_id.project_code",
						project_sheet_name: "$_id.project_sheet_name",
						type: "$_id.type",
						sub_project_name: "$_id.sub_project_name",
						"TBH Project Number": "$_id.TBH Project Number",
						"Total Project Value": "$_id.Total Project Value",
						total_project_value_planned: "$_id.total_project_value_planned",
						total_project_value_actual: "$_id.total_project_value_actual",
						project_name: "$_id.project_name",
						"TBH PM": "$_id.TBH PM",
						"Project City": "$_id.Project City",
						total_planned_duration: 1,
						total_actual_duration: 1,
						stage_durations: {
							$arrayToObject: {
								$map: {
									input: "$stages",
									as: "stage",
									in: {
										k: "$$stage.stage_name",
										v: {
											planned_duration: {
												$ifNull: ["$$stage.planned_duration", 0],
											},
											actual_duration: {
												$ifNull: ["$$stage.actual_duration", 0],
											},
										},
									},
								},
							},
						},
					},
				},

				// Sort by project code
				{ $sort: { project_code: 1 } },
			]).exec();

			// Second pass: Get unique_items data for each project
			const projectCodes = projects.map((p) => p.project_code);
			const uniqueItemsData = await AccumulatedDurationModel.find(
				{ project_code: { $in: projectCodes } },
				{ project_code: 1, unique_items: 1 },
			).lean();

			// Create a map for quick lookup
			const uniqueItemsMap = new Map(
				uniqueItemsData.map((doc) => [
					doc.project_code,
					doc.unique_items || [],
				]),
			);

			// Build type-specific field mappings from filter config
			const typeFieldsMap: Record<string, Set<string>> = {};
			for (const [typeKey, typeConfig] of Object.entries(
				filterConfig.dynamic_filters,
			)) {
				if (typeConfig.filters) {
					typeFieldsMap[typeKey] = new Set(
						typeConfig.filters.map((f) => f.item_name),
					);
				}
			}

			// Merge the data and include only type-specific fields
			const result = projects.map((project) => {
				const uniqueItems = uniqueItemsMap.get(project.project_code) || [];
				const typeFields = typeFieldsMap[project.type] || new Set();

				// Extract only the fields relevant to this project's type
				const dynamicFields: Record<string, any> = {};
				for (const item of uniqueItems) {
					if (item.item_name && typeFields.has(item.item_name)) {
						dynamicFields[item.item_name] = item.item_value;
					}
				}

				// Convert stage durations to months and flatten them
				const stageDurations: Record<string, number | null> = {};
				if (project.stage_durations) {
					for (const [stageName, durations] of Object.entries(
						project.stage_durations,
					)) {
						// Add planned duration for each stage
						stageDurations[`${stageName}_planned`] = this.daysToMonths(
							(durations as any).planned_duration || 0,
						);
						// Add actual duration for each stage
						stageDurations[`${stageName}_actual`] = this.daysToMonths(
							(durations as any).actual_duration || 0,
						);
					}
				}

				// Remove stage_durations from project before spreading
				const { stage_durations, ...projectWithoutStageDurations } = project;

				return {
					...projectWithoutStageDurations,
					total_planned_duration: this.daysToMonths(
						project.total_planned_duration,
					),
					total_actual_duration: this.daysToMonths(
						project.total_actual_duration,
					),
					...stageDurations,
					...dynamicFields,
				};
			});

			// Get total count
			const totalCount = await AccumulatedDurationModel.countDocuments({
				...query,
				"Derived Data.stage_groups": { $exists: true },
			});

			return {
				projects: result,
				total_projects: totalCount,
			};
		} catch (error) {
			throw new Error(`Failed to get scatterplot data: ${error}`);
		}
	}

	// Get the projects by type
	async getProjectsByType(type: string): Promise<any> {
		try {
			const query = { type };

			// Use aggregation pipeline to extract project_name from unique_items
			const projects = await AccumulatedDurationModel.aggregate([
				// Match documents by type
				{ $match: query },

				// Project fields and extract project_name from unique_items
				{
					$project: {
						_id: 0,
						project_code: 1,
						project_sheet_name: 1,
						sub_project_name: 1,
						// Extract Project_Name from unique_items array
						project_name: {
							$let: {
								vars: {
									projectNameItem: {
										$arrayElemAt: [
											{
												$filter: {
													input: "$unique_items",
													cond: { $eq: ["$$this.item_name", "Project_Name"] },
												},
											},
											0,
										],
									},
								},
								in: "$$projectNameItem.item_value",
							},
						},
					},
				},
			]).exec();

			return projects;
		} catch (error) {
			throw new Error(`Failed to get projects by type: ${error}`);
		}
	}
}
