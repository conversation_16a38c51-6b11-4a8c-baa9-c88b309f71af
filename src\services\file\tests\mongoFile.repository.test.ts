import { MongoFileRepository } from "../mongoFile.repository";
import { FileModel } from "../file.model";
import type { File } from "../file.model";
import { vi, describe, it, expect, beforeEach, afterEach } from "vitest";

// Create mock instance methods that we'll use in tests
const mockSave = vi.fn();
const mockToObject = vi.fn();

// Mock the FileModel
vi.mock("../file.model", () => {
	// Create a constructor function that can be instantiated with 'new'
	const MockFileModel = function(this: any, data: any) {
		Object.assign(this, data);
		this.save = mockSave;
		this.toObject = mockToObject;
		return this;
	} as any;

	// Add static methods to the constructor
	MockFileModel.find = vi.fn();
	MockFileModel.findById = vi.fn();
	MockFileModel.findOne = vi.fn();
	MockFileModel.findByIdAndDelete = vi.fn();
	MockFileModel.findByIdAndUpdate = vi.fn();
	MockFileModel.findOneAndUpdate = vi.fn();

	return {
		FileModel: MockFileModel
	};
});

describe("MongoFileRepository", () => {
	let repository: MongoFileRepository;
	const mockFileData = {
		userId: "user123",
		originalName: "test.jpg",
		storageName: "storage-test.jpg",
		size: 1024,
		mimeType: "image/jpeg",
		url: "http://example.com/test.jpg",
		metadata: {},
		isPrivate: false,
		sharedWith: [],
	};

	const mockFileWithId = {
		...mockFileData,
		id: "file123",
		createdAt: new Date(),
		updatedAt: new Date(),
	};

	beforeEach(() => {
		repository = new MongoFileRepository();
		vi.clearAllMocks();
	});

	afterEach(() => {
		vi.resetAllMocks();
	});

	describe("create", () => {
		it("should create a new file document", async () => {
			// Arrange
			// Configure mock functions for this test
			mockSave.mockResolvedValueOnce(undefined);
			mockToObject.mockReturnValueOnce(mockFileWithId);

			// Act
			const result = await repository.create(mockFileData);

			// Assert
			expect(mockSave).toHaveBeenCalled();
			expect(mockToObject).toHaveBeenCalled();
			expect(result).toEqual(mockFileWithId);
		});

		it("should throw an error if save fails", async () => {
			// Arrange
			const mockError = new Error("Database error");
			mockSave.mockRejectedValueOnce(mockError);

			// Act & Assert
			await expect(repository.create(mockFileData)).rejects.toThrow(mockError);
			expect(mockSave).toHaveBeenCalled();
		});
	});

	describe("findAll", () => {
		it("should find all files with default pagination", async () => {
			// Arrange
			const mockFiles = [mockFileWithId, { ...mockFileWithId, id: "file2" }];
			const mockSort = vi.fn().mockReturnThis();
			const mockSkip = vi.fn().mockReturnThis();
			const mockLimit = vi.fn().mockReturnThis();
			const mockLean = vi.fn().mockResolvedValue(mockFiles);

			vi.mocked(FileModel.find).mockReturnValue({
				sort: mockSort,
				skip: mockSkip,
				limit: mockLimit,
				lean: mockLean,
			} as any);

			// Act
			const result = await repository.findAll();

			// Assert
			expect(FileModel.find).toHaveBeenCalledWith({});
			expect(mockSort).toHaveBeenCalledWith({ createdAt: -1 });
			expect(mockSkip).toHaveBeenCalledWith(0);
			expect(mockLimit).toHaveBeenCalledWith(10);
			expect(result).toEqual(mockFiles);
		});

		it("should find files with userId filter and custom pagination", async () => {
			// Arrange
			const mockFiles = [mockFileWithId];
			const mockSort = vi.fn().mockReturnThis();
			const mockSkip = vi.fn().mockReturnThis();
			const mockLimit = vi.fn().mockReturnThis();
			const mockLean = vi.fn().mockResolvedValue(mockFiles);

			vi.mocked(FileModel.find).mockReturnValue({
				sort: mockSort,
				skip: mockSkip,
				limit: mockLimit,
				lean: mockLean,
			} as any);

			// Act
			const result = await repository.findAll({
				userId: "user123",
				limit: 5,
				offset: 10,
			});

			// Assert
			expect(FileModel.find).toHaveBeenCalledWith({ userId: "user123" });
			expect(mockSort).toHaveBeenCalledWith({ createdAt: -1 });
			expect(mockSkip).toHaveBeenCalledWith(10);
			expect(mockLimit).toHaveBeenCalledWith(5);
			expect(result).toEqual(mockFiles);
		});

		it("should handle error during find operation", async () => {
			// Arrange
			const mockError = new Error("Database error");
			vi.mocked(FileModel.find).mockImplementation(() => {
				throw mockError;
			});

			// Act & Assert
			await expect(repository.findAll()).rejects.toThrow(mockError);
		});
	});

	describe("findById", () => {
		it("should find a file by id", async () => {
			// Arrange
			const mockLean = vi.fn().mockResolvedValue(mockFileWithId);
			vi.mocked(FileModel.findById).mockReturnValue({
				lean: mockLean,
			} as any);

			// Act
			const result = await repository.findById("file123");

			// Assert
			expect(FileModel.findById).toHaveBeenCalledWith("file123");
			expect(mockLean).toHaveBeenCalled();
			expect(result).toEqual(mockFileWithId);
		});

		it("should return null when file not found", async () => {
			// Arrange
			const mockLean = vi.fn().mockResolvedValue(null);
			vi.mocked(FileModel.findById).mockReturnValue({
				lean: mockLean,
			} as any);

			// Act
			const result = await repository.findById("nonexistent");

			// Assert
			expect(FileModel.findById).toHaveBeenCalledWith("nonexistent");
			expect(mockLean).toHaveBeenCalled();
			expect(result).toBeNull();
		});

		it("should handle error during findById operation", async () => {
			// Arrange
			const mockError = new Error("Database error");
			vi.mocked(FileModel.findById).mockImplementation(() => {
				throw mockError;
			});

			// Act & Assert
			await expect(repository.findById("file123")).rejects.toThrow(mockError);
		});
	});

	describe("findByUserId", () => {
		it("should find files by userId", async () => {
			// Arrange
			const mockFiles = [mockFileWithId, { ...mockFileWithId, id: "file2" }];
			const mockLean = vi.fn().mockResolvedValue(mockFiles);
			vi.mocked(FileModel.find).mockReturnValue({
				lean: mockLean,
			} as any);

			// Act
			const result = await repository.findByUserId("user123");

			// Assert
			expect(FileModel.find).toHaveBeenCalledWith({ userId: "user123" });
			expect(mockLean).toHaveBeenCalled();
			expect(result).toEqual(mockFiles);
		});

		it("should return null when no files found for userId", async () => {
			// Arrange
			const mockLean = vi.fn().mockResolvedValue([]);
			vi.mocked(FileModel.find).mockReturnValue({
				lean: mockLean,
			} as any);

			// Act
			const result = await repository.findByUserId("nonexistent");

			// Assert
			expect(FileModel.find).toHaveBeenCalledWith({ userId: "nonexistent" });
			expect(mockLean).toHaveBeenCalled();
			expect(result).toEqual([]);
		});
	});

	describe("findByStorageName", () => {
		it("should find a file by storageName", async () => {
			// Arrange
			const mockLean = vi.fn().mockResolvedValue(mockFileWithId);
			vi.mocked(FileModel.findOne).mockReturnValue({
				lean: mockLean,
			} as any);

			// Act
			const result = await repository.findByStorageName("storage-test.jpg");

			// Assert
			expect(FileModel.findOne).toHaveBeenCalledWith({
				storageName: "storage-test.jpg",
			});
			expect(mockLean).toHaveBeenCalled();
			expect(result).toEqual(mockFileWithId);
		});

		it("should return null when file not found by storageName", async () => {
			// Arrange
			const mockLean = vi.fn().mockResolvedValue(null);
			vi.mocked(FileModel.findOne).mockReturnValue({
				lean: mockLean,
			} as any);

			// Act
			const result = await repository.findByStorageName("nonexistent.jpg");

			// Assert
			expect(FileModel.findOne).toHaveBeenCalledWith({
				storageName: "nonexistent.jpg",
			});
			expect(mockLean).toHaveBeenCalled();
			expect(result).toBeNull();
		});
	});

	describe("findByUrl", () => {
		it("should find a file by url", async () => {
			// Arrange
			const mockLean = vi.fn().mockResolvedValue(mockFileWithId);
			vi.mocked(FileModel.findOne).mockReturnValue({
				lean: mockLean,
			} as any);

			// Act
			const result = await repository.findByUrl("http://example.com/test.jpg");

			// Assert
			expect(FileModel.findOne).toHaveBeenCalledWith({
				url: "http://example.com/test.jpg",
			});
			expect(mockLean).toHaveBeenCalled();
			expect(result).toEqual(mockFileWithId);
		});

		it("should return null when file not found by url", async () => {
			// Arrange
			const mockLean = vi.fn().mockResolvedValue(null);
			vi.mocked(FileModel.findOne).mockReturnValue({
				lean: mockLean,
			} as any);

			// Act
			const result = await repository.findByUrl(
				"http://example.com/nonexistent.jpg",
			);

			// Assert
			expect(FileModel.findOne).toHaveBeenCalledWith({
				url: "http://example.com/nonexistent.jpg",
			});
			expect(mockLean).toHaveBeenCalled();
			expect(result).toBeNull();
		});
	});

	describe("deleteById", () => {
		it("should delete a file by id", async () => {
			// Arrange
			const mockLean = vi.fn().mockResolvedValue(mockFileWithId);
			vi.mocked(FileModel.findByIdAndDelete).mockReturnValue({
				lean: mockLean,
			} as any);

			// Act
			const result = await repository.deleteById("file123");

			// Assert
			expect(FileModel.findByIdAndDelete).toHaveBeenCalledWith("file123");
			expect(mockLean).toHaveBeenCalled();
			expect(result).toEqual(mockFileWithId);
		});

		it("should return null when file not found for deletion", async () => {
			// Arrange
			const mockLean = vi.fn().mockResolvedValue(null);
			vi.mocked(FileModel.findByIdAndDelete).mockReturnValue({
				lean: mockLean,
			} as any);

			// Act
			const result = await repository.deleteById("nonexistent");

			// Assert
			expect(FileModel.findByIdAndDelete).toHaveBeenCalledWith("nonexistent");
			expect(mockLean).toHaveBeenCalled();
			expect(result).toBeNull();
		});
	});

	describe("updateById", () => {
		it("should update a file by id", async () => {
			// Arrange
			const updateData: Partial<File> = {
				isPrivate: true,
				metadata: { updated: true },
			};
			const updatedFile = {
				...mockFileWithId,
				isPrivate: true,
				metadata: { updated: true },
			};

			const mockLean = vi.fn().mockResolvedValue(updatedFile);
			vi.mocked(FileModel.findByIdAndUpdate).mockReturnValue({
				lean: mockLean,
			} as any);

			// Act
			const result = await repository.updateById("file123", updateData);

			// Assert
			expect(FileModel.findByIdAndUpdate).toHaveBeenCalledWith(
				"file123",
				updateData,
				{
					new: true,
				},
			);
			expect(mockLean).toHaveBeenCalled();
			expect(result).toEqual(updatedFile);
		});

		it("should return null when file not found for update", async () => {
			// Arrange
			const updateData: Partial<File> = { isPrivate: true };
			const mockLean = vi.fn().mockResolvedValue(null);
			vi.mocked(FileModel.findByIdAndUpdate).mockReturnValue({
				lean: mockLean,
			} as any);

			// Act
			const result = await repository.updateById("nonexistent", updateData);

			// Assert
			expect(FileModel.findByIdAndUpdate).toHaveBeenCalledWith(
				"nonexistent",
				updateData,
				{
					new: true,
				},
			);
			expect(mockLean).toHaveBeenCalled();
			expect(result).toBeNull();
		});
	});

	describe("updateByUserId", () => {
		it("should update a file by userId", async () => {
			// Arrange
			const updateData: Partial<File> = {
				isPrivate: true,
				sharedWith: ["user456"],
			};
			const updatedFile = {
				...mockFileWithId,
				isPrivate: true,
				sharedWith: ["user456"],
			};

			const mockLean = vi.fn().mockResolvedValue(updatedFile);
			vi.mocked(FileModel.findOneAndUpdate).mockReturnValue({
				lean: mockLean,
			} as any);

			// Act
			const result = await repository.updateByUserId("user123", updateData);

			// Assert
			expect(FileModel.findOneAndUpdate).toHaveBeenCalledWith(
				{ userId: "user123" },
				updateData,
				{
					new: true,
				},
			);
			expect(mockLean).toHaveBeenCalled();
			expect(result).toEqual(updatedFile);
		});

		it("should return null when file not found for update by userId", async () => {
			// Arrange
			const updateData: Partial<File> = { isPrivate: true };
			const mockLean = vi.fn().mockResolvedValue(null);
			vi.mocked(FileModel.findOneAndUpdate).mockReturnValue({
				lean: mockLean,
			} as any);

			// Act
			const result = await repository.updateByUserId("nonexistent", updateData);

			// Assert
			expect(FileModel.findOneAndUpdate).toHaveBeenCalledWith(
				{ userId: "nonexistent" },
				updateData,
				{
					new: true,
				},
			);
			expect(mockLean).toHaveBeenCalled();
			expect(result).toBeNull();
		});
	});
});
