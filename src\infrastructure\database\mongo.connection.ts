import logger from "@/utils/logging";
import mongoose from "mongoose";
import { databaseManager } from "./database-manager";

export async function connectMongo(uri: string, connectionName = "default") {
	// Use DatabaseManager for named connections
	if (connectionName !== "default") {
		await databaseManager.addConnection(connectionName, uri);
		return;
	}
	
	// Keep default mongoose connection for backward compatibility
	if (mongoose.connection.readyState === 1) return; // Already connected
	await mongoose.connect(uri, {
		serverSelectionTimeoutMS: 5000,
		socketTimeoutMS: 45000,
	});
	// Extract host from URI for logging (without credentials)
	const hostMatch = uri.match(/mongodb(?:\+srv)?:\/\/(?:[^@]+@)?([^/]+)/);
	const host = hostMatch ? hostMatch[1] : "unknown";
	logger.info(`MongoDB connected to ${host}`);
}

export async function disconnectMongo() {
	// Disconnect all DatabaseManager connections
	await databaseManager.closeAllConnections();
	
	// Disconnect default mongoose connection
	if (mongoose.connection.readyState !== 0) {
		await mongoose.disconnect();
	}
}
