import logger from "@/utils/logging";
import type {
	CreatePermissionInput,
	CreateRoleInput,
	CreateUserInput,
	RoleResponse,
	StandardResponse,
	UserResponse,
	PaginatedUsersResponse,
	PaginationQuery,
	UpdateUserInput,
} from "@/controllers/users/users.schema";
import PermissionModel from "@/models/permission.model";
import RoleModel from "@/models/role.model";
import UserModel from "@/models/user.model";
// import logger from '@/utils/logging';

export class UserService {
	public async getUsers(paginationQuery?: PaginationQuery): Promise<PaginatedUsersResponse> {
		const page = paginationQuery?.page || 1;
		const limit = paginationQuery?.limit || 10;
		const sortBy = paginationQuery?.sortBy || 'createdAt';
		const sortOrder = paginationQuery?.sortOrder || 'desc';
		const search = paginationQuery?.search;
		const roleFilter = paginationQuery?.role;

		// Calculate skip value for pagination
		const skip = (page - 1) * limit;

		// Build query object for search and role filtering
		let query: any = { isDeleted: { $ne: true } }; // Exclude soft-deleted users
		const queryConditions = [];

		// Add search condition
		if (search) {
			queryConditions.push({
				azureAdObjectId: { $regex: search, $options: 'i' }
			});
		}

		// Add role filter condition
		if (roleFilter) {
			// First find the role by name to get its ObjectId
			const role = await RoleModel.findOne({ name: roleFilter }).lean();
			if (role) {
				queryConditions.push({
					roles: role._id
				});
			} else {
				// If role doesn't exist, return empty result
				return {
					users: [],
					pagination: {
						currentPage: page,
						totalPages: 0,
						totalCount: 0,
						limit,
						hasNextPage: false,
						hasPreviousPage: false,
					}
				};
			}
		}

		// Combine conditions with soft delete filter
		if (queryConditions.length > 0) {
			query = { $and: [{ isDeleted: { $ne: true } }, ...queryConditions] };
		}

		// Build sort object
		const sort: Record<string, 1 | -1> = { [sortBy]: sortOrder === 'asc' ? 1 : -1 };

		// Execute queries in parallel
		const [users, totalCount] = await Promise.all([
			UserModel.find(query)
				.populate('roles', 'name description permissions')
				.populate('assignedBy', 'azureAdObjectId')
				.sort(sort)
				.skip(skip)
				.limit(limit)
				.lean(),
			UserModel.countDocuments(query)
		]);

		// Calculate pagination metadata
		const totalPages = Math.ceil(totalCount / limit);
		const hasNextPage = page < totalPages;
		const hasPreviousPage = page > 1;

		const mappedUsers = users.map((user: any) => ({
			id: user._id?.toString(),
			azureAdObjectId: user.azureAdObjectId,
			roles: user.roles || [],
			assignedBy: user.assignedBy?.azureAdObjectId || user.assignedBy,
			createdAt: user.createdAt,
			updatedAt: user.updatedAt,
		}));

		return {
			users: mappedUsers,
			pagination: {
				currentPage: page,
				totalPages,
				totalCount,
				limit,
				hasNextPage,
				hasPreviousPage,
			}
		};
	}

	public async createUser(userData: CreateUserInput, assignedByAzureId: string): Promise<UserResponse> {
		// Check if user already exists
		const existingUser = await UserModel.findOne({
			azureAdObjectId: userData.azureAdObjectId,
		}).lean();
		if (existingUser) {
			throw new Error("User with this ID already exists");
		}

		// Find the assigning user's ObjectId from their Azure AD Object ID
		const assigningUser = await UserModel.findOne({
			azureAdObjectId: assignedByAzureId,
		}).lean();
		if (!assigningUser) {
			throw new Error("Assigning user not found in system");
		}

		// Validate role ObjectIds if provided
		if (userData.roles && userData.roles.length > 0) {
			const roles = await RoleModel.find({ _id: { $in: userData.roles } }).lean();
			const foundRoleIds = roles.map(role => role._id.toString());
			const missingRoles = userData.roles.filter(id => !foundRoleIds.includes(id));
			
			if (missingRoles.length > 0) {
				throw new Error(`The following role IDs do not exist: ${missingRoles.join(', ')}`);
			}
		}

		const newUser = await UserModel.create({
			azureAdObjectId: userData.azureAdObjectId,
			roles: userData.roles || [],
			assignedBy: assigningUser._id, // Use the ObjectId reference
		});

		return {
			azureAdObjectId: newUser.azureAdObjectId,
			roles: (newUser.roles as unknown as string[]) || [],
			assignedBy: newUser.assignedBy as unknown as string,
		};
	}

	public async getUser(azureAdObjectId: string): Promise<UserResponse> {
		logger.info(`Getting user ${azureAdObjectId}`);
		const user = await UserModel.findOne({ 
			azureAdObjectId, 
			isDeleted: { $ne: true } 
		}).lean();
		if (!user) {
			throw new Error("User not found");
		}
		return {
			azureAdObjectId: user.azureAdObjectId,
			roles: (user.roles as unknown as string[]) || [],
			assignedBy: user.assignedBy as unknown as string,
		};
	}

	public async createRole(roleData: CreateRoleInput): Promise<RoleResponse> {
		const newRole = await RoleModel.create(roleData);
		return {
			name: newRole.name,
		};
	}

	public async getRoles(azureAdObjectId: string): Promise<{ roles: string[] }> {
		const user = await UserModel.findOne({ 
			azureAdObjectId, 
			isDeleted: { $ne: true } 
		}).lean();
		if (!user) {
			logger.error(`User not found for ${azureAdObjectId}`);
			throw new Error("User not found");
		}
		const roles = await RoleModel.find({ _id: { $in: user.roles } }).lean();
		return {
			roles: roles.map((role) => role.name),
		};
	}

	public async getAllRoles(): Promise<any[]> {
		// Get all roles without populate first to see raw data
		const roles = await RoleModel.find().lean();

		// If roles have permissions, try to get permission details
		const rolePromises = roles.map(async (role) => {
			const permissionDetails = [];

			if (
				role.permissions &&
				Array.isArray(role.permissions) &&
				role.permissions.length > 0
			) {
				// Fetch permission details separately
				const permissions = await PermissionModel.find({
					_id: { $in: role.permissions },
				}).lean();

				for (const permission of permissions) {
					permissionDetails.push({
						id: permission._id.toString(),
						name: permission.name,
						description: permission.description,
					});
				}
			}

			return {
				id: role._id.toString(),
				name: role.name,
				description: role.description,
				permissions: permissionDetails,
			};
		});

		return Promise.all(rolePromises);
	}

	public async assignRole(
		azureAdObjectId: string,
		role: string,
	): Promise<UserResponse> {
		const user = await UserModel.findOneAndUpdate(
			{ azureAdObjectId, isDeleted: { $ne: true } },
			{ $addToSet: { roles: role } },
			{ new: true },
		).lean();
		if (!user) {
			throw new Error("User not found");
		}
		return {
			azureAdObjectId: user.azureAdObjectId,
			roles: (user.roles as unknown as string[]) || [],
			assignedBy: user.assignedBy as unknown as string,
		};
	}

	public async createPermission(
		permissionData: CreatePermissionInput,
	): Promise<StandardResponse> {
		const newPermission = await PermissionModel.create(permissionData);
		return {
			message: {
				name: newPermission.name,
				description: newPermission.description,
			},
		};
	}

	public async assignPermission(
		role: string,
		permission: string,
	): Promise<StandardResponse> {
		const roleData = await RoleModel.findOneAndUpdate(
			{ _id: role },
			{ $addToSet: { permissions: permission } },
			{ new: true },
		).lean();
		if (!roleData) {
			throw new Error("Role not found");
		}
		return {
			message: {
				name: roleData.name,
				description: roleData.description,
			},
		};
	}

	public async updateUser(
		azureAdObjectId: string,
		updateData: UpdateUserInput,
	): Promise<UserResponse> {
		// Validate that all role ObjectIds exist
		const roleIds = updateData.roles;
		const roles = await RoleModel.find({ _id: { $in: roleIds } }).lean();
		
		// Check if all provided role ObjectIds exist
		const foundRoleIds = roles.map(role => role._id.toString());
		const missingRoles = roleIds.filter(id => !foundRoleIds.includes(id));
		
		if (missingRoles.length > 0) {
			throw new Error(`The following role IDs do not exist: ${missingRoles.join(', ')}`);
		}

		// Update the user with new roles (replace all existing roles)
		const updatePayload: any = {
			roles: roleIds,
		};

		// Add assignedBy if provided
		if (updateData.assignedBy) {
			updatePayload.assignedBy = updateData.assignedBy;
		}

		const updatedUser = await UserModel.findOneAndUpdate(
			{ azureAdObjectId, isDeleted: { $ne: true } },
			updatePayload,
			{ new: true }
		).lean();

		if (!updatedUser) {
			throw new Error("User not found");
		}

		return {
			azureAdObjectId: updatedUser.azureAdObjectId,
			roles: (updatedUser.roles as unknown as string[]) || [],
			assignedBy: updatedUser.assignedBy as unknown as string,
		};
	}

	public async deleteUser(
		azureAdObjectId: string,
		deletedByAzureId: string,
	): Promise<{ message: string }> {
		// Find the user to delete
		const userToDelete = await UserModel.findOne({
			azureAdObjectId,
			isDeleted: { $ne: true }
		}).lean();
		
		if (!userToDelete) {
			throw new Error("User not found");
		}

		// Find the deleting user's ObjectId from their Azure AD Object ID
		const deletingUser = await UserModel.findOne({
			azureAdObjectId: deletedByAzureId,
			isDeleted: { $ne: true }
		}).lean();
		
		if (!deletingUser) {
			throw new Error("Deleting user not found in system");
		}

		// Perform soft delete
		await UserModel.findOneAndUpdate(
			{ azureAdObjectId, isDeleted: { $ne: true } },
			{
				isDeleted: true,
				deletedAt: new Date(),
				deletedBy: deletingUser._id,
			},
			{ new: true }
		);

		logger.info(`User ${azureAdObjectId} soft deleted by ${deletedByAzureId}`);
		
		return {
			message: "User deleted successfully"
		};
	}
}
