import type { Request, Response, NextFunction } from "express";
import type { AccumulatedDurationService } from "./accumulatedDuration.service";

export class AccumulatedDurationController {
	constructor(private accumulatedDurationService: AccumulatedDurationService) {}

	async getFilters(
		req: Request,
		res: Response,
		next: NextFunction,
	): Promise<void> {
		try {
			const filters = await this.accumulatedDurationService.getFilters();
			res.json(filters);
		} catch (error) {
			next(error);
		}
	}

	async searchProjects(
		req: Request,
		res: Response,
		next: NextFunction,
	): Promise<void> {
		try {
			const { 
				filters = {}, 
				page = 1, 
				limit = 20,
				search = "",
				sortBy = "project_code",
				sortOrder = "asc"
			} = req.body;
			const results = await this.accumulatedDurationService.searchProjects(
				filters,
				page,
				limit,
				search,
				sortBy,
				sortOrder,
			);
			res.json(results);
		} catch (error) {
			next(error);
		}
	}

	async getBenchmarkGanttData(
		req: Request,
		res: Response,
		next: NextFunction,
	): Promise<void> {
		try {
			const { filters = {}, project_selections, select_all, deselected_projects } = req.body;
			// Combine filters with project selections
			const combinedFilters = {
				...filters,
				...(project_selections && { project_selections }),
				...(select_all !== undefined && { select_all }),
				...(deselected_projects && { deselected_projects })
			};
			const benchmarkData =
				await this.accumulatedDurationService.getBenchmarkGanttData(combinedFilters);
			res.json(benchmarkData);
		} catch (error) {
			next(error);
		}
	}

	async getBenchmarkScatterplotData(
		req: Request,
		res: Response,
		next: NextFunction,
	): Promise<void> {
		try {
			const { filters = {}, project_selections, select_all, deselected_projects } = req.body;
			// Combine filters with project selections
			const combinedFilters = {
				...filters,
				...(project_selections && { project_selections }),
				...(select_all !== undefined && { select_all }),
				...(deselected_projects && { deselected_projects })
			};
			const scatterplotData =
				await this.accumulatedDurationService.getBenchmarkScatterplotData(combinedFilters);
			res.json(scatterplotData);
		} catch (error) {
			next(error);
		}
	}

	async getProjectsByType(
		req: Request,
		res: Response,
		next: NextFunction,
	): Promise<void> {
		try {
			const { type } = req.query;
			if (!type || typeof type !== 'string') {
				res.status(400).json({ error: 'Type parameter is required' });
				return;
			}
			const projects = await this.accumulatedDurationService.getProjectsByType(type);
			res.json(projects);
		} catch (error) {
			next(error);
		}
	}
}
