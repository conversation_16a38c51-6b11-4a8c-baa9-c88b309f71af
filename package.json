{"name": "<PERSON><PERSON>tein-backend", "version": "1.0.0", "description": "Backend for <PERSON>", "main": "dist/index.js", "scripts": {"start": "node -r tsconfig-paths/register dist/index.js", "dev": "cross-env NODE_ENV=development nodemon -r tsconfig-paths/register src/index.ts", "build": "rimraf dist && tsc -p tsconfig.build.json && tsc-alias -p tsconfig.build.json", "build:debug": "rimraf dist && tsc -p tsconfig.build.json --listFiles --listEmittedFiles && tsc-alias -p tsconfig.build.json", "build:verbose": "rimraf dist && tsc -p tsconfig.build.json --build --verbose && tsc-alias -p tsconfig.build.json", "lint": "biome check --write src/**/*.{js,ts}", "lint:fix": "biome check --write --fix src/**/*.{js,ts}", "format": "biome format --write src/**/*.{js,ts}", "test": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "seed": "ts-node -r tsconfig-paths/register src/scripts/seed/index.ts", "seed:stats": "ts-node -r tsconfig-paths/register src/scripts/seed/cli.ts stats", "seed:validate": "ts-node -r tsconfig-paths/register src/scripts/seed/cli.ts validate", "seed:export": "ts-node -r tsconfig-paths/register src/scripts/seed/cli.ts export", "seed:cleanup": "ts-node -r tsconfig-paths/register src/scripts/seed/cli.ts cleanup", "seed:dry-run": "ts-node -r tsconfig-paths/register src/scripts/seed/cli.ts dry-run", "seed:help": "ts-node -r tsconfig-paths/register src/scripts/seed/cli.ts help"}, "dependencies": {"@azure/msal-node": "3.2.2", "@azure/storage-blob": "12.26.0", "@elastic/elasticsearch": "^8.17.1", "@prisma/client": "^6.4.1", "@types/pg": "8.11.10", "cors": "2.8.5", "express": "4.18.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.5.0", "jsonwebtoken": "9.0.2", "multer": "1.4.5-lts.1", "pg": "8.13.1", "pg-pool": "3.7.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tsconfig-paths": "4.2.0", "uuid": "^11.1.0", "vite-tsconfig-paths": "^5.1.4", "winston": "3.17.0", "winston-daily-rotate-file": "5.0.0", "xlsx": "^0.18.5", "zod": "3.24.1"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@typegoose/typegoose": "12.15.1", "@types/cors": "2.8.17", "@types/express": "5.0.0", "@types/express-rate-limit": "^6.0.2", "@types/jest": "29.5.14", "@types/jsonwebtoken": "9.0.7", "@types/multer": "1.4.12", "@types/node": "22.9.1", "@types/supertest": "^6.0.2", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^10.0.0", "@types/xlsx": "^0.0.36", "@typescript-eslint/eslint-plugin": "5.0.0", "@typescript-eslint/parser": "5.0.0", "@vitest/coverage-v8": "3.1.3", "@vitest/ui": "3.1.3", "cross-env": "^7.0.3", "dotenv": "16.4.6", "eslint": "8.0.0", "eslint-config-prettier": "9.1.0", "get-port": "^7.1.0", "mongodb-memory-server": "^10.1.4", "mongoose": "8.14.1", "nodemon": "3.1.3", "prettier": "3.4.1", "prisma": "^6.4.1", "rimraf": "^5.0.0", "supertest": "^7.0.0", "ts-node": "10.9.2", "tsc-alias": "1.8.8", "typescript": "5.8.1-rc", "vitest": "3.1.3"}}