import { startServer } from '../../src/infrastructure/server';
import { Application } from 'express';
import { TestDatabase } from './test-db';
import logger from '../../src/utils/logging';
// import { initializeModels } from './init-models';
import mongoose from 'mongoose';
import { Server as HttpServer } from 'http';
import getPort from 'get-port';

// Import the MongoDB connection singleton from the main application
import { connectMongo } from '../../src/infrastructure/database/mongo.connection';
import app from '@/infrastructure/app';

export class TestServer {
  private static instance: Application | undefined;
  private static serverInstance: HttpServer | null = null;
  private static isInitialized = false;

  static async getTestServer(): Promise<Application> {
    if (!this.isInitialized) {
      try {
        // Initialize databases
        await TestDatabase.initializePostgres();

        // Ensure MongoDB is connected using the same singleton as the main app
        // This is critical for Typegoose to work correctly
        if (mongoose.connection.readyState !== 1) {
          // Force the singleton to initialize if not already done
          // Access the singleton to trigger initialization
          connectMongo(process.env.MONGODB_URI || 'mongodb://localhost:27017/test');
          // Wait for connection to be established
          await new Promise<void>((resolve) => {
            const checkConnection = () => {
              if (mongoose.connection.readyState === 1) {
                resolve();
              } else {
                setTimeout(checkConnection, 100);
              }
            };
            checkConnection();
          });
        }

        // Initialize models in the correct order
        // await initializeModels();

        // Get a random available port
        const randomPort = await getPort();

        // Create and configure test server with test logger
        const server = await startServer(randomPort);
        this.serverInstance = server as HttpServer;
        this.instance = app;
        this.isInitialized = true;

        logger.info(`Test server started on random port: ${randomPort}`);
      } catch (error) {
        console.error('Error initializing test server:', error);
        throw error;
      }
    }
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    return this.instance!;
  }

  static async cleanup(): Promise<void> {
    // Clear test data
    await TestDatabase.clearPostgresData();
    await TestDatabase.clearMongoData();
  }

  static async teardown(): Promise<void> {
    if (this.isInitialized) {
      // Close the HTTP server
      if (this.serverInstance) {
        await new Promise<void>((resolve) => {
          this.serverInstance?.close(() => {
            resolve();
          });
        });
        this.serverInstance = null;
      }
      await TestDatabase.closeConnections();
      this.instance = undefined;
      this.isInitialized = false;
    }
  }
}
