import "reflect-metadata";

/**
 * This module defines the data models for the methodology system.
 * It includes models for methodology nodes, file metadata, and expert information.
 * The models use Typegoose for type-safe MongoDB interactions.
 */

// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {
	type Ref,
	getModelForClass,
	modelOptions,
	prop,
} from "@typegoose/typegoose";
import { Types } from "mongoose";

// Create a class for Expert that can be used as a value
class Expert {
	/** The name of the expert */
	@prop({ type: () => String })
	name?: string;

	/** The email address of the expert */
	@prop({ type: () => String })
	email?: string;

	/** The professional title or role of the expert */
	@prop({ type: () => String })
	title?: string;
}

/**
 * Class representing metadata for files attached to methodology nodes
 * @class FileMetaData
 */
class FileMetaData {
	/** Unique identifier for the file */
	@prop({ required: true, type: () => Types.ObjectId })
	public fileId!: Types.ObjectId;

	/** Original name of the uploaded file */
	@prop({ required: true, type: () => String })
	public originalFileName!: string;

	/** Type of document - can be either 'framework', 'template', or 'example' */
	@prop({
		required: true,
		enum: ["framework", "template", "example"],
		type: () => String,
	})
	public docType!: string;

	/** URL where the file can be accessed */
	@prop({ required: true, type: () => String })
	public url!: string;
}

/**
 * Configuration for the MethodologyNode model
 * - Collection name: methodology_nodes
 * - Uses discriminatorKey 'nodeType' for node type differentiation
 * - Includes timestamps for document creation and updates
 * - Allows mixed types in the schema
 */
@modelOptions({
	schemaOptions: {
		collection: "methodology_nodes",
		discriminatorKey: "nodeType",
		timestamps: true,
	},
	options: {
		allowMixed: 0, // 0 = allow mixed types
	},
})
/**
 * Class representing a node in the methodology tree structure.
 * Each node can be either a regular node (with potential child nodes) or a step node (leaf node).
 * @class MethodologyNode
 */
class MethodologyNode {
	/** Unique name identifier for the node */
	@prop({ required: true, unique: true, type: () => String })
	public name!: string;

	/** Type of the node - either 'regular' or 'step' */
	@prop({ required: true, enum: ["regular", "step"], type: () => String })
	public nodeType!: string;

	/** Reference to the parent node. Null for root nodes */
	@prop({
		ref: () => MethodologyNode,
		default: null,
		type: () => Types.ObjectId,
	})
	public parentId?: Ref<MethodologyNode>;

	/** Array of files attached to this node */
	@prop({ type: () => [FileMetaData], default: [] })
	public files!: FileMetaData[];

	/** Order/position of the node among its siblings */
	@prop({ required: false, type: () => Number })
	public order?: number;

	/** Detailed description of the node's purpose or content */
	@prop({ required: false, type: () => String })
	public description?: string;

	/** Name for the next level in the hierarchy (for regular nodes) */
	@prop({ required: false, type: () => String })
	public nextLevelName?: string;

	/** Array of experts associated with this node */
	@prop({ required: false, type: () => [Expert] })
	public experts?: Expert[];
}

// Create and export the Mongoose model
const MethodologyNodeModel = getModelForClass(MethodologyNode);
export { MethodologyNodeModel, FileMetaData };
