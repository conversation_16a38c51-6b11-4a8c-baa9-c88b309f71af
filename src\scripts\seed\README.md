# Database Seeding System

This directory contains scripts to seed the Einstein database with initial permissions, roles, and users. The seeding system is **idempotent** - you can run it multiple times safely without creating duplicates.

## 📁 Structure

```
src/scripts/seed/
├── config.ts           # Configuration for all seeding data
├── seedPermissions.ts  # Seeds permissions
├── seedRoles.ts        # Seeds roles and assigns permissions  
├── seedUsers.ts        # Seeds users and assigns roles
├── index.ts            # Main orchestrator script
└── README.md           # This file
```

## 🚀 Quick Start

1. **Edit the configuration** in `config.ts` to match your needs
2. **Run the full seeding process**:
   ```bash
   yarn seed
   ```

## 📋 Configuration

Edit `src/scripts/seed/config.ts` to customize:

### Permissions
Add or modify permissions that define specific actions:
```typescript
{
  name: "create-projects",
  description: "Can create new projects"
}
```

### Roles  
Add or modify roles that group permissions:
```typescript
{
  name: "PROJECT_MANAGER", 
  description: "Can manage projects and team members",
  permissions: ["create-projects", "read-projects", "update-projects"]
}
```

### Users
Add users with their Azure AD Object IDs:
```typescript
{
  azureAdObjectId: "<EMAIL>", 
  roles: ["PROJECT_MANAGER"],
  assignedBy: "<EMAIL>"
}
```

### Options
Control the seeding behavior:
```typescript
export const seedOptions = {
  updateExisting: true,    // Update existing records
  createUsers: true,       // Create users (set false for roles/permissions only)
  verbose: true,           // Detailed logging
  recreate: false          // ⚠️ DANGER: Delete all existing data first
}
```

## 🔧 Available Scripts

### Run All Seeders
```bash
yarn seed
# or
npm run seed
```

### Run Individual Seeders
```bash
# Permissions only
yarn ts-node -r tsconfig-paths/register src/scripts/seed/seedPermissions.ts

# Roles only (requires permissions to exist)
yarn ts-node -r tsconfig-paths/register src/scripts/seed/seedRoles.ts

# Users only (requires roles to exist)
yarn ts-node -r tsconfig-paths/register src/scripts/seed/seedUsers.ts
```

## 📝 Common Use Cases

### 1. Initial Setup
First time setting up the database:
```typescript
// In config.ts
export const seedOptions = {
  updateExisting: true,
  createUsers: true,
  verbose: true,
  recreate: false
}
```

### 2. Update Permissions/Roles Only
Don't touch users, just update permissions and roles:
```typescript
export const seedOptions = {
  updateExisting: true,
  createUsers: false,  // Skip users
  verbose: true,
  recreate: false
}
```

### 3. Add New Users
After initial setup, add more users:
```typescript
// Add to seedConfig.users array, then run:
export const seedOptions = {
  updateExisting: true,
  createUsers: true,
  verbose: true,
  recreate: false
}
```

### 4. Clean Start (⚠️ DANGEROUS)
Delete everything and start fresh:
```typescript
export const seedOptions = {
  updateExisting: true,
  createUsers: true,
  verbose: true,
  recreate: true  // ⚠️ This will delete ALL existing data!
}
```

## 🔍 Verification

After seeding, verify the data using the API:

```bash
# Get all permissions
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:4000/api/einstein/users/permissions

# Get all roles with permissions
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:4000/api/einstein/users/roles/all

# Get all users
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:4000/api/einstein/users
```

## 🛡️ Safety Features

- **Idempotent**: Running multiple times won't create duplicates
- **Validation**: Checks for missing permissions/roles before creating relationships
- **Detailed Logging**: Shows exactly what was created/updated/skipped
- **Error Handling**: Continues processing even if individual items fail
- **Summary Reports**: Shows counts of operations performed

## 🚨 Important Notes

1. **Azure AD Object IDs**: Update the placeholder IDs in `config.ts` with real Azure AD Object IDs from your tenant

2. **User Dependencies**: Users can only be created if:
   - The referenced roles exist
   - The `assignedBy` user exists (if not "system")

3. **Role Dependencies**: Roles can only be created if:
   - All referenced permissions exist

4. **Environment**: The seeding uses the MongoDB connection configured in your environment

## 🔄 Example Workflow

1. **Initial setup**:
   ```bash
   # Edit config.ts with your permissions, roles, and placeholder users
   yarn seed
   ```

2. **Add real users**:
   ```typescript
   // In config.ts, replace placeholder Azure AD Object IDs
   users: [
     {
       azureAdObjectId: "<EMAIL>", // Real Azure AD ID
       roles: ["ADMIN"],
       assignedBy: "system"
     }
   ]
   ```

3. **Re-run to update**:
   ```bash
   yarn seed
   ```

4. **Add more roles/permissions**:
   ```typescript
   // Add to permissions or roles arrays in config.ts
   ```

5. **Update existing data**:
   ```bash
   yarn seed  # Will update existing records with new data
   ```

## 🆘 Troubleshooting

### "Permission not found" errors
- Ensure permissions are seeded before roles
- Check permission names match exactly (case-sensitive)

### "Role not found" errors  
- Ensure roles are seeded before users
- Check role names match exactly (case-sensitive)

### "AssignedBy user not found" warnings
- The user referenced in `assignedBy` must exist first
- Use "system" for system-assigned roles
- Seed admin users first, then regular users

### Connection errors
- Ensure MongoDB is running
- Check your `.env` configuration
- Verify DATABASE_URL or MONGO_URI is correct 