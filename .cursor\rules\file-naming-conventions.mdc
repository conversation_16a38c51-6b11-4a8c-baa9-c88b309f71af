---
description: 
globs: 
alwaysApply: true
---
# File Naming Conventions

This project follows consistent file naming conventions to improve code navigation and readability.

## TypeScript Files

All TypeScript files use the `.ts` extension with the following patterns:

1. **Domain-specific files**: Named with `domain.type.ts` format
   - Example: `file.service.ts`, `user.controller.ts`, `project.model.ts`

2. **Implementation-specific files**: Named with `implementationDomain.type.ts` format
   - Example: `mongoFile.repository.ts`, `azureBlob.service.ts`

3. **Utility/Helper files**: Named with `descriptive-name.ts` format
   - Example: `logging.ts`, `http.exception.ts` 

## Models

Model files follow the convention:
- `domain.model.ts` - Defines the domain entity and MongoDB schema using Typegoose

## Repositories

Repository files follow these conventions:
- `domain.repository.ts` - Defines the repository interface
- `mongoDomain.repository.ts` - MongoDB implementation of the repository interface
- Other implementation formats: `[technology/db]Domain.repository.ts`

## Services

Service files follow these conventions:
- `domain.service.ts` - Contains business logic for the domain
- `[provider]Domain.service.ts` - Implementation using specific provider/technology

## Controllers and Routes

- `domain.controller.ts` - HTTP controller handling requests and responses
- `domain.routes.ts` - Route definitions for the domain API endpoints

