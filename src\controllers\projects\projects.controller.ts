import type { ProjectRepository } from "@/services/repositories/ProjectRepository";
import type { Request, Response, NextFunction, RequestHandler } from "express";
import { ZodError } from "zod";
import type { Project } from "@/models/project.model";
import logger from "@/utils/logging";
import {
	getMarketSharePercentagesSchema,
	getProjectByIdSchema,
	getProjectsSchema,
	searchItemFilterSchema,
	getFilteredProjectsSchema,
} from "./projects.schema";
import type {
	FilterMapping,
	PaginatedResult,
} from "@/services/repositories/types";
import type { GetProjectByIdSchema } from "./projects.schema";

export const createProjectsController = (
	projectService: ProjectRepository,
) => {
	const getProjects: RequestHandler = async (
		req: Request<object, object, object, { page?: string; limit?: string }>,
		res: Response<
			PaginatedResult<Project> | { error: string; details?: string }
		>,
		next: NextFunction,
	) => {
		try {
			const { page, limit } = getProjectsSchema.parse(req.query);
			const result = await projectService.getProjects(page, limit);
			res.json(result);
		} catch (error) {
			if (error instanceof ZodError) {
				res.status(400).json({
					error: "Validation error",
					details: error.message,
				});
				return;
			}
			next(error);
		}
	};

	const getProjectById: RequestHandler<GetProjectByIdSchema> = async (
		req,
		res,
	) => {
		try {
			const { projectId } = getProjectByIdSchema.parse(req.params);
			const project = await projectService.getProjectById(projectId);
			if (!project) {
				res.status(404).json({ error: "Project not found" });
				return;
			}
			res.json(project);
		} catch (error) {
			if (error instanceof ZodError) {
				res.status(400).json({
					error: "Validation error",
					details: error.message,
				});
				return;
			}
		}
	};

	const getAvailableProjectFilters: RequestHandler = async (_req, res) => {
		const filters = await projectService.getAvailableProjectFilters();
		res.json(filters);
	};

	const searchItemFilter: RequestHandler = async (req, res) => {
		try {
			const { fieldName, page, limit, queryTerm } =
				searchItemFilterSchema.parse(req.query);
			const filters = await projectService.searchItemFilter({
				fieldName,
				page,
				limit,
				queryTerm,
			});
			res.json(filters);
		} catch (error) {
			if (error instanceof ZodError) {
				res
					.status(400)
					.json({ error: "Validation error", details: error.message });
				return;
			}
		}
	};

	const getFilteredProjects: RequestHandler = async (req, res, next) => {
		try {
			// Get pagination parameters from either query params or body
			let pageParam = 1;
			let limitParam = 10;

			// Check query params first
			if (req.query.page || req.query.limit) {
				const queryParams = getFilteredProjectsSchema.parse(req.query);
				pageParam = queryParams.page;
				limitParam = queryParams.limit;
			}

			// Handle both formats for filters and check for pagination in body
			let filtersObj = {};
			let bodyPage: number | undefined;
			let bodyLimit: number | undefined;

			// First try to parse with the schema
			try {
				const parsed = getFilteredProjectsSchema.parse(req.body);

				// Check if we have filters.filters structure (nested filters)
				if (
					parsed.filters &&
					typeof parsed.filters === "object" &&
					"filters" in parsed.filters
				) {
					filtersObj = parsed.filters.filters || {};
				} else {
					filtersObj = parsed.filters || {};
				}

				// If pagination is in the body, use those values
				if (parsed.page) bodyPage = parsed.page;
				if (parsed.limit) bodyLimit = parsed.limit;
			} catch (e) {
				// If schema validation fails, check if body has direct filters
				if (req.body && typeof req.body === "object") {
					// Extract potential pagination params from body
					if ("page" in req.body) bodyPage = Number(req.body.page);
					if ("limit" in req.body) bodyLimit = Number(req.body.limit);

					// Check for nested filters structure
					if (
						"filters" in req.body &&
						typeof req.body.filters === "object" &&
						"filters" in req.body.filters
					) {
						filtersObj = req.body.filters.filters || {};
					} else {
						// Extract filters - remove pagination fields if present
						const { page, limit, ...restOfBody } = req.body;
						filtersObj = restOfBody;
					}
				}
			}

			// Body pagination params override query params if present
			const page = bodyPage || pageParam;
			const limit = bodyLimit || limitParam;


			// Check if filtersObj is empty but we have nested filters
			if (Object.keys(filtersObj).length === 0) {
				logger.warn(
					"Empty filters object detected. Trying alternative extraction methods.",
				);

				// Case 1: Check for nested filters structure
				if (req.body?.filters && typeof req.body.filters === "object") {

					// Try to extract filters directly from the body as a last resort
					if (typeof req.body.filters === "object") {
						if (
							"filters" in req.body.filters &&
							typeof req.body.filters.filters === "object"
						) {
							filtersObj = req.body.filters.filters;
						} else {
							filtersObj = req.body.filters;
						}
					}
				}
				// Case 2: Check if the body itself contains filter keys (like industry)
				else if (req.body && typeof req.body === "object") {
					const knownFilterKeys = [
						"industry",
						"serviceOffering",
						"city",
						"customername",
						"projectname",
						"plannedRevenue",
						"status",
						"gfa",
						"projectManager",
						"projectDirector",
					];

					// Check if any known filter keys exist directly in the body
					const hasDirectFilters = knownFilterKeys.some(
						(key) => key in req.body,
					);

					if (hasDirectFilters) {
						// Extract only the known filter keys
						const extractedFilters: Record<string, unknown> = {};
						for (const key of knownFilterKeys) {
							if (key in req.body) {
								extractedFilters[key] = req.body[key];
							}
						}
						filtersObj = extractedFilters;
					}
				}

			}

			// Transform filters if they contain objects with value/label properties
			const transformedFilters = Object.fromEntries(
				Object.entries(filtersObj).map(([key, value]) => [
					key,
					Array.isArray(value)
						? value.map((v) => (typeof v === "string" ? v : v.value))
						: value,
				]),
			) as Partial<Record<keyof FilterMapping, string[]>>;



			const paginatedProjects = await projectService.getProjectsByFilters(
				transformedFilters,
				page,
				limit,
			);
			res.json(paginatedProjects);
		} catch (error) {
			if (error instanceof ZodError) {
				res.status(400).json({
					error: "Validation error",
					details: error.message,
				});
				return;
			}
			logger.error("Error in getFilteredProjects:", { error });
			next(error);
		}
	};

	const getMarketSharePercentages: RequestHandler = async (req, res, next) => {
		try {
			const { filters = {}, deselectedRows } =
				getMarketSharePercentagesSchema.parse(req.body);
			const transformedFilters = Object.fromEntries(
				Object.entries(filters).map(([key, value]) => [
					key,
					Array.isArray(value)
						? value.map((v) => (typeof v === "string" ? v : v.value))
						: value,
				]),
			) as Partial<Record<keyof FilterMapping, string[]>>;

			const percentages = await projectService.getPercentageByMarketShare(
				transformedFilters,
				deselectedRows,
			);
			res.json(percentages);
		} catch (error) {
			next(error);
		}
	};








	/**
	 * Downloads filtered projects as an Excel file
	 */
	const downloadFilteredProjects: RequestHandler = async (req, res, next) => {
		try {
			// Handle both formats for filters similar to getFilteredProjects
			let filtersObj = {};

			// First try to parse with the schema
			try {
				const parsed = getFilteredProjectsSchema.parse(req.body);

				// Check if we have filters.filters structure (nested filters)
				if (
					parsed.filters &&
					typeof parsed.filters === "object" &&
					"filters" in parsed.filters
				) {
					filtersObj = parsed.filters.filters || {};
				} else {
					filtersObj = parsed.filters || {};
				}
			} catch (e) {
				// If schema validation fails, check if body has direct filters
				if (req.body && typeof req.body === "object") {
					// Check for nested filters structure
					if (
						"filters" in req.body &&
						typeof req.body.filters === "object" &&
						"filters" in req.body.filters
					) {
						filtersObj = req.body.filters.filters || {};
					} else {
						// Extract filters - remove pagination fields if present
						const { page: _page, limit: _limit, ...restOfBody } = req.body;
						filtersObj = restOfBody;
					}
				}
			}


			// Check if filtersObj is empty but we have nested filters
			if (Object.keys(filtersObj).length === 0) {
				logger.warn(
					"Empty filters object detected. Trying alternative extraction methods.",
				);

				// Case 1: Check for nested filters structure
				if (req.body?.filters && typeof req.body.filters === "object") {

					// Try to extract filters directly from the body as a last resort
					if (typeof req.body.filters === "object") {
						if (
							"filters" in req.body.filters &&
							typeof req.body.filters.filters === "object"
						) {
							filtersObj = req.body.filters.filters;
						} else {
							filtersObj = req.body.filters;
						}
					}
				}
				// Case 2: Check if the body itself contains filter keys (like industry)
				else if (req.body && typeof req.body === "object") {
					const knownFilterKeys = [
						"industry",
						"serviceOffering",
						"city",
						"customername",
						"projectname",
						"plannedRevenue",
						"status",
						"gfa",
					];

					// Check if any known filter keys exist directly in the body
					const hasDirectFilters = knownFilterKeys.some(
						(key) => key in req.body,
					);

					if (hasDirectFilters) {
						// Extract only the known filter keys
						const extractedFilters: Record<string, unknown> = {};
						for (const key of knownFilterKeys) {
							if (key in req.body) {
								extractedFilters[key] = req.body[key];
							}
						}
						filtersObj = extractedFilters;
					}
				}

			}

			// Transform filters if they contain objects with value/label properties
			const transformedFilters = Object.fromEntries(
				Object.entries(filtersObj).map(([key, value]) => [
					key,
					Array.isArray(value)
						? value.map((v) => (typeof v === "string" ? v : v.value))
						: value,
				]),
			) as Partial<Record<keyof FilterMapping, string[]>>;

			// Get the Excel file data
			const { filename, data } =
				await projectService.downloadFilteredProjects(transformedFilters);

			// Set headers for file download
			res.setHeader(
				"Content-Disposition",
				`attachment; filename="${filename}"`,
			);
			res.setHeader(
				"Content-Type",
				"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
			);
			res.send(data);
		} catch (error) {
			if (error instanceof ZodError) {
				res.status(400).json({
					error: "Validation error",
					details: error.message,
				});
				return;
			}
			logger.error("Error in downloadFilteredProjects:", { error });
			next(error);
		}
	};

	return {
		getProjects,
		getProjectById,
		getAvailableProjectFilters,
		searchItemFilter,
		getFilteredProjects,
		getMarketSharePercentages,
		downloadFilteredProjects,
	};
};

export type ProjectsController = ReturnType<typeof createProjectsController>;
