import type { Document } from "mongoose";

/**
 * Filter input parameters for filtering map projects
 */
export type FilterInput = Partial<{
	company: string[];
	city: string[];
	industry: string[];
	serviceOffering: string[];
	plannedRevenue: string[];
	status: string[];
	projectname: string[];
	customername: string[];
	projectManager: string[];
	projectDirector: string[];
	neLat: number;
	neLng: number;
	swLat: number;
	swLng: number;
}>;

/**
 * DTO for filtered project locations
 */
export interface ProjectLocationDTO {
	projectId: string;
	industry?: string;
	latitude: number;
	longitude: number;
	projectname?: string;
	projectdirector?: string;
	customername?: string;
}

/**
 * DTO for detailed project view
 */
export interface ProjectDetailDTO {
	projectId: string;
	company?: string;
	profitCenter?: string;
	city?: string;
	projectName?: string;
	projectDirector?: string;
	projectManager?: string;
	plannedRevenue?: number;
	totalRevenues?: number;
	customerName?: string;
	contactPerson?: string;
	serviceOffering?: string;
	industry?: string;
	clientType?: string;
	projectType?: string;
	activeDate?: Date;
	estimatedProjectValue?: number;
	salesCurrency?: string;
	region?: string;
	startDate?: Date;
	endDate?: Date;
	latitude?: number;
	longitude?: number;
	address?: string;
	formattedAddress?: string;
	location?: { type: string; coordinates: number[] };
	jobSheetLink?: string;
}

/**
 * Helper type for Mongoose document projection with proper typing
 */
export interface ProjectDocument extends Document {
	projectid: string;
	company?: string;
	location?: string;
	projectname?: string;
	projectdirector?: string;
	projectmanager?: string;
	customername?: string;
	primarymarket?: string;
	servicename?: string;
	estconsfees?: number;
	estprojectvalue?: number;
	contactperson?: string;
	salescurrency?: string;
	profitcentre?: string;
	totalrevenues?: number;
	clienttype?: string;
	projecttype?: string;
	startdate?: string | Date;
	enddate?: string | Date;
	activedate?: string | Date;
	latlng?: {
		type: string;
		coordinates: number[];
	} | null;
	jobSheetLink?: string | null;
}

/**
 * Response type for available filters
 */
export interface FiltersResponse {
	count: number;
	company: string[];
	city: string[];
	industry: string[];
	serviceOffering: string[];
	plannedRevenue: { value: string; label: string }[];
	status: string[];
	projectManager: string[];
	projectDirector: string[];
}

/**
 * Response type for file download
 */
export interface DownloadResponse {
	filename: string;
	data: Buffer;
}

/**
 * Type for CSV row data in downloads
 */
export interface CsvRowData {
	projectId: string;
	projectName: string;
	customerName: string;
	city: string;
	industry: string;
	serviceOffering: string;
	plannedRevenue: number | string;
	projectDirector: string;
	projectManager: string;
	startDate: string;
	endDate: string;
	[key: string]: string | number | undefined;
}
