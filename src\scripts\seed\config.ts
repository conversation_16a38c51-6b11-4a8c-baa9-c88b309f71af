/**
 * Seeding Configuration
 *
 * This file contains all the data that will be seeded into the database.
 * You can modify this configuration and re-run the seed script to update the database.
 *
 * The seeding process is idempotent - you can run it multiple times safely.
 */

export interface SeedPermission {
	name: string;
	description: string;
}

export interface SeedRole {
	name: string;
	description: string;
	permissions: string[]; // Permission names
}

export interface SeedUser {
	azureAdObjectId: string;
	roles: string[]; // Role names
	assignedBy?: string; // Azure AD Object ID of the user who assigned roles
}

export const seedConfig = {
	/**
	 * Base permissions that define specific actions in the system
	 */
	permissions: [
		// User Management
		{
			name: "manage-users",
			description: "Can create, update, and delete users",
		},
		{
			name: "assign-roles",
			description: "Can assign and remove roles from users",
		},

		// Role Management
		{
			name: "manage-roles",
			description: "Can create, update, and delete roles",
		},

		// Permission Management
		{
			name: "manage-permissions",
			description: "Can create, update, and delete permissions",
		},
		// Einstein Admin User
		{
			name: "einstein-admin",
			description: "Have Access to Einstein Admin Section",
		},
	] as SeedPermission[],

	/**
	 * Roles that group permissions together
	 */
	roles: [
		// System Admin
		{
			name: "SYSTEM_ADMIN",
			description: "Full system administrator with all privileges",
			permissions: [
				"manage-users",
				"manage-roles",
				"manage-permissions",
				"einstein-admin",
				"assign-roles",
			],
		},
		// Einstein Admin
		{
			name: "ADMIN",
			description: "Full system administrator with all privileges",
			permissions: ["einstein-admin"],
		},
		{
			name: "USER",
			description: "Basic user with read access and feedback capabilities",
			permissions: [],
		},
	] as SeedRole[],

	/**
	 * Initial users to create in the system
	 * Add your Azure AD Object IDs here
	 */
	users: [
		{
			azureAdObjectId: "<EMAIL>",
			roles: ["SYSTEM_ADMIN"],
			assignedBy: "system seed",
		},
		{
			azureAdObjectId: "<EMAIL>",
			roles: ["SYSTEM_ADMIN"],
			assignedBy: "system seed",
		},
	] as SeedUser[],
};

/**
 * Configuration options for the seeding process
 */
export const seedOptions = {
	// Whether to update existing records or skip them
	updateExisting: true,

	// Whether to create users (set to false if you only want roles/permissions)
	createUsers: true,

	// Whether to log detailed information during seeding
	verbose: true,

	// Whether to remove existing data before seeding (DANGEROUS!)
	recreate: false,

	// Dry run mode - preview changes without making any database modifications
	dryRun: false,
};
