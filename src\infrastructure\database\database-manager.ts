import mongoose, { type Connection } from "mongoose";
import logger from "@/utils/logging";

interface DatabaseConnection {
	name: string;
	uri: string;
	connection?: Connection;
}

export class DatabaseManager {
	private connections: Map<string, DatabaseConnection> = new Map();
	private static instance: DatabaseManager;

	private constructor() {}

	public static getInstance(): DatabaseManager {
		if (!DatabaseManager.instance) {
			DatabaseManager.instance = new DatabaseManager();
		}
		return DatabaseManager.instance;
	}

	public async addConnection(name: string, uri: string): Promise<Connection> {
		if (this.connections.has(name)) {
			const existing = this.connections.get(name);
			if (existing?.connection) {
				return existing.connection;
			}
		}

		try {
			const connection = await mongoose.createConnection(uri).asPromise();
			this.connections.set(name, { name, uri, connection });
			logger.info(`MongoDB connection '${name}' established`);
			return connection;
		} catch (error) {
			logger.error(`Failed to connect to MongoDB '${name}':`, error);
			throw error;
		}
	}

	public getConnection(name: string): Connection {
		const conn = this.connections.get(name);
		if (!conn?.connection) {
			throw new Error(`Database connection '${name}' not found or not established`);
		}
		return conn.connection;
	}

	public async closeConnection(name: string): Promise<void> {
		const conn = this.connections.get(name);
		if (conn?.connection) {
			await conn.connection.close();
			this.connections.delete(name);
			logger.info(`MongoDB connection '${name}' closed`);
		}
	}

	public async closeAllConnections(): Promise<void> {
		const promises = Array.from(this.connections.keys()).map(name => 
			this.closeConnection(name)
		);
		await Promise.all(promises);
	}

	public isConnected(name: string): boolean {
		const conn = this.connections.get(name);
		return conn?.connection?.readyState === 1;
	}

	public getAllConnections(): string[] {
		return Array.from(this.connections.keys());
	}
}

export const databaseManager = DatabaseManager.getInstance();