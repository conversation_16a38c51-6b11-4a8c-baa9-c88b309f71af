import logger from "@/utils/logging";
import type {
	FilterInput,
	ProjectLocationDTO,
	ProjectDetailDTO,
	FiltersResponse,
	DownloadResponse,
	ProjectDocument,
	CsvRowData,
} from "./map.types";
import { RESULTS_LIMIT, CITY_COORDINATES } from "./map.constants";
import type { MongoMapRepository } from "@/services/repositories/mongoMap.repository";
import * as XLSX from "xlsx";

export class MapService {
	constructor(private mapRepository: MongoMapRepository) {}

	/**
	 * Builds the MongoDB query object based on the provided filters.
	 * @param filters - The filter criteria.
	 * @returns A MongoDB query object.
	 */
	private _buildFilterQuery(filters: FilterInput): Record<string, unknown> {
		const query: Record<string, unknown> = {};
		const andConditions: Record<string, unknown>[] = [];

		// --- Check for non-bounding-box filters ---
		const hasOtherFilters =
			(filters.company && filters.company.length > 0) ||
			(filters.city && filters.city.length > 0) ||
			(filters.industry && filters.industry.length > 0) ||
			(filters.serviceOffering && filters.serviceOffering.length > 0) ||
			(filters.plannedRevenue && filters.plannedRevenue.length > 0) ||
			(filters.status && filters.status.length > 0) ||
			(filters.projectname && filters.projectname.length > 0) ||
			(filters.customername && filters.customername.length > 0) ||
			(filters.projectManager && filters.projectManager.length > 0) ||
			(filters.projectDirector && filters.projectDirector.length > 0);

		// --- Geospatial Filter Logic ---
		let geospatialFilterSet = false;

		// Priority 1: City Proximity Search
		if (filters.city && filters.city.length > 0) {
			const targetCityCode = filters.city[0]; // Using the first city code in the array for proximity
			const cityData = CITY_COORDINATES.find((c) => c.city === targetCityCode);

			if (cityData) {
				const [lng, lat] = [cityData.coordinates.lng, cityData.coordinates.lat];
				const radiusInKm = 500;
				const earthRadiusKm = 6371; // Earth's radius in kilometers
				const radiusInRadians = radiusInKm / earthRadiusKm;

				// logger.debug(
				//   `Applying proximity search for city: ${targetCityCode} within ${radiusInKm}km radius.`,
				//   { coordinates: [lng, lat] }
				// );
				query.latlng = {
					$geoWithin: {
						$centerSphere: [
							[lng, lat], // city coordinates [lng, lat]
							radiusInRadians, // radius in radians
						],
					},
				};
				geospatialFilterSet = true;
			} else {
				logger.warn(
					`City code '${targetCityCode}' provided in filters.city not found in CITY_COORDINATES. Proximity search not applied for this city. Falling back to other geo filters or default.`,
				);
			}
		}

		// Priority 2: Bounding Box (applied only if city proximity didn't take over AND no other general filters are set)
		const hasBoundingBox =
			filters.neLat !== undefined &&
			filters.neLng !== undefined &&
			filters.swLat !== undefined &&
			filters.swLng !== undefined;

		if (!geospatialFilterSet && hasBoundingBox && !hasOtherFilters) {
			// logger.debug(
			//   'Applying bounding box filter (city proximity not applied/matched, and no other general filters active).'
			// );
			// We've already checked that these values are defined in hasBoundingBox
			const swLng = filters.swLng as number;
			const swLat = filters.swLat as number;
			const neLng = filters.neLng as number;
			const neLat = filters.neLat as number;

			// Check if the bounding box crosses the International Date Line (IDL)
			if (neLng < swLng) {
				logger.info(
					"Detected bounding box crossing the International Date Line",
					{
						swLng,
						swLat,
						neLng,
						neLat,
					},
				);
				// Don't set latlng for IDL crossing case, use $or directly
				query.$or = [
					{
						latlng: {
							$geoWithin: {
								$box: [
									[swLng, swLat],
									[180, neLat],
								],
							},
						},
					},
					{
						latlng: {
							$geoWithin: {
								$box: [
									[-180, swLat],
									[neLng, neLat],
								],
							},
						},
					},
				];
			} else {
				// Normal case (no date line crossing)
				query.latlng = {
					$geoWithin: {
						$box: [
							[swLng, swLat],
							[neLng, neLat],
						],
					},
				};
			}
			geospatialFilterSet = true; // Mark that a specific geo filter is now set by bounding box
		}

		// Priority 3: Default Geospatial Requirement (if no specific geo filter was applied yet)
		if (!geospatialFilterSet) {
			logger.info(
				"No specific geo filter (city proximity/bounding box) applied. Using default: latlng must exist.",
			);
			query.latlng = { $exists: true, $ne: null };
		}

		// --- Standard Field Filters ($in) ---
		// These are applied regardless of the geospatial logic above
		if (filters?.company?.length) {
			query.company = { $in: filters.company };
		}
		if (filters?.city?.length) {
			// Assuming 'location' field stores the city name in ProjectModel
			query.location = { $in: filters.city };
		}
		if (filters?.industry?.length) {
			query.primarymarket = { $in: filters.industry };
		}
		if (filters?.serviceOffering?.length) {
			query.servicename = { $in: filters.serviceOffering };
		}
		if (filters?.projectname?.length) {
			query.projectname = { $in: filters.projectname };
		}
		if (filters?.customername?.length) {
			query.customername = { $in: filters.customername };
		}
		if (filters?.projectManager?.length) {
			query.projectmanager = { $in: filters.projectManager };
		}
		if (filters?.projectDirector?.length) {
			query.projectdirector = { $in: filters.projectDirector };
		}

		// 3. Revenue Filter ($or condition)
		if (filters?.plannedRevenue?.length) {
			const revenueConditions: Record<string, unknown>[] = [];
			for (const range of filters.plannedRevenue) {
				switch (range) {
					case "OVER_1M":
						revenueConditions.push({ estconsfees: { $gte: 1000000 } });
						break;
					case "500K_1M":
						revenueConditions.push({
							estconsfees: { $gte: 500000, $lt: 1000000 },
						});
						break;
					case "250K_500K":
						revenueConditions.push({
							estconsfees: { $gte: 250000, $lt: 500000 },
						});
						break;
					case "UNDER_250K":
						revenueConditions.push({ estconsfees: { $lt: 250000 } });
						break;
				}
			}
			if (revenueConditions.length > 0) {
				// Add the $or condition for revenues to the main $and array
				andConditions.push({ $or: revenueConditions });
			}
		}

		// 4. Status Filter ($or condition)
		if (filters?.status?.length) {
			const statusConditions: Record<string, unknown>[] = [];
			const now = new Date();
			const nowISOString = now.toISOString().split("T")[0]; // Get YYYY-MM-DD format

			for (const stat of filters.status) {
				switch (stat) {
					case "ACTIVE":
						statusConditions.push({
							// Project has started (startdate or activedate is not null)
							$or: [
								{ startdate: { $ne: null } },
								{ activedate: { $ne: null } },
							],
							// And enddate is in the future (string comparison works for YYYY-MM-DD format)
							enddate: { $gt: nowISOString },
						});
						break;
					case "PLANNED":
						// Project hasn't started (no start dates set)
						statusConditions.push({
							$and: [
								{
									$or: [{ startdate: null }, { startdate: { $exists: false } }],
								},
								{
									$or: [
										{ activedate: null },
										{ activedate: { $exists: false } },
									],
								},
							],
						});
						break;
					case "COMPLETED":
						statusConditions.push({
							// Project has started
							$or: [
								{ startdate: { $ne: null } },
								{ activedate: { $ne: null } },
							],
							// And enddate is in the past or today (string comparison works for YYYY-MM-DD format)
							enddate: { $lte: nowISOString },
						});
						break;
				}
			}
			if (statusConditions.length > 0) {
				// Add the $or condition for statuses to the main $and array
				andConditions.push({ $or: statusConditions });
			}
		}

		// Combine $and conditions if any exist
		if (andConditions.length > 0) {
			query.$and = andConditions;
		}

		return query;
	}

	/**
	 * Maps database project fields to a frontend-friendly DTO.
	 * @param projectDetails - The raw project data from MongoDB.
	 * @returns A ProjectDetailDTO object.
	 */
	private _mapProjectToFrontendDTO(
		projectDetails: ProjectDocument,
	): ProjectDetailDTO {
		const mappedProject: Partial<ProjectDetailDTO> = {}; // Use Partial during build

		// Map fields using a type-safe approach
		if (projectDetails.customername)
			mappedProject.customerName = projectDetails.customername;
		if (projectDetails.projectname)
			mappedProject.projectName = projectDetails.projectname;
		if (projectDetails.location) mappedProject.city = projectDetails.location;
		if (projectDetails.primarymarket)
			mappedProject.industry = projectDetails.primarymarket;
		if (projectDetails.servicename)
			mappedProject.serviceOffering = projectDetails.servicename;
		if (projectDetails.estconsfees)
			mappedProject.plannedRevenue = projectDetails.estconsfees;
		if (projectDetails.projectid)
			mappedProject.projectId = projectDetails.projectid;
		if (projectDetails.projectdirector)
			mappedProject.projectDirector = projectDetails.projectdirector;
		if (projectDetails.projectmanager)
			mappedProject.projectManager = projectDetails.projectmanager;
		if (projectDetails.estprojectvalue)
			mappedProject.estimatedProjectValue = projectDetails.estprojectvalue;
		if (projectDetails.contactperson)
			mappedProject.contactPerson = projectDetails.contactperson;
		if (projectDetails.salescurrency)
			mappedProject.salesCurrency = projectDetails.salescurrency;
		if (projectDetails.profitcentre)
			mappedProject.profitCenter = projectDetails.profitcentre;
		if (projectDetails.totalrevenues)
			mappedProject.totalRevenues = projectDetails.totalrevenues;
		if (projectDetails.clienttype)
			mappedProject.clientType = projectDetails.clienttype;
		if (projectDetails.projecttype)
			mappedProject.projectType = projectDetails.projecttype;
		if (projectDetails.jobSheetLink)
			mappedProject.jobSheetLink = projectDetails.jobSheetLink;

		// Convert string dates to Date objects if needed
		if (projectDetails.startdate) {
			mappedProject.startDate =
				typeof projectDetails.startdate === "string"
					? new Date(projectDetails.startdate)
					: projectDetails.startdate;
		}
		if (projectDetails.enddate) {
			mappedProject.endDate =
				typeof projectDetails.enddate === "string"
					? new Date(projectDetails.enddate)
					: projectDetails.enddate;
		}
		if (projectDetails.activedate) {
			mappedProject.activeDate =
				typeof projectDetails.activedate === "string"
					? new Date(projectDetails.activedate)
					: projectDetails.activedate;
		}

		// Location details from latlng fallback
		if (
			projectDetails.latlng &&
			projectDetails.latlng.coordinates?.length === 2
		) {
			mappedProject.longitude = projectDetails.latlng.coordinates[0];
			mappedProject.latitude = projectDetails.latlng.coordinates[1];
			// Keep the original GeoJSON structure if needed elsewhere
			mappedProject.location = {
				type: "Point",
				coordinates: [...projectDetails.latlng.coordinates],
			};
		}

		return mappedProject as ProjectDetailDTO;
	}

	/**
	 * Get available filters for projects
	 */
	public async getFilters(): Promise<FiltersResponse> {
		try {
			return await this.mapRepository.getFilters();
		} catch (error) {
			logger.error("Error fetching available project filters", { error });
			throw error;
		}
	}

	/**
	 * Get filtered project locations based on the provided filters
	 */
	public async getFilteredProjectLocations(
		filters: FilterInput,
	): Promise<ProjectLocationDTO[] | { error: string }[]> {
		try {
			// Build the query using the consolidated private method
			const query = this._buildFilterQuery(filters);

			// Define the projection for the fields needed in the result
			const projection = {
				projectid: 1,
				primarymarket: 1,
				latlng: 1,
				projectname: 1,
				projectdirector: 1,
				customername: 1,
				_id: 0,
			};

			// Query projects with the constructed query and limit
			const projects = await this.mapRepository.findFilteredProjects(
				query,
				projection,
				RESULTS_LIMIT,
			);

			// logger.debug(
			//   `Found ${projects.length} projects matching the filters (limited to ${RESULTS_LIMIT})`,
			//   { filterCount: Object.keys(filters).length }
			// );

			// Map the results to the ProjectLocationDTO
			return projects.map((project): ProjectLocationDTO => {
				const coordinates = project.latlng?.coordinates ?? [0, 0];

				// Ensure coordinates are valid numbers
				const longitude =
					typeof coordinates[0] === "number" ? coordinates[0] : 0;
				const latitude =
					typeof coordinates[1] === "number" ? coordinates[1] : 0;

				return {
					projectId: project.projectid,
					industry: project.primarymarket,
					latitude: latitude,
					longitude: longitude,
					projectname: project.projectname,
					projectdirector: project.projectdirector,
					customername: project.customername,
				};
			});
		} catch (error: unknown) {
			logger.error("Error fetching filtered project locations", { error });
			const errorMessage =
				error instanceof Error ? error.message : "Unknown error occurred";
			return [{ error: errorMessage }];
		}
	}

	/**
	 * Get a project by ID with enriched location data
	 */
	public async getProjectById(
		projectId: string,
	): Promise<ProjectDetailDTO | null> {
		try {
			// Define the projection for fields needed from ProjectModel
			const projectProjection = {
				_id: 0,
				projectid: 1,
				company: 1,
				profitcentre: 1,
				location: 1,
				projectname: 1,
				projectdirector: 1,
				projectmanager: 1,
				estconsfees: 1,
				totalrevenues: 1,
				customername: 1,
				contactperson: 1,
				servicename: 1,
				primarymarket: 1,
				clienttype: 1,
				projecttype: 1,
				activedate: 1,
				estprojectvalue: 1,
				salescurrency: 1,
				region: 1,
				startdate: 1,
				enddate: 1,
				latlng: 1,
				jobSheetLink: 1,
			};

			// Fetch the core project details
			const projectDetails = await this.mapRepository.findProjectById(
				projectId,
				projectProjection,
			);

			if (!projectDetails) {
				logger.warn(`Project not found with ID: ${projectId}`);
				return null;
			}

			// Map the raw data to the frontend DTO
			const mappedProject = this._mapProjectToFrontendDTO(projectDetails);

			// --- Enrich with data from MapLocationModel ---
			try {
				const locationProjection = {
					"Project ID": 1,
					"Project Name": 1,
					Company: 1,
					"Customer Name": 1,
					Address: 1,
					Latitude: 1,
					Longitude: 1,
					"Formatted Address": 1,
					location: 1,
					_id: 0,
				};

				const mapLocation = await this.mapRepository.findMapLocationByProjectId(
					mappedProject.projectId,
					locationProjection,
				);

				if (mapLocation) {
					// Overwrite/add fields from MapLocationModel if found
					mappedProject.latitude = mapLocation.latitude;
					mappedProject.longitude = mapLocation.longitude;
					mappedProject.address = mapLocation.address;
					mappedProject.formattedAddress = mapLocation.formattedAddress;
					// Overwrite city from ProjectModel.location if MapLocationModel provides a GeoJSON point
					if (mapLocation.location) {
						mappedProject.location = mapLocation.location;
					}
				}
			} catch (locationError) {
				// Log enrichment error but don't fail the whole request
				logger.error("Error enriching project with MapLocationModel data", {
					error: locationError,
					projectId: mappedProject.projectId,
				});
			}

			return mappedProject;
		} catch (error: unknown) {
			logger.error("Error fetching project by ID", { error, projectId });
			throw error;
		}
	}

	/**
	 * Fetches filtered project data and formats it for download
	 */
	public async downloadFilteredData(
		filters: FilterInput,
	): Promise<DownloadResponse> {
		try {
			// Create a copy of filters and remove bounding box properties for download
			const { neLat, neLng, swLat, swLng, ...downloadFilters } = filters;

			// Use the modified filters for the query
			const query = this._buildFilterQuery(downloadFilters);
			// logger.debug('Generated query for download (ignoring bounds):', query);

			// Fetch all matching projects without the usual limit
			const projects = await this.mapRepository.findFilteredProjects(
				query,
				{},
				0,
			);

			// logger.debug(`Found ${projects.length} projects for download.`);

			if (!projects || projects.length === 0) {
				return { filename: "no_data.xlsx", data: Buffer.from("") };
			}

			// Define CSV headers
			const headers: (keyof CsvRowData)[] = [
				"projectId",
				"projectName",
				"customerName",
				"city",
				"industry",
				"serviceOffering",
				"plannedRevenue",
				"projectDirector",
				"projectManager",
				"startDate",
				"endDate",
			];

			// Map projects to an array of objects matching the headers
			const csvDataRows: CsvRowData[] = projects.map((project) => {
				const dto = this._mapProjectToFrontendDTO(project);
				return {
					projectId: dto.projectId ?? "",
					projectName: dto.projectName ?? "",
					customerName: dto.customerName ?? "",
					city: dto.city ?? "",
					industry: dto.industry ?? "",
					serviceOffering: dto.serviceOffering ?? "",
					plannedRevenue: dto.plannedRevenue ?? "",
					projectDirector: dto.projectDirector ?? "",
					projectManager: dto.projectManager ?? "",
					startDate: dto.startDate
						? dto.startDate.toISOString().split("T")[0]
						: "",
					endDate: dto.endDate ? dto.endDate.toISOString().split("T")[0] : "",
				};
			});

			// Generate XLSX
			const worksheetData: string[][] = [
				headers as string[],
				...csvDataRows.map((row) =>
					headers.map((header) => String(row[header] ?? "")),
				),
			];

			const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet(worksheetData);
			const wb: XLSX.WorkBook = XLSX.utils.book_new();
			XLSX.utils.book_append_sheet(wb, ws, "Filtered Projects");

			const xlsxBuffer: Buffer = XLSX.write(wb, {
				type: "buffer",
				bookType: "xlsx",
			});

			const filename = `filtered_projects_${new Date().toISOString().split("T")[0]}.xlsx`;

			return { filename, data: xlsxBuffer };
		} catch (error) {
			logger.error("Error fetching or formatting data for download:", error);
			throw new Error("Failed to generate download file.");
		}
	}
}
