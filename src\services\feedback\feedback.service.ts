import UserModel from "@/models/user.model";
import logger from "@/utils/logging";
import type { Feedback } from "./feedback.model";
import type { MongoFeedbackRepository } from "./mongoFeedback.repository";
import type {
	FeedbackInput,
	FeedbackResponse,
	FeedbackCreateInput,
} from "./feedback.types";

export class FeedbackService {
	constructor(private feedbackRepo: MongoFeedbackRepository) {}

	/**
	 * Create a new feedback entry
	 * @param feedbackData - The feedback data to create
	 * @returns The created feedback
	 */
	public async createFeedback(
		feedbackData: FeedbackInput,
	): Promise<FeedbackResponse> {
		// Check if the user exists
		const user = await UserModel.findOne({
			azureAdObjectId: feedbackData.azureAdObjectId,
		}).lean();

		if (!user) {
			logger.warn(
				`User with Azure AD Object ID ${feedbackData.azureAdObjectId} not found when submitting feedback`,
			);
			// We'll still create the feedback, but without linking to a user record
		}

		// Create the feedback
		const feedbackToCreate: Partial<Feedback> = {
			message: feedbackData.message,
			azureAdObjectId: feedbackData.azureAdObjectId,
			userId: user ? user._id : undefined,
			page_url: feedbackData.page_url,
			page_title: feedbackData.page_title,
		};

		const newFeedback = await this.feedbackRepo.create(
			feedbackToCreate as FeedbackCreateInput,
		);

		logger.info(
			`User ${feedbackData.azureAdObjectId} submitted feedback with ID: ${newFeedback._id}`,
		);

		return {
			id: newFeedback._id.toString(),
			message: newFeedback.message,
			azureAdObjectId: newFeedback.azureAdObjectId,
			createdAt: newFeedback.createdAt || new Date(),
			page_url: newFeedback.page_url,
			page_title: newFeedback.page_title,
		};
	}

	/**
	 * Get all feedback entries
	 * @returns Array of feedback entries
	 */
	public async getAllFeedback(): Promise<FeedbackResponse[]> {
		const feedback = await this.feedbackRepo.findAll();

		return feedback.map((item) => ({
			id: item._id.toString(),
			message: item.message,
			azureAdObjectId: item.azureAdObjectId,
			createdAt: item.createdAt || new Date(),
			page_url: item.page_url,
			page_title: item.page_title,
		}));
	}

	/**
	 * Get feedback by ID
	 * @param id - The feedback ID
	 * @returns The feedback entry
	 */
	public async getFeedbackById(id: string): Promise<FeedbackResponse | null> {
		const feedback = await this.feedbackRepo.findById(id);

		if (!feedback) {
			return null;
		}

		return {
			id: feedback._id.toString(),
			message: feedback.message,
			azureAdObjectId: feedback.azureAdObjectId,
			createdAt: feedback.createdAt || new Date(),
			page_url: feedback.page_url,
			page_title: feedback.page_title,
		};
	}

	/**
	 * Get feedback by user's Azure AD Object ID
	 * @param azureAdObjectId - The user's Azure AD Object ID
	 * @returns Array of feedback entries from the user
	 */
	public async getFeedbackByUser(
		azureAdObjectId: string,
	): Promise<FeedbackResponse[]> {
		const feedback =
			await this.feedbackRepo.findByAzureAdObjectId(azureAdObjectId);

		return feedback.map((item) => ({
			id: item._id.toString(),
			message: item.message,
			azureAdObjectId: item.azureAdObjectId,
			createdAt: item.createdAt || new Date(),
			page_url: item.page_url,
			page_title: item.page_title,
		}));
	}
}
