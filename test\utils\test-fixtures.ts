import type { DocumentType } from "@typegoose/typegoose";
import UserModel from "../../src/models/user.model";
import RoleModel from "../../src/models/role.model";
import PermissionModel from "../../src/models/permission.model";
import type { User } from "../../src/models/user.model";
import type { Role } from "../../src/models/role.model";
import type { Permission } from "../../src/models/permission.model";
import type { Project } from "@/models/project.model";
import { ProjectModel } from "@/models/project.model";

export type TestUser = DocumentType<User>;
export type TestRole = DocumentType<Role>;
export type TestPermission = DocumentType<Permission>;

export const testUsers = {
	admin: {
		azureAdObjectId: "<EMAIL>",
	},
	user: {
		azureAdObjectId: "<EMAIL>",
	},
} as const;

export const testRoles = {
	admin: {
		name: "ADMIN",
		description: "Administrator role with full access",
	},
	user: {
		name: "USER",
		description: "Regular user role with limited access",
	},
	superUser: {
		name: "SUPERUSER",
		description: "Superuser role with full access",
	},
} as const;

export const testProjects = [
	{
		projectid: "210294",
		company: "TBH",
		profitcentre: "SR",
		location: "MEL",
		projectname: "Timbertop P-6 School",
		projectdirector: "Jiong Zhu",
		projectmanager: "Tareq Hajeer",
		estconsfees: 5500,
		totalrevenues: -1560,
		customername: "Monash University",
		contactperson: "Vivienne Elder-Smith",
		servicename: "Planning & Scheduling",
		primarymarket: "EDUCATION",
		clienttype: "Education",
		projecttype: "Time and material",
		activedate: "2021-07-19",
		estprojectvalue: 2000000,
		salescurrency: "AUD",
		region: "SR",
		startdate: null,
		enddate: "2021-07-31",
		latlng: null,
		projectaddress: null,
		designasplannedstartdate: "2019-11-30",
		designasplannedenddate: "2020-02-19",
		designasplannedduration: 2,
		designasbuiltstartdate: "2019-11-30",
		designasbuiltenddate: "2020-02-19",
		designasbuiltduration: 2,
		planningapprovalasplannedstartdate: "2019-11-30",
		planningapprovalasplannedenddate: "2020-04-29",
		planningapprovalasplannedduration: 4,
		planningapprovalasbuiltstartdate: "2019-11-30",
		planningapprovalasbuiltenddate: "2020-04-29",
		planningapprovalasbuiltduration: 4,
		procurementasplannedstartdate: "2019-11-30",
		procurementasplannedenddate: "2020-08-28",
		procurementasplannedduration: 8,
		procurementasbuiltstartdate: "2019-11-30",
		procurementasbuiltenddate: "2020-08-28",
		procurementasbuiltduration: 8,
		constructionandcommissioningasplannedstartdate: "2019-02-12",
		constructionandcommissioningasplannedenddate: "2020-11-30",
		constructionandcommissioningasplannedduration: 21.54,
		constructionandcommissioningasbuiltstartdate: "2019-02-12",
		constructionandcommissioningasbuiltenddate: "2020-04-12",
		constructionandcommissioningasbuiltduration: 13.93,
		gfa: 2154.1,
		numberoffloors: null,
		totalcostasplanned: null,
		designasplannedcost: null,
		planningasplannedcost: null,
		procurementasplannedcost: null,
		constructionasplannedcost: null,
		totalcostasbuilt: null,
		designasbuiltcost: null,
		planningasbuiltcost: null,
		procurementasbuiltcost: null,
		constructionasbuiltcost: null,
		comments: "Status on 06/03 - Not complete",
	},
	{
		projectid: "200406",
		company: "TBH",
		profitcentre: "SR",
		location: "MEL",
		projectname: "MRS Student Village Amenities Refurbishment",
		projectdirector: "Jiong Zhu",
		projectmanager: "Tareq Hajeer",
		estconsfees: 4490,
		totalrevenues: 43935,
		customername: "Monash University",
		contactperson: "Axel Keert",
		servicename: "Planning & Scheduling",
		primarymarket: "EDUCATION",
		clienttype: "Education",
		projecttype: "Fixed fee",
		activedate: "2020-12-08",
		estprojectvalue: 4490,
		salescurrency: "AUD",
		region: "SR",
		startdate: null,
		enddate: "2020-11-27",
		latlng: null,
		projectaddress: null,
		designasplannedstartdate: "2019-10-02",
		designasplannedenddate: "2019-11-26",
		designasplannedduration: 1,
		designasbuiltstartdate: "2019-10-02",
		designasbuiltenddate: "2020-01-17",
		designasbuiltduration: 3,
		planningapprovalasplannedstartdate: "2019-10-02",
		planningapprovalasplannedenddate: "2020-01-21",
		planningapprovalasplannedduration: 3,
		planningapprovalasbuiltstartdate: "2019-10-02",
		planningapprovalasbuiltenddate: "2020-02-14",
		planningapprovalasbuiltduration: 4,
		procurementasplannedstartdate: "2019-10-14",
		procurementasplannedenddate: "2020-06-16",
		procurementasplannedduration: 8,
		procurementasbuiltstartdate: "2019-10-14",
		procurementasbuiltenddate: "2020-06-12",
		procurementasbuiltduration: 7,
		constructionandcommissioningasplannedstartdate: "2019-10-29",
		constructionandcommissioningasplannedenddate: "2020-11-09",
		constructionandcommissioningasplannedduration: 12.36,
		constructionandcommissioningasbuiltstartdate: "2019-10-29",
		constructionandcommissioningasbuiltenddate: "2020-11-09",
		constructionandcommissioningasbuiltduration: 12.36,
		gfa: 1236.07,
		numberoffloors: null,
		totalcostasplanned: null,
		designasplannedcost: null,
		planningasplannedcost: null,
		procurementasplannedcost: null,
		constructionasplannedcost: null,
		totalcostasbuilt: null,
		designasbuiltcost: null,
		planningasbuiltcost: null,
		procurementasbuiltcost: null,
		constructionasbuiltcost: null,
		comments: "NA for planned Planning end date",
	},
];

export function createTestProject(
	projectData: Partial<Project> = testProjects[0],
): Promise<DocumentType<Project>> {
	// Create a copy of the data to avoid modifying the original

	return ProjectModel.create(projectData);
}

export async function createTestRole(
	roleData: Partial<Role> = testRoles.user,
): Promise<TestRole> {
	return RoleModel.create(roleData);
}

export async function createTestUser(
	userData: Partial<User> = testUsers.user,
): Promise<TestUser> {
	// If no roles provided, create a default user role
	if (!userData.roles) {
		const userRole = await createTestRole();
		userData.roles = [userRole._id];
	}

	return UserModel.create(userData);
}

export async function setupTestData() {
	// Ensure Permission model is initialized first
	await PermissionModel.findOne().exec();

	// Create roles
	const adminRole = await createTestRole(testRoles.admin);
	const userRole = await createTestRole(testRoles.user);

	// Create users
	const adminUser = await createTestUser({
		...testUsers.admin,
		roles: [adminRole._id],
	});

	const regularUser = await createTestUser({
		...testUsers.user,
		roles: [userRole._id],
	});

	// Create projects
	await createTestProject(testProjects[0]);
	await createTestProject(testProjects[1]);

	return {
		roles: { admin: adminRole, user: userRole },
		users: { admin: adminUser, user: regularUser },
		projects: {
			project1: testProjects[0],
			project2: testProjects[1],
		},
	};
}
