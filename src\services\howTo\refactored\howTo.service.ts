import {
	BlobSASPermissions,
	StorageSharedKeyCredential,
	generateBlobSASQueryParameters,
} from "@azure/storage-blob";
import { Types } from "mongoose";
import {
	type CreateHowToGuideData,
	HowToGuide,
	HowToGuideModel,
	type UpdateHowToGuideData,
} from "../../../models/howTo/howTo.model";
import logger from "../../../utils/logging";
import type {
	AttachedFileInput,
	FilterOptions,
	IHowToRepository,
	PaginationResult,
	SearchOptions,
	TaxonomyItem,
	TaxonomyOption,
} from "../../repositories/howTo.repository";

// =====================================================
// SERVICE IMPLEMENTATION
// =====================================================

export class HowToService implements IHowToRepository {
	// =====================================================
	// HELPER METHODS
	// =====================================================

	/**
	 * Extract account name and key from connection string
	 */
	private extractAzureCredentialsFromConnectionString(
		connectionString: string,
	): { accountName?: string; accountKey?: string } {
		if (!connectionString) {
			return {};
		}

		try {
			const parts = connectionString.split(";");
			const credentials: { accountName?: string; accountKey?: string } = {};

			for (const part of parts) {
				if (part.startsWith("AccountName=")) {
					credentials.accountName = part.substring("AccountName=".length);
				} else if (part.startsWith("AccountKey=")) {
					credentials.accountKey = part.substring("AccountKey=".length);
				}
			}

			return credentials;
		} catch (error) {
			logger.error(
				"Error extracting credentials from connection string:",
				error,
			);
			return {};
		}
	}

	// =====================================================
	// CRUD OPERATIONS
	// =====================================================

	async create(data: CreateHowToGuideData): Promise<HowToGuide> {
		// Build hierarchy path
		const hierarchyPath: string[] = [];
		if (data.taxonomy.sector?.id) hierarchyPath.push(data.taxonomy.sector.id);
		if (data.taxonomy.subsector?.id)
			hierarchyPath.push(data.taxonomy.subsector.id);
		if (data.taxonomy.buildType?.id)
			hierarchyPath.push(data.taxonomy.buildType.id);

		const howToGuide = new HowToGuideModel({
			title: data.title,
			description: data.description,
			overview: data.overview,
			taxonomy: {
				...data.taxonomy,
				hierarchyPath,
			},
			content: {
				attachedFiles: [],
			},
			metadata: {
				order: data.metadata?.order,
				parentId: data.metadata?.parentId
					? new Types.ObjectId(data.metadata.parentId)
					: undefined,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		});

		return await howToGuide.save();
	}

	async findById(id: string): Promise<HowToGuide | null> {
		return await HowToGuideModel.findById(id).lean();
	}

	async findAll(page = 1, limit = 20): Promise<PaginationResult<HowToGuide>> {
		const skip = (page - 1) * limit;

		const [documents, total] = await Promise.all([
			HowToGuideModel.find({})
				.sort({ "metadata.order": 1, "metadata.createdAt": -1 })
				.skip(skip)
				.limit(limit)
				.lean(),
			HowToGuideModel.countDocuments({}),
		]);

		return {
			documents,
			pagination: {
				page,
				limit,
				total,
				pages: Math.ceil(total / limit),
			},
		};
	}

	async update(
		id: string,
		data: UpdateHowToGuideData,
	): Promise<HowToGuide | null> {
		const update: any = {};

		if (data.title) update.title = data.title;
		if (data.description) update.description = data.description;
		if (data.overview !== undefined) update.overview = data.overview;
		if (data.metadata?.order !== undefined)
			update["metadata.order"] = data.metadata.order;
		if (data.metadata?.parentId !== undefined) {
			update["metadata.parentId"] = data.metadata.parentId
				? new Types.ObjectId(data.metadata.parentId)
				: null;
		}

		// Handle taxonomy updates
		if (data.taxonomy) {
			const hierarchyPath: string[] = [];

			if (data.taxonomy.sector) {
				update["taxonomy.sector"] = data.taxonomy.sector;
				hierarchyPath.push(data.taxonomy.sector.id);
			}
			if (data.taxonomy.subsector) {
				update["taxonomy.subsector"] = data.taxonomy.subsector;
				hierarchyPath.push(data.taxonomy.subsector.id);
			}
			if (data.taxonomy.buildType) {
				update["taxonomy.buildType"] = data.taxonomy.buildType;
				hierarchyPath.push(data.taxonomy.buildType.id);
			}

			if (hierarchyPath.length > 0) {
				update["taxonomy.hierarchyPath"] = hierarchyPath;
			}
		}

		update["metadata.updatedAt"] = new Date();

		return await HowToGuideModel.findByIdAndUpdate(id, update, {
			new: true,
		}).lean();
	}

	async delete(id: string): Promise<boolean> {
		const result = await HowToGuideModel.findByIdAndDelete(id);
		return result !== null;
	}

	// =====================================================
	// SPECIFIC FIELD UPDATES
	// =====================================================

	async updateTitle(id: string, title: string): Promise<HowToGuide | null> {
		return await HowToGuide.updateTitle(id, title);
	}

	async updateDescription(
		id: string,
		description: string,
	): Promise<HowToGuide | null> {
		return await HowToGuide.updateDescription(id, description);
	}

	async updateOverview(
		id: string,
		overview: string,
	): Promise<HowToGuide | null> {
		return await HowToGuide.updateOverview(id, overview);
	}

	async updateTaxonomy(
		id: string,
		sector?: TaxonomyItem,
		subsector?: TaxonomyItem,
		buildType?: TaxonomyItem,
	): Promise<HowToGuide | null> {
		return await HowToGuide.updateTaxonomy(id, sector, subsector, buildType);
	}

	async updateOrder(id: string, order: number): Promise<HowToGuide | null> {
		return await HowToGuide.updateOrder(id, order);
	}

	async updateParent(
		id: string,
		parentId: string | null,
	): Promise<HowToGuide | null> {
		return await HowToGuide.updateParent(id, parentId);
	}

	// =====================================================
	// FILE OPERATIONS
	// =====================================================

	async addAttachedFile(
		id: string,
		file: AttachedFileInput,
	): Promise<HowToGuide | null> {
		return await HowToGuide.addAttachedFile(id, file.url, file.label);
	}

	async removeAttachedFile(
		id: string,
		fileId: string,
	): Promise<HowToGuide | null> {
		return await HowToGuide.removeAttachedFile(id, fileId);
	}

	async replaceAttachedFiles(
		id: string,
		files: AttachedFileInput[],
	): Promise<HowToGuide | null> {
		return await HowToGuide.replaceAttachedFiles(id, files);
	}

	// =====================================================
	// FILTER OPERATIONS
	// =====================================================

	async getSectors(): Promise<TaxonomyOption[]> {
		return await HowToGuide.getSectors();
	}

	async getSubsectors(sectorId: string): Promise<TaxonomyOption[]> {
		return await HowToGuide.getSubsectors(sectorId);
	}

	async getBuildTypes(
		sectorId: string,
		subsectorId: string,
	): Promise<TaxonomyOption[]> {
		return await HowToGuide.getBuildTypes(sectorId, subsectorId);
	}

	// =====================================================
	// SEARCH AND FILTER OPERATIONS
	// =====================================================

	async findByFilters(
		options: FilterOptions,
	): Promise<PaginationResult<HowToGuide>> {
		return await HowToGuide.findByFilters(
			options.sectorId,
			options.subsectorId,
			options.buildTypeId,
			options.page,
			options.limit,
		);
	}

	async findByHierarchy(hierarchyLevel: string): Promise<HowToGuide[]> {
		return await HowToGuide.findByHierarchy(hierarchyLevel);
	}

	async search(options: SearchOptions): Promise<PaginationResult<HowToGuide>> {
		return await HowToGuide.search(
			options.searchTerm,
			options.page,
			options.limit,
		);
	}

	async findWithAttachments(): Promise<HowToGuide[]> {
		return await HowToGuide.findWithAttachments();
	}

	async findByParent(parentId: string): Promise<HowToGuide[]> {
		return await HowToGuide.findByParent(parentId);
	}

	async findRecent(limit = 10): Promise<HowToGuide[]> {
		return await HowToGuide.findRecent(limit);
	}

	// =====================================================
	// UTILITY OPERATIONS
	// =====================================================

	generateSasUrlForBlob(blobUrl: string, expiryMinutes = 60): string {
		// Extract credentials from connection string or individual env vars
		const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING || "";
		const extractedCredentials =
			this.extractAzureCredentialsFromConnectionString(connectionString);

		const accountName =
			process.env.AZURE_STORAGE_ACCOUNT_NAME ||
			extractedCredentials.accountName;
		const accountKey =
			process.env.AZURE_STORAGE_ACCOUNT_KEY || extractedCredentials.accountKey;

		// Check if Azure storage configuration is available
		if (!accountName) {
			logger.error(
				"Azure storage account name not configured. Please set AZURE_STORAGE_ACCOUNT_NAME environment variable or AZURE_STORAGE_CONNECTION_STRING.",
			);
			throw new Error("Azure storage account name not configured");
		}

		if (!accountKey) {
			logger.error(
				"Azure storage account key not configured. Please set AZURE_STORAGE_ACCOUNT_KEY environment variable or AZURE_STORAGE_CONNECTION_STRING.",
			);
			throw new Error("Azure storage account key not configured");
		}

		try {
			// Parse the URL to extract container and blob name
			const url = new URL(blobUrl);
			const pathParts = url.pathname
				.split("/")
				.filter((part) => part.length > 0);

			if (pathParts.length < 2) {
				logger.error(`Invalid blob URL format: ${blobUrl}`);
				throw new Error("Invalid blob URL format");
			}

			// Get container name (first part of the path)
			const containerName = pathParts[0];
			// The rest is the blob name - decode URL-encoded characters
			const blobName = decodeURIComponent(pathParts.slice(1).join("/"));

			if (!containerName || !blobName) {
				logger.error(`Invalid blob URL: ${blobUrl}`);
				throw new Error("Invalid blob URL");
			}

			logger.info(
				`Generating SAS token for container: ${containerName}, blob: ${blobName}`,
			);

			// Create a StorageSharedKeyCredential
			const sharedKeyCredential = new StorageSharedKeyCredential(
				accountName,
				accountKey,
			);

			// Set the start and expiry time
			const startTime = new Date();
			startTime.setMinutes(startTime.getMinutes() - 5); // 5 minutes ago to account for clock skew

			const expiryTime = new Date();
			expiryTime.setMinutes(expiryTime.getMinutes() + expiryMinutes);

			// Set permissions for the SAS token with proper API version
			const sasOptions = {
				containerName,
				blobName,
				permissions: BlobSASPermissions.parse("r"), // Read-only permission
				startsOn: startTime, // Add start time to prevent clock skew issues
				expiresOn: expiryTime,
				version: "2020-08-04", // Specify API version
			};

			// Generate the SAS token
			const sasToken = generateBlobSASQueryParameters(
				sasOptions,
				sharedKeyCredential,
			).toString();

			// Combine the original URL with the SAS token
			return `${blobUrl}?${sasToken}`;
		} catch (error) {
			logger.error(`Error generating SAS token for URL: ${blobUrl}`, error);

			// If we're in development mode, return the original URL as a fallback
			if (process.env.NODE_ENV === "development") {
				logger.warn(
					"Running in development mode, returning original URL as fallback",
				);
				return blobUrl;
			}

			throw new Error(
				`Failed to generate SAS token: ${(error as Error).message}`,
			);
		}
	}
}

// =====================================================
// SERVICE INSTANCE
// =====================================================

export const howToService = new HowToService();
