import {
	getNotesModel,
	type NotesDocument,
} from "@/models/productivity/notes.model";
import type { INotesRepository, BulkUpdateItem, BulkUpdateResult } from "./notes.repository";
import logger from "@/utils/logging";

export class MongoNotesRepository implements INotesRepository {
	async findAll(): Promise<NotesDocument[]> {
		try {
			logger.info("Retrieving all notes");
			const notesModel = getNotesModel();
			
			// Project only the fields requested: _id, TradeInfoID, TradeID, img_tags, href_tags, imgFile, Description
			const projection = {
				_id: 1,
				TradeInfoID: 1,
				TradeID: 1,
				Description: 1,
				imgFile: 1,
				img_tags: 1,
				href_tags: 1
			};
			
			return await notesModel.find({}, projection).sort({ TradeID: 1 }).lean();
		} catch (error) {
			logger.error("Error retrieving all notes:", error);
			throw error;
		}
	}

	async findById(id: string): Promise<NotesDocument | null> {
		try {
			logger.info(`Retrieving note by ID: ${id}`);
			const notesModel = getNotesModel();
			return await notesModel.findById(id).lean();
		} catch (error) {
			logger.error(`Error retrieving note by ID ${id}:`, error);
			throw error;
		}
	}

	async findByTradeId(tradeId: number): Promise<NotesDocument[]> {
		try {
			logger.info(`Retrieving notes by TradeID: ${tradeId}`);
			const notesModel = getNotesModel();
			return await notesModel.find({ TradeID: tradeId }).lean();
		} catch (error) {
			logger.error(`Error retrieving notes by TradeID ${tradeId}:`, error);
			throw error;
		}
	}

	async findByTradeInfoId(tradeInfoId: number): Promise<NotesDocument[]> {
		try {
			logger.info(`Retrieving notes by TradeInfoID: ${tradeInfoId}`);
			const notesModel = getNotesModel();
			return await notesModel.find({ TradeInfoID: tradeInfoId }).lean();
		} catch (error) {
			logger.error(
				`Error retrieving notes by TradeInfoID ${tradeInfoId}:`,
				error,
			);
			throw error;
		}
	}

	async findActiveNotes(): Promise<NotesDocument[]> {
		try {
			logger.info("Retrieving active notes (NotActive = 0)");
			const notesModel = getNotesModel();
			return await notesModel.find({ NotActive: 0 }).lean();
		} catch (error) {
			logger.error("Error retrieving active notes:", error);
			throw error;
		}
	}

	async findVisibleNotes(): Promise<NotesDocument[]> {
		try {
			logger.info("Retrieving visible notes (NotShow = 0)");
			const notesModel = getNotesModel();
			return await notesModel.find({ NotShow: 0 }).lean();
		} catch (error) {
			logger.error("Error retrieving visible notes:", error);
			throw error;
		}
	}

	async bulkUpdate(updates: BulkUpdateItem[]): Promise<BulkUpdateResult> {
		try {
			logger.info(`Performing bulk update on ${updates.length} notes`);
			const notesModel = getNotesModel();

			const bulkOperations = updates.map(({ id, updates: updateData }) => ({
				updateOne: {
					filter: { _id: id },
					update: { $set: updateData }
				}
			}));

			const result = await notesModel.bulkWrite(bulkOperations);

			return {
				modifiedCount: result.modifiedCount,
				matchedCount: result.matchedCount
			};
		} catch (error) {
			logger.error("Error performing bulk update on notes:", error);
			throw error;
		}
	}
}
