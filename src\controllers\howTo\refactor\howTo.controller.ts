import config from "@/config/config";
import logger from "@/utils/logging";
import type { NextFunction, Request, Response } from "express";
import { Types } from "mongoose";
import { z } from "zod";
import type { AzureBlobService } from "../../../services/file/azureBlob.service";
import type {
	AttachedFileInput,
	FilterOptions,
	IHowToRepository,
	SearchOptions,
} from "../../../services/repositories/howTo.repository";

// Validation schemas
const idParamSchema = z.object({
	id: z.string().refine((val) => Types.ObjectId.isValid(val), {
		message: "Invalid ID format",
	}),
});

const attachedFileSchema = z.object({
	url: z.string().url("Invalid URL format"),
	label: z.string().min(1, "Label is required"),
});

const createHowToSchema = z.object({
	title: z.string().min(1, "Title is required"),
	description: z.string().min(1, "Description must not be empty").optional(),
	overview: z.string().optional(),
	taxonomy: z.object({
		sector: z
			.object({
				id: z.string(),
				label: z.string(),
				description: z.string().optional(),
			})
			.optional(),
		subsector: z
			.object({
				id: z.string(),
				label: z.string(),
				description: z.string().optional(),
			})
			.optional(),
		buildType: z
			.object({
				id: z.string(),
				label: z.string(),
				description: z.string().optional(),
			})
			.optional(),
	}),
	metadata: z
		.object({
			order: z.number().optional(),
			parentId: z.string().optional(),
		})
		.optional(),
	attachedFiles: z
		.array(attachedFileSchema)
		.max(20, "Maximum 20 files allowed")
		.optional(),
});

const updateHowToSchema = createHowToSchema.partial().extend({
	attachedFiles: z
		.array(attachedFileSchema)
		.max(20, "Maximum 20 files allowed")
		.optional(),
});

const filterSchema = z.object({
	sectorId: z.string().optional(),
	subsectorId: z.string().optional(),
	buildTypeId: z.string().optional(),
	page: z.number().optional(),
	limit: z.number().optional(),
});

const searchSchema = z.object({
	searchTerm: z.string().min(1, "Search term is required"),
	page: z.number().optional(),
	limit: z.number().optional(),
});

const presignedUrlSchema = z.object({
	fileName: z.string().min(1, "File name is required"),
	fileType: z.string().min(1, "File type is required"),
	expiryMinutes: z.number().min(1).max(1440).optional(), // 1 minute to 24 hours
});

const batchPresignedUrlSchema = z.object({
	files: z
		.array(
			z.object({
				fileName: z.string().min(1, "File name is required"),
				fileType: z.string().min(1, "File type is required"),
			}),
		)
		.min(1, "At least one file is required")
		.max(10, "Maximum 10 files allowed"),
	expiryMinutes: z.number().min(1).max(1440).optional(), // 1 minute to 24 hours
});

const deleteFileSchema = z.object({
	url: z.string().url("Invalid URL format"),
});

const deleteMultipleFilesSchema = z.object({
	urls: z
		.array(z.string().url("Invalid URL format"))
		.min(1, "At least one URL is required")
		.max(50, "Maximum 50 files allowed"),
});

const createTemplateSchema = z.object({
	title: z.string().min(1, "Title is required"),
	sector: z.object({
		id: z.string().min(1, "Sector ID is required"),
		label: z.string().min(1, "Sector label is required"),
		description: z.string().optional(),
	}),
	subsector: z.object({
		id: z.string().min(1, "Subsector ID is required"),
		label: z.string().min(1, "Subsector label is required"),
		description: z.string().optional(),
	}),
	buildType: z.object({
		id: z.string().min(1, "Build type ID is required"),
		label: z.string().min(1, "Build type label is required"),
		description: z.string().optional(),
	}),
});

/**
 * @swagger
 * components:
 *   schemas:
 *     HowToGuide:
 *       type: object
 *       required:
 *         - title
 *         - taxonomy
 *       properties:
 *         _id:
 *           type: string
 *           description: Unique identifier for the HowTo guide
 *         title:
 *           type: string
 *           description: Title of the HowTo guide
 *         description:
 *           type: string
 *           description: Description of the HowTo guide
 *         overview:
 *           type: string
 *           description: Overview content of the HowTo guide
 *         taxonomy:
 *           type: object
 *           properties:
 *             sector:
 *               $ref: '#/components/schemas/TaxonomyItem'
 *             subsector:
 *               $ref: '#/components/schemas/TaxonomyItem'
 *             buildType:
 *               $ref: '#/components/schemas/TaxonomyItem'
 *             hierarchyPath:
 *               type: array
 *               items:
 *                 type: string
 *         content:
 *           type: object
 *           properties:
 *             attachedFiles:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/AttachedFile'
 *         metadata:
 *           type: object
 *           properties:
 *             order:
 *               type: number
 *             parentId:
 *               type: string
 *             createdAt:
 *               type: string
 *               format: date-time
 *             updatedAt:
 *               type: string
 *               format: date-time
 *
 *     TaxonomyItem:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         label:
 *           type: string
 *         description:
 *           type: string
 *
 *     AttachedFile:
 *       type: object
 *       properties:
 *         url:
 *           type: string
 *           format: uri
 *         label:
 *           type: string
 *
 *     PresignedUrlRequest:
 *       type: object
 *       required:
 *         - fileName
 *         - fileType
 *       properties:
 *         fileName:
 *           type: string
 *         fileType:
 *           type: string
 *         expiryMinutes:
 *           type: number
 *           minimum: 1
 *           maximum: 1440
 *
 *     PresignedUrlResponse:
 *       type: object
 *       properties:
 *         uploadUrl:
 *           type: string
 *           format: uri
 *         blobUrl:
 *           type: string
 *           format: uri
 *
 *     PaginationResult:
 *       type: object
 *       properties:
 *         documents:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/HowToGuide'
 *         pagination:
 *           type: object
 *           properties:
 *             page:
 *               type: number
 *             limit:
 *               type: number
 *             total:
 *               type: number
 *             pages:
 *               type: number
 *
 *     ErrorResponse:
 *       type: object
 *       properties:
 *         error:
 *           type: string
 *         details:
 *           type: array
 *           items:
 *             type: object
 */
export class HowToController {
	private howToRepository: IHowToRepository;
	private azureBlobService: AzureBlobService;

	constructor(
		howToRepository: IHowToRepository,
		azureBlobService: AzureBlobService,
	) {
		this.howToRepository = howToRepository;
		this.azureBlobService = azureBlobService;
	}

	/**
	 * @swagger
	 * /api/einstein/how-to/upload/presigned-url:
	 *   post:
	 *     summary: Generate presigned URL for file upload
	 *     description: Generate a presigned URL for uploading a single file to Azure Blob Storage
	 *     tags: [File Management]
	 *     requestBody:
	 *       required: true
	 *       content:
	 *         application/json:
	 *           schema:
	 *             $ref: '#/components/schemas/PresignedUrlRequest'
	 *           example:
	 *             fileName: "document.pdf"
	 *             fileType: "application/pdf"
	 *             expiryMinutes: 60
	 *     responses:
	 *       200:
	 *         description: Presigned URL generated successfully
	 *         content:
	 *           application/json:
	 *             schema:
	 *               type: object
	 *               properties:
	 *                 message:
	 *                   type: string
	 *                 data:
	 *                   $ref: '#/components/schemas/PresignedUrlResponse'
	 *       400:
	 *         description: Validation error
	 *         content:
	 *           application/json:
	 *             schema:
	 *               $ref: '#/components/schemas/ErrorResponse'
	 *       500:
	 *         description: Server error
	 */
	public generateUploadPresignedUrl = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const validatedData = presignedUrlSchema.parse(req.body);

			const result = await this.azureBlobService.generateUploadPresignedUrl(
				validatedData.fileName,
				validatedData.fileType,
				validatedData.expiryMinutes,
			);

			res.status(200).json({
				message: "Upload presigned URL generated successfully",
				data: result,
			});
		} catch (error) {
			if (error instanceof z.ZodError) {
				res
					.status(400)
					.json({ error: "Validation error", details: error.errors });
				return;
			}
			next(error);
		}
	};

	/**
	 * @swagger
	 * /api/einstein/how-to/upload/batch-presigned-urls:
	 *   post:
	 *     summary: Generate batch presigned URLs for file uploads
	 *     description: Generate presigned URLs for uploading multiple files to Azure Blob Storage
	 *     tags: [File Management]
	 *     requestBody:
	 *       required: true
	 *       content:
	 *         application/json:
	 *           schema:
	 *             type: object
	 *             required:
	 *               - files
	 *             properties:
	 *               files:
	 *                 type: array
	 *                 minItems: 1
	 *                 maxItems: 10
	 *                 items:
	 *                   type: object
	 *                   properties:
	 *                     fileName:
	 *                       type: string
	 *                     fileType:
	 *                       type: string
	 *               expiryMinutes:
	 *                 type: number
	 *                 minimum: 1
	 *                 maximum: 1440
	 *           example:
	 *             files:
	 *               - fileName: "document1.pdf"
	 *                 fileType: "application/pdf"
	 *               - fileName: "image1.jpg"
	 *                 fileType: "image/jpeg"
	 *             expiryMinutes: 60
	 *     responses:
	 *       200:
	 *         description: Batch presigned URLs generated successfully
	 *         content:
	 *           application/json:
	 *             schema:
	 *               type: object
	 *               properties:
	 *                 message:
	 *                   type: string
	 *                 data:
	 *                   type: array
	 *                   items:
	 *                     $ref: '#/components/schemas/PresignedUrlResponse'
	 *       400:
	 *         description: Validation error
	 *         content:
	 *           application/json:
	 *             schema:
	 *               $ref: '#/components/schemas/ErrorResponse'
	 */
	public generateBatchUploadPresignedUrls = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const validatedData = batchPresignedUrlSchema.parse(req.body);

			const fileInfos = validatedData.files.map((file) => ({
				originalName: file.fileName,
				mimeType: file.fileType,
			}));

			const results =
				await this.azureBlobService.generateBatchUploadPresignedUrls(
					fileInfos,
					validatedData.expiryMinutes,
				);

			res.status(200).json({
				message: "Batch upload presigned URLs generated successfully",
				data: results,
			});
		} catch (error) {
			if (error instanceof z.ZodError) {
				res
					.status(400)
					.json({ error: "Validation error", details: error.errors });
				return;
			}
			next(error);
		}
	};

	/**
	 * @swagger
	 * /api/einstein/how-to/files/delete:
	 *   delete:
	 *     summary: Delete a single file from Azure Blob Storage
	 *     description: Delete a file from Azure Blob Storage using its URL
	 *     tags: [File Management]
	 *     requestBody:
	 *       required: true
	 *       content:
	 *         application/json:
	 *           schema:
	 *             type: object
	 *             required:
	 *               - url
	 *             properties:
	 *               url:
	 *                 type: string
	 *                 format: uri
	 *           example:
	 *             url: "https://storage.blob.core.windows.net/container/file.pdf"
	 *     responses:
	 *       200:
	 *         description: File deleted successfully
	 *         content:
	 *           application/json:
	 *             schema:
	 *               type: object
	 *               properties:
	 *                 message:
	 *                   type: string
	 *                 data:
	 *                   type: object
	 *                   properties:
	 *                     url:
	 *                       type: string
	 *                     deleted:
	 *                       type: boolean
	 *       404:
	 *         description: File not found or already deleted
	 *       400:
	 *         description: Validation error
	 */
	public deleteFile = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const validatedData = deleteFileSchema.parse(req.body);

			const deleted = await this.azureBlobService.deleteFileByUrl(
				validatedData.url,
			);

			if (deleted) {
				res.status(200).json({
					message: "File deleted successfully",
					data: { url: validatedData.url, deleted: true },
				});
			} else {
				res.status(404).json({
					message: "File not found or already deleted",
					data: { url: validatedData.url, deleted: false },
				});
			}
		} catch (error) {
			if (error instanceof z.ZodError) {
				res
					.status(400)
					.json({ error: "Validation error", details: error.errors });
				return;
			}
			next(error);
		}
	};

	/**
	 * Delete multiple files from Azure Blob Storage
	 */
	public deleteMultipleFiles = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const validatedData = deleteMultipleFilesSchema.parse(req.body);

			const results = await this.azureBlobService.deleteMultipleFiles(
				validatedData.urls,
			);

			const successCount = results.filter((r) => r.success).length;
			const failureCount = results.length - successCount;

			res.status(200).json({
				message: `Batch delete completed: ${successCount} deleted, ${failureCount} failed`,
				data: {
					results,
					summary: {
						total: results.length,
						successful: successCount,
						failed: failureCount,
					},
				},
			});
		} catch (error) {
			if (error instanceof z.ZodError) {
				res
					.status(400)
					.json({ error: "Validation error", details: error.errors });
				return;
			}
			next(error);
		}
	};

	/**
	 * Check if a file exists in Azure Blob Storage
	 */
	public checkFileExists = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const url = req.query.url as string;

			if (!url) {
				res.status(400).json({ error: "URL parameter is required" });
				return;
			}

			const urlSchema = z.string().url("Invalid URL format");
			const validatedUrl = urlSchema.parse(url);

			const exists = await this.azureBlobService.fileExists(validatedUrl);

			res.status(200).json({
				message: "File existence check completed",
				data: { url: validatedUrl, exists },
			});
		} catch (error) {
			if (error instanceof z.ZodError) {
				res
					.status(400)
					.json({ error: "Validation error", details: error.errors });
				return;
			}
			next(error);
		}
	};

	/**
	 * Get all HowTos
	 */
	public getAllHowTos = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const page = req.query.page
				? Number.parseInt(req.query.page as string)
				: 1;
			const limit = req.query.limit
				? Number.parseInt(req.query.limit as string)
				: 20;

			const result = await this.howToRepository.findAll(page, limit);
			res.status(200).json({ data: result });
		} catch (error) {
			next(error);
		}
	};

	/**
	 * Get HowTo by ID
	 */
	public getHowToById = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const { id } = idParamSchema.parse(req.params);
			const howTo = await this.howToRepository.findById(id);

			if (!howTo) {
				res.status(404).json({ error: "HowTo not found" });
				return;
			}

			// Process description to replace file URLs with SAS URLs
			if (howTo.description) {
				howTo.description = await this.processDescriptionUrls(
					howTo.description,
				);
			}

			if (howTo.overview) {
				howTo.overview = await this.processDescriptionUrls(howTo.overview);
				logger.debug("processedOverview", {
					processedOverview: howTo.overview,
				});
			}

			res.status(200).json({ data: howTo });
		} catch (error) {
			if (error instanceof z.ZodError) {
				res
					.status(400)
					.json({ error: "Validation error", details: error.errors });
				return;
			}
			next(error);
		}
	};

	/**
	 * Create a new HowTo
	 */
	public createHowTo = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const validatedData = createHowToSchema.parse(req.body);
			const { attachedFiles, ...howToData } = validatedData;

			// Create the HowTo first
			let newHowTo = await this.howToRepository.create(howToData);

			// Handle attached files if provided
			if (attachedFiles && attachedFiles.length > 0) {
				const updatedHowTo = await this.howToRepository.replaceAttachedFiles(
					newHowTo._id.toString(),
					attachedFiles,
				);
				if (!updatedHowTo) {
					res
						.status(500)
						.json({ error: "Failed to attach files during creation" });
					return;
				}
				newHowTo = updatedHowTo;
			}

			res.status(201).json({ data: newHowTo });
		} catch (error) {
			if (error instanceof z.ZodError) {
				res
					.status(400)
					.json({ error: "Validation error", details: error.errors });
				return;
			}
			next(error);
		}
	};

	/**
	 * Update existing HowTo
	 */
	public updateHowTo = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const { id } = idParamSchema.parse(req.params);
			const validatedData = updateHowToSchema.parse(req.body);

			// Extract attached files from the validated data
			const { attachedFiles, ...howToData } = validatedData;

			// Update the main HowTo data (without attached files)
			let updatedHowTo = await this.howToRepository.update(id, howToData);

			if (!updatedHowTo) {
				res.status(404).json({ error: "HowTo not found" });
				return;
			}

			// Handle attached files if provided - replace all existing files
			if (attachedFiles && attachedFiles.length > 0) {
				updatedHowTo = await this.howToRepository.replaceAttachedFiles(
					id,
					attachedFiles,
				);
				if (!updatedHowTo) {
					res
						.status(404)
						.json({ error: "HowTo not found during file attachment" });
					return;
				}
			}

			res.status(200).json({ data: updatedHowTo });
		} catch (error) {
			if (error instanceof z.ZodError) {
				res
					.status(400)
					.json({ error: "Validation error", details: error.errors });
				return;
			}
			next(error);
		}
	};

	/**
	 * Delete HowTo
	 */
	public deleteHowTo = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const { id } = idParamSchema.parse(req.params);
			const deleted = await this.howToRepository.delete(id);

			if (!deleted) {
				res.status(404).json({ error: "HowTo not found" });
				return;
			}

			res.status(200).json({ message: "HowTo deleted successfully" });
		} catch (error) {
			if (error instanceof z.ZodError) {
				res
					.status(400)
					.json({ error: "Validation error", details: error.errors });
				return;
			}
			next(error);
		}
	};

	/**
	 * Get HowTos by filter criteria
	 */
	public getFilteredHowTos = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const filters: FilterOptions = {
				sectorId: req.query.sectorId as string,
				subsectorId: req.query.subsectorId as string,
				buildTypeId: req.query.buildTypeId as string,
				page: req.query.page ? Number.parseInt(req.query.page as string) : 1,
				limit: req.query.limit
					? Number.parseInt(req.query.limit as string)
					: 20,
			};

			const result = await this.howToRepository.findByFilters(filters);
			res.status(200).json({ data: result });
		} catch (error) {
			next(error);
		}
	};

	/**
	 * Get filtered HowTos in tree structure
	 */
	public getFilteredHowTosTree = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const filters: FilterOptions = {
				sectorId: req.query.sectorId as string,
				subsectorId: req.query.subsectorId as string,
				buildTypeId: req.query.buildTypeId as string,
				// For tree structure, we want all items (no pagination)
				page: 1,
				limit: 10000, // Large limit to get all items
			};

			const result = await this.howToRepository.findByFilters(filters);

			// Build tree structure from flat list
			const treeData = this.buildTreeStructure(result.documents);

			res.status(200).json({
				data: treeData,
				pagination: result.pagination,
			});
		} catch (error) {
			next(error);
		}
	};

	/**
	 * Helper method to build tree structure from flat list
	 */
	private buildTreeStructure(items: any[]): any[] {
		// Create a map for quick lookup
		const itemMap = new Map<string, any>();
		const rootItems: any[] = [];

		// First pass: create map and add children array with only essential fields
		for (const item of items) {
			const itemWithChildren = {
				_id: item._id,
				title: item.title,
				parentId: item.metadata?.parentId || null,
				order: item.metadata?.order || 0,
				children: [],
			};
			itemMap.set(item._id.toString(), itemWithChildren);
		}

		// Second pass: build parent-child relationships
		for (const item of items) {
			const itemWithChildren = itemMap.get(item._id.toString());

			if (itemWithChildren?.parentId) {
				// Has a parent, add to parent's children
				const parentId = itemWithChildren.parentId.toString();
				const parent = itemMap.get(parentId);

				if (parent) {
					parent.children.push(itemWithChildren);
				} else {
					// Parent not found in filtered results, treat as root
					rootItems.push(itemWithChildren);
				}
			} else {
				// No parent, it's a root item
				rootItems.push(itemWithChildren);
			}
		}

		// Clean up empty children arrays and sort by order
		const cleanAndSort = (nodes: any[]): any[] => {
			return nodes
				.map((node) => {
					// Remove internal fields used for sorting/building, but keep order
					const { parentId, ...cleanNode } = node;

					if (node.children.length === 0) {
						const { children, ...nodeWithoutChildren } = cleanNode;
						return nodeWithoutChildren;
					}
					return {
						...cleanNode,
						children: cleanAndSort(node.children),
					};
				})
				.sort((a, b) => {
					// Sort by order, then by title
					const orderA = a.order || 0;
					const orderB = b.order || 0;

					if (orderA !== orderB) {
						return orderA - orderB;
					}

					return a.title.localeCompare(b.title);
				});
		};

		return cleanAndSort(rootItems);
	}

	/**
	 * Extract storage account name from Azure Storage connection string
	 */
	private extractStorageAccountName(connectionString: string): string | null {
		try {
			const accountNameMatch = connectionString.match(/AccountName=([^;]+)/);
			return accountNameMatch ? accountNameMatch[1] : null;
		} catch (error) {
			console.error("Error extracting storage account name:", error);
			return null;
		}
	}

	/**
	 * Convert a single /files/ URL to full Azure Storage URL with SAS token
	 * @param fileUrl - URL in format /files/container-name/blob-path
	 * @returns Full Azure Storage URL with SAS token
	 */
	private async convertFileUrlToAzureUrl(fileUrl: string): Promise<string> {
		try {
			// Extract storage account name from connection string
			const connectionString = config.azure.storageConnectionString;
			if (!connectionString) {
				logger.warn("Azure storage connection string not configured");
				return fileUrl; // Return original URL if no connection string
			}

			const storageAccountName =
				this.extractStorageAccountName(connectionString);
			if (!storageAccountName) {
				logger.warn(
					"Could not extract storage account name from connection string",
				);
				return fileUrl; // Return original URL if can't extract account name
			}

			// Parse the URL to extract container and blob path
			// Expected format: /files/container-name/blob-path
			if (!fileUrl.startsWith("/files/")) {
				return fileUrl; // Return original if not a /files/ URL
			}

			// Remove /files/ prefix: /files/container-name/blob-path -> container-name/blob-path
			const pathWithoutFiles = fileUrl.substring(7);

			// Extract container name (first segment)
			const pathSegments = pathWithoutFiles.split("/");
			if (pathSegments.length < 2) {
				logger.warn(`Invalid file URL format: ${fileUrl}`);
				return fileUrl;
			}

			const containerName = pathSegments[0];

			// Generate container SAS token for the specific container
			const containerSasUrl =
				await this.azureBlobService.generateContainerSASToken(containerName);

			// Extract the SAS token part from the URL (everything after the ?)
			const sasTokenPart = containerSasUrl.includes("?")
				? containerSasUrl.split("?")[1]
				: "";

			// Build full Azure Storage URL with SAS token
			const fullUrl = `https://${storageAccountName}.blob.core.windows.net/${pathWithoutFiles}${sasTokenPart ? `?${sasTokenPart}` : ""}`;

			return fullUrl;
		} catch (error) {
			logger.error("Error converting file URL to Azure URL:", error);
			return fileUrl; // Return original URL if conversion fails
		}
	}

	/**
	 * Process description content and replace file URLs with SAS URLs
	 */
	private async processDescriptionUrls(description: string): Promise<string> {
		if (!description) return description;

		try {
			// Find all URLs that match the pattern /files/
			const urlPattern = /\/files\/[^\s"')<>]+/g;
			const matches = description.match(urlPattern);

			if (!matches || matches.length === 0) {
				return description;
			}

			// Convert each URL
			let processedDescription = description;
			for (const match of matches) {
				const convertedUrl = await this.convertFileUrlToAzureUrl(match);
				processedDescription = processedDescription.replace(
					match,
					convertedUrl,
				);
			}

			logger.debug("processedDescription", { processedDescription });
			return processedDescription;
		} catch (error) {
			logger.error("Error processing description URLs:", error);
			// Return original description if processing fails
			return description;
		}
	}

	/**
	 * Get HowTos by parent ID
	 */
	public getHowTosByParentId = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const parentId = req.query.parentId as string;

			if (parentId && !Types.ObjectId.isValid(parentId)) {
				res.status(400).json({ error: "Invalid parent ID format" });
				return;
			}

			const howTos = await this.howToRepository.findByParent(parentId);
			res.status(200).json({ data: howTos });
		} catch (error) {
			next(error);
		}
	};

	/**
	 * Search HowTos
	 */
	public searchHowTos = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const searchOptions: SearchOptions = {
				searchTerm: req.query.searchTerm as string,
				page: req.query.page ? Number.parseInt(req.query.page as string) : 1,
				limit: req.query.limit
					? Number.parseInt(req.query.limit as string)
					: 20,
			};

			const validatedOptions = searchSchema.parse(searchOptions);
			const result = await this.howToRepository.search(validatedOptions);
			res.status(200).json({ data: result });
		} catch (error) {
			if (error instanceof z.ZodError) {
				res
					.status(400)
					.json({ error: "Validation error", details: error.errors });
				return;
			}
			next(error);
		}
	};

	/**
	 * Get recent HowTos
	 */
	public getRecentHowTos = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const limit = req.query.limit
				? Number.parseInt(req.query.limit as string)
				: 10;
			const howTos = await this.howToRepository.findRecent(limit);
			res.status(200).json({ data: howTos });
		} catch (error) {
			next(error);
		}
	};

	/**
	 * Get filter options
	 */
	public getSectors = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const sectors = await this.howToRepository.getSectors();
			res.status(200).json({ data: sectors });
		} catch (error) {
			next(error);
		}
	};

	public getSubsectors = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const sectorId = req.query.sectorId as string;
			if (!sectorId) {
				res.status(400).json({ error: "Sector ID is required" });
				return;
			}

			const subsectors = await this.howToRepository.getSubsectors(sectorId);
			res.status(200).json({ data: subsectors });
		} catch (error) {
			next(error);
		}
	};

	public getBuildTypes = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const sectorId = req.query.sectorId as string;
			const subsectorId = req.query.subsectorId as string;

			if (!sectorId || !subsectorId) {
				res
					.status(400)
					.json({ error: "Sector ID and Subsector ID are required" });
				return;
			}

			const buildTypes = await this.howToRepository.getBuildTypes(
				sectorId,
				subsectorId,
			);
			res.status(200).json({ data: buildTypes });
		} catch (error) {
			next(error);
		}
	};

	/**
	 * Add attached file to HowTo
	 */
	public addAttachedFile = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const { id } = idParamSchema.parse(req.params);
			const { url, label } = req.body;

			if (!url || !label) {
				res.status(400).json({ error: "URL and label are required" });
				return;
			}

			const fileInput: AttachedFileInput = { url, label };
			const updatedHowTo = await this.howToRepository.addAttachedFile(
				id,
				fileInput,
			);

			if (!updatedHowTo) {
				res.status(404).json({ error: "HowTo not found" });
				return;
			}

			res.status(200).json({ data: updatedHowTo });
		} catch (error) {
			if (error instanceof z.ZodError) {
				res
					.status(400)
					.json({ error: "Validation error", details: error.errors });
				return;
			}
			next(error);
		}
	};

	/**
	 * Remove attached file from HowTo
	 */
	public removeAttachedFile = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const { id } = idParamSchema.parse(req.params);
			const { fileId } = req.body;

			if (!fileId) {
				res.status(400).json({ error: "File ID is required" });
				return;
			}

			const updatedHowTo = await this.howToRepository.removeAttachedFile(
				id,
				fileId,
			);

			if (!updatedHowTo) {
				res.status(404).json({ error: "HowTo not found" });
				return;
			}

			res.status(200).json({ data: updatedHowTo });
		} catch (error) {
			if (error instanceof z.ZodError) {
				res
					.status(400)
					.json({ error: "Validation error", details: error.errors });
				return;
			}
			next(error);
		}
	};

	/**
	 * Generate a SAS token for a file URL
	 */
	public generateSasUrl = async (req: Request, res: Response) => {
		try {
			const urlSchema = z.object({
				url: z.string().url("Invalid URL format"),
				expiryMinutes: z.number().optional(),
			});

			// Get the URL from query parameter
			const url = req.query.url as string;
			const expiryMinutes = req.query.expiryMinutes
				? Number.parseInt(req.query.expiryMinutes as string)
				: undefined;

			if (!url) {
				res.status(400).json({ error: "URL parameter is required" });
				return;
			}

			// Validate the URL
			const { url: validatedUrl, expiryMinutes: validatedExpiryMinutes } =
				urlSchema.parse({
					url,
					expiryMinutes,
				});

			// Generate SAS token using HowTo repository
			const sasUrl = this.howToRepository.generateSasUrlForBlob(
				validatedUrl,
				validatedExpiryMinutes,
			);

			res.status(200).json({ data: { sasUrl } });
		} catch (error) {
			if (error instanceof z.ZodError) {
				res
					.status(400)
					.json({ error: "Validation error", details: error.errors });
				return;
			}

			if (error instanceof Error) {
				const errorMessage = error.message;

				if (
					errorMessage.includes("Azure storage account name not configured")
				) {
					res.status(500).json({
						error: "Server configuration error",
						message:
							"Azure storage account name is not configured. Please contact the administrator.",
					});
					return;
				}

				if (errorMessage.includes("Azure storage account key not configured")) {
					res.status(500).json({
						error: "Server configuration error",
						message:
							"Azure storage account key is not configured. Please contact the administrator.",
					});
					return;
				}

				if (errorMessage.includes("Invalid blob URL")) {
					res.status(400).json({
						error: "Invalid URL format",
						message: "The provided URL is not a valid Azure Blob Storage URL.",
					});
					return;
				}
			}

			// For any other errors
			res.status(500).json({
				error: "Server error",
				message: "An unexpected error occurred while generating the SAS URL.",
			});
		}
	};

	/**
	 * Upload an image to Azure Blob Storage
	 * @deprecated Use generateUploadPresignedUrl instead for better performance
	 */
	public uploadImageToAzure = async (
		req: Request,
		res: Response,
		next: NextFunction,
	): Promise<void> => {
		try {
			if (!req.file) {
				res.status(400).json({ error: "No file uploaded." });
				return;
			}

			const { buffer, originalname, mimetype } = req.file;
			const result = await this.azureBlobService.uploadFile(
				buffer,
				originalname,
				mimetype,
			);

			// Convert the returned URL to a full Azure URL with SAS token
			const convertedUrl = await this.convertFileUrlToAzureUrl(result.url);

			res.status(200).json({
				message: "File uploaded successfully to Azure Blob Storage.",
				data: {
					...result,
					url: convertedUrl,
				},
			});
		} catch (error) {
			console.error("Error uploading image to Azure:", error);
			next(error);
		}
	};

	/**
	 * Create a new HowTo template with specified taxonomy
	 */
	public createTemplate = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const validatedData = createTemplateSchema.parse(req.body);

			// Construct hierarchy path
			const hierarchyPath = [
				validatedData.sector.id,
				validatedData.subsector.id,
				validatedData.buildType.id,
			];

			// Create the template data
			const templateData = {
				title: validatedData.title,
				description: "", // Empty description for template
				overview: "", // Empty overview for template
				taxonomy: {
					sector: validatedData.sector,
					subsector: validatedData.subsector,
					buildType: validatedData.buildType,
					hierarchyPath,
				},
				metadata: {
					order: 1,
					parentId: undefined, // Top-level document
				},
			};

			const newTemplate = await this.howToRepository.create(templateData);

			res.status(201).json({
				message: "HowTo template created successfully",
				data: newTemplate,
			});
		} catch (error) {
			if (error instanceof z.ZodError) {
				res
					.status(400)
					.json({ error: "Validation error", details: error.errors });
				return;
			}
			next(error);
		}
	};
}
