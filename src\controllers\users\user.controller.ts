import type { Request, Response, NextFunction } from "express";
import type { UserService } from "@/services/user/user.service";
import {
	assignPermissionSchema,
	assignRoleSchema,
	createPermissionSchema,
	createRoleSchema,
	createUserSchema,
	paginationQuerySchema,
	updateUserSchema,
} from "./users.schema";

export class UserController {
	private userService: UserService;

	constructor(userService: UserService) {
		this.userService = userService;
	}

	public async getUsers(req: Request, res: Response, next: NextFunction) {
		try {
			// Parse and validate query parameters
			const paginationQuery = paginationQuerySchema.parse({
				page: req.query.page ? Number(req.query.page) : 1,
				limit: req.query.limit ? Number(req.query.limit) : 10,
				sortBy: req.query.sortBy || 'createdAt',
				sortOrder: req.query.sortOrder || 'desc',
				search: req.query.search ? String(req.query.search) : undefined,
				role: req.query.role ? String(req.query.role) : undefined,
			});

			const result = await this.userService.getUsers(paginationQuery);
			res.status(200).json(result);
		} catch (error) {
			next(error);
		}
	}

	public async createUser(req: Request, res: Response, next: NextFunction) {
		try {
			// Parse and validate only the allowed fields (ignoring any assignedBy from frontend)
			const validatedData = createUserSchema.parse({
				azureAdObjectId: req.body.azureAdObjectId,
				roles: req.body.roles,
				// Explicitly exclude assignedBy from frontend request
			});
			
			if (!validatedData.azureAdObjectId) {
				res.status(400).json({ message: "Azure AD Object ID is required" });
				return;
			}

			// Get the authenticated user's Azure AD Object ID from token validation
			const assignedBy = req.headers["x-azure-ad-object-id"] as string;
			if (!assignedBy) {
				res.status(401).json({ message: "Authentication required" });
				return;
			}

			const user = await this.userService.createUser(validatedData, assignedBy);
			res.status(201).json(user);
		} catch (error) {
			if (error instanceof Error) {
				if (error.message.includes("role IDs do not exist")) {
					res.status(400).json({ message: error.message });
					return;
				}
				if (error.message === "Assigning user not found in system") {
					res.status(400).json({ message: error.message });
					return;
				}
				res.status(400).json({ message: error.message });
				return;
			}
			next(error);
		}
	}

	public async getUser(req: Request, res: Response, next: NextFunction) {
		try {
			const user = await this.userService.getUser(req.params.azureAdObjectId);
			res.status(200).json(user);
		} catch (error) {
			if (error instanceof Error && error.message === "User not found") {
				res.status(404).json({ message: error.message });
				return;
			}
			next(error);
		}
	}

	public async createRole(req: Request, res: Response, next: NextFunction) {
		try {
			const validatedData = createRoleSchema.parse(req.body);
			const role = await this.userService.createRole(validatedData);
			res.status(201).json(role);
		} catch (error) {
			if (error instanceof Error) {
				res.status(400).json({ message: error.message });
				return;
			}
			next(error);
		}
	}

	public async getRoles(req: Request, res: Response, next: NextFunction) {
		try {
			// const validatedData = getRolesSchema.parse(req.body);
			const azureAdObjectId = req.headers["x-azure-ad-object-id"] as string;
			const roles = await this.userService.getRoles(azureAdObjectId);
			res.status(200).json(roles);
		} catch (error) {
			next(error);
		}
	}

	public async getAllRoles(req: Request, res: Response, next: NextFunction) {
		try {
			const roles = await this.userService.getAllRoles();
			res.status(200).json(roles);
		} catch (error) {
			next(error);
		}
	}

	public async assignRole(req: Request, res: Response, next: NextFunction) {
		try {
			const validatedData = assignRoleSchema.parse(req.body);
			if (!validatedData.azureAdObjectId || !validatedData.role) {
				res
					.status(400)
					.json({ message: "Azure AD Object ID and role are required" });
				return;
			}
			const user = await this.userService.assignRole(
				validatedData.azureAdObjectId,
				validatedData.role,
			);
			res.status(200).json(user);
		} catch (error) {
			if (error instanceof Error) {
				res.status(400).json({ message: error.message });
				return;
			}
			next(error);
		}
	}

	public async createPermission(
		req: Request,
		res: Response,
		next: NextFunction,
	) {
		try {
			const validatedData = createPermissionSchema.parse(req.body);
			const permission = await this.userService.createPermission(validatedData);
			res.status(201).json(permission);
		} catch (error) {
			if (error instanceof Error) {
				res.status(400).json({ message: error.message });
				return;
			}
			next(error);
		}
	}

	public async assignPermission(
		req: Request,
		res: Response,
		next: NextFunction,
	) {
		try {
			const validatedData = assignPermissionSchema.parse(req.body);
			const role = await this.userService.assignPermission(
				validatedData.role,
				validatedData.permission,
			);
			res.status(200).json(role);
		} catch (error) {
			next(error);
		}
	}

	public async updateUser(req: Request, res: Response, next: NextFunction) {
		try {
			const { azureAdObjectId } = req.params;
			const validatedData = updateUserSchema.parse(req.body);
			
			if (!azureAdObjectId) {
				res.status(400).json({ message: "Azure AD Object ID is required" });
				return;
			}

			const updatedUser = await this.userService.updateUser(azureAdObjectId, validatedData);
			res.status(200).json(updatedUser);
		} catch (error) {
			if (error instanceof Error) {
				if (error.message === "User not found") {
					res.status(404).json({ message: error.message });
					return;
				}
				if (error.message.includes("role IDs do not exist")) {
					res.status(400).json({ message: error.message });
					return;
				}
				res.status(400).json({ message: error.message });
				return;
			}
			next(error);
		}
	}

	public async deleteUser(req: Request, res: Response, next: NextFunction) {
		try {
			const { azureAdObjectId } = req.params;
			
			if (!azureAdObjectId) {
				res.status(400).json({ message: "Azure AD Object ID is required" });
				return;
			}

			// Get the authenticated user's Azure AD Object ID from token validation
			const deletedBy = req.headers["x-azure-ad-object-id"] as string;
			if (!deletedBy) {
				res.status(401).json({ message: "Authentication required" });
				return;
			}

			const result = await this.userService.deleteUser(azureAdObjectId, deletedBy);
			res.status(200).json(result);
		} catch (error) {
			if (error instanceof Error) {
				if (error.message === "User not found") {
					res.status(404).json({ message: error.message });
					return;
				}
				if (error.message === "Deleting user not found in system") {
					res.status(400).json({ message: error.message });
					return;
				}
				res.status(400).json({ message: error.message });
				return;
			}
			next(error);
		}
	}
}
