import { FeedbackService } from "../feedback.service";
import UserModel from "@/models/user.model";
import { vi, describe, it, expect, beforeEach } from "vitest";
import { Types } from "mongoose";
import type { MongoFeedbackRepository } from "../mongoFeedback.repository";
import type { Feedback } from "../feedback.model";
import type { FeedbackInput, FeedbackCreateInput } from "../feedback.types";

// Mock UserModel
vi.mock("@/models/user.model", () => ({
	default: {
		findOne: vi.fn(),
	},
}));

// Mock logger to avoid console output during tests
vi.mock("@/utils/logging", () => ({
	default: {
		info: vi.fn(),
		warn: vi.fn(),
		error: vi.fn(),
	},
}));

// Helper to create mock feedback data
const createMockFeedback = (overrides = {}): Feedback => ({
	_id: new Types.ObjectId(),
	message: "Test feedback message",
	azureAdObjectId: "test-azure-id",
	createdAt: new Date(),
	page_url: "https://test.com/page",
	page_title: "Test Page",
	...overrides,
});

describe("FeedbackService", () => {
	let feedbackService: FeedbackService;
	let mockFeedbackRepo: MongoFeedbackRepository;

	beforeEach(() => {
		// Reset all mocks
		vi.clearAllMocks();

		// Set up mock repository
		mockFeedbackRepo = {
			create: vi.fn(),
			findAll: vi.fn(),
			findById: vi.fn(),
			findByAzureAdObjectId: vi.fn(),
		} as unknown as MongoFeedbackRepository;

		// Initialize service with mock repository
		feedbackService = new FeedbackService(mockFeedbackRepo);
	});

	describe("createFeedback", () => {
		const mockInput: FeedbackInput = {
			message: "Test feedback",
			azureAdObjectId: "test-azure-id",
			page_url: "https://test.com/page",
			page_title: "Test Page",
		};

		it("should create feedback when user exists", async () => {
			// Mock user exists
			const mockUser = {
				_id: new Types.ObjectId(),
				azureAdObjectId: mockInput.azureAdObjectId,
			};

			// Mock findOne with chainable lean method
			const mockLean = vi.fn().mockResolvedValueOnce(mockUser);
			vi.mocked(UserModel.findOne).mockReturnValueOnce({
				lean: mockLean,
			} as any);

			// Mock repository create
			const mockCreatedFeedback = createMockFeedback({
				userId: mockUser._id,
				azureAdObjectId: mockInput.azureAdObjectId,
				message: mockInput.message,
			});
			vi.mocked(mockFeedbackRepo.create).mockResolvedValueOnce(
				mockCreatedFeedback,
			);

			// Execute method
			const result = await feedbackService.createFeedback(mockInput);

			// Assertions
			expect(UserModel.findOne).toHaveBeenCalledWith({
				azureAdObjectId: mockInput.azureAdObjectId,
			});
			expect(mockLean).toHaveBeenCalled();
			expect(mockFeedbackRepo.create).toHaveBeenCalledWith(
				expect.objectContaining({
					message: mockInput.message,
					azureAdObjectId: mockInput.azureAdObjectId,
					userId: mockUser._id,
					page_url: mockInput.page_url,
					page_title: mockInput.page_title,
				}),
			);
			expect(result).toEqual({
				id: mockCreatedFeedback._id.toString(),
				message: mockCreatedFeedback.message,
				azureAdObjectId: mockCreatedFeedback.azureAdObjectId,
				createdAt: mockCreatedFeedback.createdAt,
				page_url: mockCreatedFeedback.page_url,
				page_title: mockCreatedFeedback.page_title,
			});
		});

		it("should create feedback when user doesn't exist", async () => {
			// Mock user not found
			const mockLean = vi.fn().mockResolvedValueOnce(null);
			vi.mocked(UserModel.findOne).mockReturnValueOnce({
				lean: mockLean,
			} as any);

			// Mock repository create
			const mockCreatedFeedback = createMockFeedback({
				userId: undefined,
				azureAdObjectId: mockInput.azureAdObjectId,
				message: mockInput.message,
			});
			vi.mocked(mockFeedbackRepo.create).mockResolvedValueOnce(
				mockCreatedFeedback,
			);

			// Execute method
			const result = await feedbackService.createFeedback(mockInput);

			// Assertions
			expect(UserModel.findOne).toHaveBeenCalledWith({
				azureAdObjectId: mockInput.azureAdObjectId,
			});
			expect(mockLean).toHaveBeenCalled();
			expect(mockFeedbackRepo.create).toHaveBeenCalledWith(
				expect.objectContaining({
					message: mockInput.message,
					azureAdObjectId: mockInput.azureAdObjectId,
					userId: undefined,
					page_url: mockInput.page_url,
					page_title: mockInput.page_title,
				}),
			);
			expect(result).toEqual({
				id: mockCreatedFeedback._id.toString(),
				message: mockCreatedFeedback.message,
				azureAdObjectId: mockCreatedFeedback.azureAdObjectId,
				createdAt: mockCreatedFeedback.createdAt,
				page_url: mockCreatedFeedback.page_url,
				page_title: mockCreatedFeedback.page_title,
			});
		});
	});

	describe("getAllFeedback", () => {
		it("should return all feedback entries", async () => {
			// Mock repository findAll
			const mockFeedbacks = [
				createMockFeedback({ message: "Feedback 1" }),
				createMockFeedback({ message: "Feedback 2" }),
			];
			vi.mocked(mockFeedbackRepo.findAll).mockResolvedValueOnce(mockFeedbacks);

			// Execute method
			const result = await feedbackService.getAllFeedback();

			// Assertions
			expect(mockFeedbackRepo.findAll).toHaveBeenCalled();
			expect(result).toHaveLength(2);
			expect(result[0]).toEqual({
				id: mockFeedbacks[0]._id.toString(),
				message: mockFeedbacks[0].message,
				azureAdObjectId: mockFeedbacks[0].azureAdObjectId,
				createdAt: mockFeedbacks[0].createdAt,
				page_url: mockFeedbacks[0].page_url,
				page_title: mockFeedbacks[0].page_title,
			});
		});

		it("should return empty array when no feedback exists", async () => {
			// Mock repository findAll empty
			vi.mocked(mockFeedbackRepo.findAll).mockResolvedValueOnce([]);

			// Execute method
			const result = await feedbackService.getAllFeedback();

			// Assertions
			expect(mockFeedbackRepo.findAll).toHaveBeenCalled();
			expect(result).toEqual([]);
		});
	});

	describe("getFeedbackById", () => {
		it("should return feedback when ID exists", async () => {
			// Mock feedback
			const mockFeedback = createMockFeedback();
			const mockFeedbackId = mockFeedback._id.toString();

			// Mock repository findById
			vi.mocked(mockFeedbackRepo.findById).mockResolvedValueOnce(mockFeedback);

			// Execute method
			const result = await feedbackService.getFeedbackById(mockFeedbackId);

			// Assertions
			expect(mockFeedbackRepo.findById).toHaveBeenCalledWith(mockFeedbackId);
			expect(result).toEqual({
				id: mockFeedback._id.toString(),
				message: mockFeedback.message,
				azureAdObjectId: mockFeedback.azureAdObjectId,
				createdAt: mockFeedback.createdAt,
				page_url: mockFeedback.page_url,
				page_title: mockFeedback.page_title,
			});
		});

		it("should return null when ID doesn't exist", async () => {
			// Mock repository findById null
			vi.mocked(mockFeedbackRepo.findById).mockResolvedValueOnce(null);

			// Execute method
			const result = await feedbackService.getFeedbackById("non-existent-id");

			// Assertions
			expect(mockFeedbackRepo.findById).toHaveBeenCalledWith("non-existent-id");
			expect(result).toBeNull();
		});
	});

	describe("getFeedbackByUser", () => {
		it("should return feedback for a specific user", async () => {
			const azureAdObjectId = "test-azure-id";

			// Mock repository findByAzureAdObjectId
			const mockFeedbacks = [
				createMockFeedback({ azureAdObjectId, message: "User Feedback 1" }),
				createMockFeedback({ azureAdObjectId, message: "User Feedback 2" }),
			];
			vi.mocked(mockFeedbackRepo.findByAzureAdObjectId).mockResolvedValueOnce(
				mockFeedbacks,
			);

			// Execute method
			const result = await feedbackService.getFeedbackByUser(azureAdObjectId);

			// Assertions
			expect(mockFeedbackRepo.findByAzureAdObjectId).toHaveBeenCalledWith(
				azureAdObjectId,
			);
			expect(result).toHaveLength(2);
			expect(result[0]).toEqual({
				id: mockFeedbacks[0]._id.toString(),
				message: mockFeedbacks[0].message,
				azureAdObjectId: mockFeedbacks[0].azureAdObjectId,
				createdAt: mockFeedbacks[0].createdAt,
				page_url: mockFeedbacks[0].page_url,
				page_title: mockFeedbacks[0].page_title,
			});
			expect(result[1].message).toBe("User Feedback 2");
		});

		it("should return empty array when user has no feedback", async () => {
			// Mock repository findByAzureAdObjectId empty
			vi.mocked(mockFeedbackRepo.findByAzureAdObjectId).mockResolvedValueOnce(
				[],
			);

			// Execute method
			const result = await feedbackService.getFeedbackByUser(
				"user-with-no-feedback",
			);

			// Assertions
			expect(mockFeedbackRepo.findByAzureAdObjectId).toHaveBeenCalledWith(
				"user-with-no-feedback",
			);
			expect(result).toEqual([]);
		});
	});
});
