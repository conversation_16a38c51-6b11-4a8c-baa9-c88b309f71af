import { Router } from 'express';
import { FeedbackController } from './feedback.controller';
import { FeedbackService } from './feedback.service';
import { MongoFeedbackRepository } from './mongoFeedback.repository';

const router = Router();
const feedbackRepository = new MongoFeedbackRepository();
const feedbackService = new FeedbackService(feedbackRepository);
const feedbackController = new FeedbackController(feedbackService);

// Submit feedback - available to all authenticated users
router.post('/', async (req, res, next) => {
  await feedbackController.submitFeedback(req, res, next);
});

// Get user's own feedback
router.get('/user', async (req, res, next) => {
  await feedbackController.getFeedbackByUser(req, res, next);
});

// For admin access
router.get('/', async (req, res, next) => {
  await feedbackController.getAllFeedback(req, res, next);
});

router.get('/:id', async (req, res, next) => {
  await feedbackController.getFeedbackById(req, res, next);
});

export default router;
