# Stage 1: Build Stage
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Install dependencies needed for building
RUN apk add --no-cache python3 make g++ git

# Copy package files for dependency installation
COPY package.json yarn.lock ./

# Install all dependencies (including devDependencies)
RUN yarn install --frozen-lockfile && \
    yarn cache clean

# Copy prisma schema and generate client
COPY prisma ./prisma/
RUN yarn prisma generate

# Copy source code and config files
COPY . .

# Build the application
RUN yarn build

# Stage 2: Runtime Stage
FROM node:18-alpine AS runtime

# Set working directory
WORKDIR /app

# Set NODE_ENV
ENV NODE_ENV=development

# Copy package files
COPY package.json yarn.lock ./

# Install production dependencies and required dev dependencies for path resolution
RUN yarn install --frozen-lockfile --production && \
    yarn add tsconfig-paths ts-node && \
    yarn cache clean && \
    rm -rf /root/.npm /tmp/*

# Copy built application and Prisma client from builder stage
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules/.prisma ./node_modules/.prisma
COPY --from=builder /app/node_modules/@prisma ./node_modules/@prisma
COPY --from=builder /app/tsconfig.json ./tsconfig.json

# Copy any other necessary files
COPY .env* ./
COPY prisma ./prisma/

# Expose development port
EXPOSE 4000

# Start the application with path resolution support
CMD ["node", "-r", "tsconfig-paths/register", "-r", "ts-node/register/transpile-only", "dist/index.js"]
