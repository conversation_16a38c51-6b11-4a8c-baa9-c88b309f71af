import RoleModel from "@/models/role.model";
import UserModel from "@/models/user.model";
import type { NextFunction, Request, Response } from "express";
import type { UserService } from "@/services/user/user.service";
import logger from "../utils/logging";

export class RoleMiddleware {
	private static instance: RoleMiddleware;
	private userService: UserService;

	private constructor(userService: UserService) {
		this.userService = userService;
	}

	public static getInstance(userService: UserService): RoleMiddleware {
		if (!RoleMiddleware.instance) {
			RoleMiddleware.instance = new RoleMiddleware(userService);
		}
		return RoleMiddleware.instance;
	}

	public checkRole(requiredRoles: string | string[]) {
		// const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];

		return async (
			req: Request,
			res: Response,
			next: NextFunction,
		): Promise<void> => {
			try {
				// TODO: Automatically create user in mongoDB assign roles to users when then sign up using MSAL
				// Get azureAdObjectId from request header or token
				const azureAdObjectId = req.headers["x-azure-ad-object-id"] as string;

				// Find user in database
				const user = await UserModel.findOne({ azureAdObjectId }).lean();
				if (!user) {
					res.status(401).json({
						message: "Unauthorized - Request Access from Digital Team",
					});
					return;
				}
				const rolesArray = Array.isArray(requiredRoles)
					? requiredRoles
					: [requiredRoles];

				// Check if user has any of the required roles
				// Fetch user roles from database
				const userRolesObjectIds = user.roles;

				// Find roles that match the user's roles
				const userRoles = await RoleModel.find({
					_id: { $in: userRolesObjectIds },
				}).lean();

				// Check if user has any of the required roles
				const hasRequiredAccess = userRoles.some((role) =>
					rolesArray.includes(role.name),
				);

				// If user does not have required access, return 401
				if (!hasRequiredAccess) {
					res.status(401).json({
						message: "Unauthorized - Request Access from Digital Team",
					});
					return;
				}
				next();
			} catch (error) {
				logger.error("Error checking user role", { error });
				if (error instanceof Error && error.message === "User not found") {
					res.status(404).json({ message: "User not found" });
					return;
				}
				next(error);
			}
		};
	}
}
