import type { Request, Response, NextFunction } from "express";
import type { MapService } from "@/services/maps/map.service";
import type { FilterInput } from "@/services/maps/map.types";
import logger from "@/utils/logging";

export class MapController {
	constructor(private mapService: MapService) {}

	/**
	 * Get available filters for the map interface
	 */
	public getFilters = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		const filters = await this.mapService.getFilters();
		return res.status(200).json(filters);
	};

	/**
	 * Get filtered project locations for the map
	 */
	public getFilteredProjectLocations = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		const filters = req.body as FilterInput;

		const projectLocations =
			await this.mapService.getFilteredProjectLocations(filters);

		// Check if result contains error object
		if (projectLocations.length === 1 && "error" in projectLocations[0]) {
			return res.status(400).json({ message: projectLocations[0].error });
		}

		return res.status(200).json(projectLocations);
	};

	/**
	 * Get project details by ID
	 */
	public getProjectById = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		const projectId = req.params.projectId;

		if (!projectId) {
			return res.status(400).json({ message: "Project ID is required" });
		}

		const project = await this.mapService.getProjectById(projectId);

		if (!project) {
			return res.status(404).json({ message: "Project not found" });
		}

		return res.status(200).json(project);
	};

	/**
	 * Download filtered projects data
	 */
	public downloadFilteredData = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		const filters = req.body as FilterInput;
		// logger.info('Received download request with filters:', { filters });

		const { filename, data } =
			await this.mapService.downloadFilteredData(filters);

		// Set response headers for file download
		res.setHeader(
			"Content-Type",
			"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
		);
		res.setHeader("Content-Disposition", `attachment; filename=${filename}`);
		res.setHeader("Content-Length", data.length);

		return res.status(200).send(data);
	};
}
