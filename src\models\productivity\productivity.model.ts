import { databaseManager } from "@/infrastructure/database/database-manager";
import {
	prop,
	getModelForClass,
	modelOptions,
	Severity,
} from "@typegoose/typegoose";
import { Types, type Connection } from "mongoose";

@modelOptions({
	schemaOptions: { collection: "clean_productivity_data" },
	options: {
		allowMixed: Severity.ALLOW,
	},
})
export class Productivity {
	@prop({ required: true, type: () => Types.ObjectId })
	public _id!: Types.ObjectId;

	@prop({ required: true })
	public dataSource!: string; // e.g., 'ratesdb'

	@prop({ required: true })
	public phase!: string; // e.g., 'Pre-Construction'

	@prop({ required: true })
	public order1!: number; // e.g., 1

	@prop({ required: true })
	public stage!: string; // e.g., 'Design & Documentation'

	@prop({ required: true })
	public order2!: number; // e.g., 2

	@prop({ required: true })
	public type!: string; // e.g., 'ARCHITECTURAL'

	@prop({ required: true })
	public activity!: string; // e.g., 'ARCHITECTURAL DETAILS'

	@prop()
	public quantity?: number | null;

	@prop()
	public unitOfQuantity?: string | null;

	@prop({ required: true })
	public rate!: number;

	@prop()
	public rateUnit?: string | null;

	@prop()
	public labourResource?: number | null;

	@prop()
	public crewSizeResource?: number | null;

	@prop()
	public plantAndEquipmentResource?: number | null;

	@prop()
	public canFlip?: boolean | null;

	@prop()
	public notes?: string | null;

	@prop({ required: true })
	public categoryType!: string; // e.g., 'building'
}

// Export a function to get the model after the connection is ready
export function getProductivityModel() {
	const conn: Connection = databaseManager.getConnection("productivity");
	return getModelForClass(Productivity, { existingConnection: conn });
}

export type ProductivityDocument = Productivity;
