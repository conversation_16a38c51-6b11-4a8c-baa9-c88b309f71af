import { z } from 'zod';

export const getProjectsSchema = z.object({
  page: z.string().optional().default('1').transform(Number),
  limit: z.string().optional().default('10').transform(Number),
});

export const getProjectByIdSchema = z.object({
  projectId: z.string(),
});

export const searchItemFilterSchema = z.object({
  fieldName: z.string(),
  page: z.string().optional().default('1').transform(Number),
  limit: z.string().optional().default('10').transform(Number),
  queryTerm: z.string().optional(),
});

export const getMarketSharePercentagesSchema = z.object({
  filters: z
    .record(
      z.string(),
      z.union([
        z.array(z.string()),
        z.array(
          z.object({
            value: z.string(),
            label: z.string(),
          })
        ),
      ])
    )
    .optional(),
  deselectedRows: z.array(z.object({ projectId: z.string(), index: z.string() })).optional(),
});




// Define a reusable filter record schema
const filterRecordSchema = z.record(
  z.string(),
  z.union([
    z.array(z.string()),
    z.array(
      z.object({
        value: z.string(),
        label: z.string(),
      })
    ),
  ])
);

// Define a schema that can handle nested filters
export const getFilteredProjectsSchema = z.object({
  // Handle regular filters
  filters: z
    .union([
      // Option 1: Regular filters record
      filterRecordSchema,
      // Option 2: Nested filters.filters structure
      z.object({
        filters: filterRecordSchema,
      }),
    ])
    .optional(),
  page: z.union([z.string(), z.number()]).optional().default('1').transform(Number),
  limit: z.union([z.string(), z.number()]).optional().default('10').transform(Number),
});

export type GetProjectsSchema = z.infer<typeof getProjectsSchema>;
export type GetProjectByIdSchema = z.infer<typeof getProjectByIdSchema>;
export type SearchItemFilterSchema = z.infer<typeof searchItemFilterSchema>;
export type GetMarketSharePercentagesSchema = z.infer<typeof getMarketSharePercentagesSchema>;
export type GetFilteredProjectsSchema = z.infer<typeof getFilteredProjectsSchema>;
