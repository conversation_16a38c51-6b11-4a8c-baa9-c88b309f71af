# ElasticSearch API

This set of endpoints allows you to search and retrieve documents from ElasticSearch.

## Configuration

The ElasticSearch client is configured using environment variables:

```env
ELASTIC_NODE=https://your-elasticsearch-server:9200
ELASTIC_USERNAME=elastic
ELASTIC_PASSWORD=your-password
ELASTIC_INDEX=sharepoint_crawl
ELASTIC_REJECT_UNAUTHORIZED=false
```

## API Endpoints

### Search Documents

```
GET /api/search/query
```

Query Parameters:
- `query` (required): The search term to look for
- `field` (optional): The field to search in (default: "content")
- `size` (optional): Number of results to return (default: 10)
- `includeContent` (optional): Whether to include document content in results (default: false)
- `fileType` (optional): Filter by file extension (pdf, docx, etc.)
- `accessibleOnly` (optional): Only return accessible documents (default: false)

Example:
```bash
curl -X GET "http://localhost:4000/api/search/query?query=meeting&field=content&size=5&fileType=pdf&accessibleOnly=true"
```

### Count Documents

```
GET /api/search/count
```

Query Parameters:
- `query` (optional): The search term to count occurrences for
- `field` (optional): The field to search in (default: "content")

Example:
```bash
curl -X GET "http://localhost:4000/api/search/count?query=budget&field=content"
```

### Get Document by ID

```
GET /api/search/document/:id
```

Path Parameters:
- `id` (required): The document ID to retrieve

Example:
```bash
curl -X GET "http://localhost:4000/api/search/document/doc123"
```

## Response Formats

### Search Results

```json
[
  {
    "score": 1.5,
    "url": "https://example.com/document.pdf",
    "file_name": "document.pdf",
    "type": "pdf",
    "size": 1024,
    "accessible": true,
    "scan_date": "2023-01-01T00:00:00Z",
    "highlights": [
      "This is a <mark>meeting</mark> document"
    ],
    "content": "This is the full content of the document..." 
  }
]
```

### Count Response

```json
{
  "count": 42
}
```

### Document Response

```json
{
  "url": "https://example.com/document.pdf",
  "file_name": "document.pdf",
  "file_type": "pdf",
  "content_type": "application/pdf",
  "file_size": 1024,
  "content_size": 1024,
  "accessible": true,
  "timestamp": "2023-01-01T00:00:00Z",
  "content": "This is the full content of the document..."
}
``` 