import logger from "@/utils/logging";
import dotenv from "dotenv";

const nodeEnv = process.env.NODE_ENV || "development";
dotenv.config({ path: `./.env.${nodeEnv}` });
logger.info(`Loaded environment variables from ./.env.${nodeEnv}`);

// Single configuration object that uses the loaded environment variables
const config = {
	port: process.env.PORT || 4000,
	database: {
		postgres: {
			host: process.env.POSTGRES_HOST || "localhost",
			port: process.env.POSTGRES_PORT || 5432,
			username: process.env.POSTGRES_USER || "postgres",
			password: process.env.POSTGRES_PASSWORD || "postgres",
			database: process.env.POSTGRES_DB || "dev_database",
			max: Number.parseInt(process.env.DB_POOL_MAX || "20"),
			idleTimeoutMillis: Number.parseInt(
				process.env.DB_POOL_IDLE_TIMEOUT || "30000",
			),
			connectionTimeoutMillis: 2000,
			ssl:
				nodeEnv === "production"
					? {
							rejectUnauthorized: true,
							ca: process.env.DB_SSL_CA,
							cert: process.env.DB_SSL_CERT,
							key: process.env.DB_SSL_KEY,
						}
					: process.env.DB_SSL === "true"
						? { rejectUnauthorized: false }
						: false,
		},
		mongo: {
			uri:
				process.env.MONGO_URI ||
				`mongodb://localhost:27017/einstein${nodeEnv === "test" ? "_testing_db" : ""}`,
			productivityUri:
				process.env.MONGO_PRODUCTIVITY_URI ||
				`mongodb://localhost:27017/productivity${nodeEnv === "test" ? "_testing_db" : ""}`,
		},
	},
	elasticsearch: {
		node: process.env.ELASTIC_NODE || "https://localhost:9200",
		username: process.env.ELASTIC_USERNAME || "elastic",
		password: process.env.ELASTIC_PASSWORD || "",
		indexName: process.env.ELASTIC_INDEX || "sharepoint_crawl",
		rejectUnauthorized: process.env.ELASTIC_REJECT_UNAUTHORIZED !== "false",
	},
	azure: {
		storageConnectionString: process.env.AZURE_STORAGE_CONNECTION_STRING,
		storageContainerName:
			process.env.AZURE_STORAGE_CONTAINER_NAME || "methodology-files",
	},
	logger: {
		logPath: process.env.LOG_PATH || `./logs/${nodeEnv}.log`,
		serviceName: "einstein-backend",
		levels: {
			error: 0,
			warn: 1,
			info: 2,
			http: 3,
			debug: 4,
		},
		colors: {
			error: "red",
			warn: "yellow",
			info: "green",
			http: "magenta",
			debug: "white",
		},
		level: () => {
			return nodeEnv === "development" ? "debug" : "info";
		},
	},
	auth: {
		isEnabled: process.env.AUTH_ENABLED !== "false", // defaults to true
	},
	rateLimit: {
		general: {
			windowMs: Number.parseInt(process.env.RATE_LIMIT_WINDOW_MS || "900000"), // 15 minutes
			max: Number.parseInt(process.env.RATE_LIMIT_MAX || "100"),
		},
		api: {
			windowMs: Number.parseInt(
				process.env.API_RATE_LIMIT_WINDOW_MS || "60000",
			), // 1 minute
			max: Number.parseInt(process.env.API_RATE_LIMIT_MAX || "100"),
		},
		auth: {
			windowMs: Number.parseInt(
				process.env.AUTH_RATE_LIMIT_WINDOW_MS || "900000",
			), // 15 minutes
			max: Number.parseInt(process.env.AUTH_RATE_LIMIT_MAX || "100"),
		},
		upload: {
			windowMs: Number.parseInt(
				process.env.UPLOAD_RATE_LIMIT_WINDOW_MS || "3600000",
			), // 1 hour
			max: Number.parseInt(process.env.UPLOAD_RATE_LIMIT_MAX || "10"),
		},
		search: {
			windowMs: Number.parseInt(
				process.env.SEARCH_RATE_LIMIT_WINDOW_MS || "60000",
			), // 1 minute
			max: Number.parseInt(process.env.SEARCH_RATE_LIMIT_MAX || "80"),
		},
	},
};

export default config;
