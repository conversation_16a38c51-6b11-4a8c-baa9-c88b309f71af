import config from "@/config/config";
import { uploadLimiter } from "@/middlewares/rateLimiting";
import { BlobServiceClient } from "@azure/storage-blob";
import { Router } from "express";
import multer from "multer";
import { AzureBlobService } from "./azureBlob.service";
import { FileController } from "./file.controller";
import { FileService } from "./file.service";
import { MongoFileRepository } from "./mongoFile.repository";

const fileRepository = new MongoFileRepository();
// Ensure connection string exists before using it
const connectionString = config.azure.storageConnectionString || "";
if (!connectionString) {
	throw new Error("Azure storage connection string is not configured");
}

const azureBlobService = new AzureBlobService(
	BlobServiceClient.fromConnectionString(connectionString),
);
const fileService = new FileService(fileRepository, azureBlobService);
const fileController = new FileController(fileService);

const router = Router();
const upload = multer();

router.post(
	"/upload",
	uploadLimiter,
	upload.array("files", 10),
	async (req, res) => {
		await fileController.uploadFiles(req, res);
	},
);

router.post("/grant-access", async (req, res) => {
	await fileController.grantAccessToFile(req, res);
});

router.get("/user", async (req, res) => {
	await fileController.getUserFiles(req, res);
});

router.get("/user/:userId", async (req, res) => {
	await fileController.getFileByUserId(req, res);
});

// router.get('/:fileName', async (req, res) => {
//   await fileController.viewFile(req, res);
// });

router.get("/:containerName/:blob(*)", async (req, res) => {
	await fileController.viewFile(req, res);
});

export default router;
