import RoleModel from "@/models/role.model";
import PermissionModel from "@/models/permission.model";
import logger from "@/utils/logging";
import { Types } from "mongoose";
import { seedConfig, seedOptions, type SeedRole } from "./config";

/**
 * Seeds roles into the database and assigns permissions to them
 * This function is idempotent - it can be run multiple times safely
 */
export async function seedRoles(): Promise<void> {
	const { roles } = seedConfig;
	const { updateExisting, verbose, recreate, dryRun } = seedOptions;

	logger.info(`Starting to seed ${roles.length} roles...`);

	if (dryRun) {
		logger.info("🔍 DRY RUN MODE - No database changes will be made");
	}

	if (recreate && !dryRun) {
		logger.warn("⚠️  RECREATE mode: Deleting all existing roles...");
		await RoleModel.deleteMany({});
		logger.info("✅ All roles deleted");
	} else if (recreate && dryRun) {
		logger.warn("🔍 DRY RUN: Would delete all existing roles");
	}

	// First, ensure all permissions exist and get their IDs
	const permissionMap = await getPermissionMap();

	const results = {
		created: 0,
		updated: 0,
		skipped: 0,
		errors: 0,
	};

	for (const roleData of roles) {
		try {
			await seedRole(
				roleData,
				permissionMap,
				updateExisting,
				verbose,
				dryRun,
				results,
			);
		} catch (error) {
			results.errors++;
			logger.error(`❌ Error seeding role "${roleData.name}":`, error);
		}
	}

	// Summary
	logger.info(`📊 Role seeding ${dryRun ? "(DRY RUN) " : ""}summary:`);
	logger.info(
		`   ✅ ${dryRun ? "Would create" : "Created"}: ${results.created}`,
	);
	logger.info(
		`   🔄 ${dryRun ? "Would update" : "Updated"}: ${results.updated}`,
	);
	logger.info(`   ⏭️  Skipped: ${results.skipped}`);
	if (results.errors > 0) {
		logger.error(`   ❌ Errors: ${results.errors}`);
	}

	logger.info(`✅ Role seeding ${dryRun ? "(DRY RUN) " : ""}completed!`);
}

async function getPermissionMap(): Promise<Map<string, Types.ObjectId>> {
	const { dryRun } = seedOptions;

	if (dryRun) {
		// In dry run mode, simulate the permissions that would exist
		// by using the permissions from the config
		const { permissions } = seedConfig;
		const permissionMap = new Map<string, Types.ObjectId>();

		for (const permission of permissions) {
			// Create a fake ObjectId for simulation
			const fakeId = new Types.ObjectId();
			permissionMap.set(permission.name, fakeId);
		}

		logger.info(`📋 Simulating ${permissionMap.size} permissions (DRY RUN)`);
		return permissionMap;
	}

	// Normal mode - query the database
	const permissions = await PermissionModel.find({}).lean();
	const permissionMap = new Map<string, Types.ObjectId>();

	for (const permission of permissions) {
		permissionMap.set(permission.name, permission._id);
	}

	logger.info(`📋 Found ${permissionMap.size} existing permissions`);
	return permissionMap;
}

async function seedRole(
	roleData: SeedRole,
	permissionMap: Map<string, Types.ObjectId>,
	updateExisting: boolean,
	verbose: boolean,
	dryRun: boolean,
	results: {
		created: number;
		updated: number;
		skipped: number;
		errors: number;
	},
): Promise<void> {
	// Validate that all permissions exist
	const missingPermissions = roleData.permissions.filter(
		(permName) => !permissionMap.has(permName),
	);

	if (missingPermissions.length > 0) {
		throw new Error(
			`Role "${roleData.name}" references non-existent permissions: ${missingPermissions.join(", ")}`,
		);
	}

	// Convert permission names to ObjectIds
	const permissionIds = roleData.permissions.map((permName) => {
		const permissionId = permissionMap.get(permName);
		if (!permissionId) {
			throw new Error(`Permission "${permName}" not found in map`);
		}
		return permissionId;
	});

	const existingRole = await RoleModel.findOne({
		name: roleData.name,
	}).lean();

	if (existingRole) {
		if (updateExisting) {
			// Check if update is needed
			const needsUpdate =
				existingRole.description !== roleData.description ||
				!arraysEqual(
					(existingRole.permissions || []).map((p) => p.toString()).sort(),
					permissionIds.map((p) => p.toString()).sort(),
				);

			if (needsUpdate) {
				if (!dryRun) {
					await RoleModel.findOneAndUpdate(
						{ name: roleData.name },
						{
							description: roleData.description,
							permissions: permissionIds,
						},
						{ new: true },
					);
				}
				results.updated++;
				if (verbose) {
					if (!dryRun) {
						logger.info(
							`🔄 Updated role: ${roleData.name} (${roleData.permissions.length} permissions)`,
						);
					} else {
						logger.info(
							`🔍 DRY RUN: Would update role: ${roleData.name} (${roleData.permissions.length} permissions)`,
						);
					}
				}
			} else {
				results.skipped++;
				if (verbose) {
					logger.info(`⏭️  Skipped role (no changes): ${roleData.name}`);
				}
			}
		} else {
			results.skipped++;
			if (verbose) {
				logger.info(`⏭️  Skipped existing role: ${roleData.name}`);
			}
		}
	} else {
		// Create new role
		if (!dryRun) {
			await RoleModel.create({
				name: roleData.name,
				description: roleData.description,
				permissions: permissionIds,
			});
		}
		results.created++;
		if (verbose) {
			if (!dryRun) {
				logger.info(
					`✅ Created role: ${roleData.name} (${roleData.permissions.length} permissions)`,
				);
			} else {
				logger.info(
					`🔍 DRY RUN: Would create role: ${roleData.name} (${roleData.permissions.length} permissions)`,
				);
			}
		}
	}
}

function arraysEqual(a: string[], b: string[]): boolean {
	return a.length === b.length && a.every((val, i) => val === b[i]);
}

// Allow running this script directly
if (require.main === module) {
	(async () => {
		try {
			// Import database connection
			const { connectMongo } = await import(
				"@/infrastructure/database/mongo.connection"
			);
			const config = await import("@/config/config");

			// Connect to MongoDB
			await connectMongo(config.default.database.mongo.uri);
			logger.info("📦 Connected to MongoDB");

			// Run the seeder
			await seedRoles();

			process.exit(0);
		} catch (error) {
			logger.error("💥 Error in role seeding:", error);
			process.exit(1);
		}
	})();
}
