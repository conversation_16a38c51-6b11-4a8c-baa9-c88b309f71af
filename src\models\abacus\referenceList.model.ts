// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { prop, getModelForClass, modelOptions, ReturnModelType } from '@typegoose/typegoose';

type ReferenceList_Type =
  | 'procurement_model'
  | 'construction_cost_source'
  | 'land_type'
  | 'site_area'
  | 'sector_mapping';

@modelOptions({
  schemaOptions: {
    collection: 'reference_list',
    timestamps: true,
  },
})
class ReferenceList {
  @prop({ required: true, type: () => String })
  public type!: ReferenceList_Type;

  @prop({ required: true, type: () => String })
  public sector_code!: string;

  @prop({ required: true, type: () => String })
  public sector!: string;

  @prop({ required: true, type: () => String })
  public sub_sector!: string;

  @prop({ required: false, type: () => String })
  public source_of_construction_cost!: string;

  @prop({ required: false, type: () => String })
  public level_of_estimate!: string;

  @prop({ required: false, type: () => String })
  public land_type!: string;

  @prop({ required: false, type: () => Number })
  public site_area!: number;

  @prop({ required: false, type: () => String })
  public procurement_model!: string;
}

export const ReferenceListModel = getModelForClass(ReferenceList);
export default ReferenceList;
