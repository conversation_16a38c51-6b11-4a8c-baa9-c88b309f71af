import { MongoFeedbackRepository } from "../mongoFeedback.repository";
import { FeedbackModel } from "../feedback.model";
import { Types } from "mongoose";
import { vi, describe, it, expect, beforeEach, afterEach } from "vitest";
import { HttpException } from "@/utils/exceptions/http.exception";
import type { Feedback } from "../feedback.model";
import type { FeedbackCreateInput } from "../feedback.types";

// Create mock instance methods that we'll use in tests
const mockSave = vi.fn();
const mockToObject = vi.fn();
const mockLean = vi.fn();

// Mock the FeedbackModel
vi.mock("../feedback.model", () => {
	// Create a constructor function that can be instantiated with 'new'
	const MockFeedbackModel = function (this: any, data: any) {
		Object.assign(this, data);
		this.save = mockSave;
		this.toObject = mockToObject;
		return this;
	} as any;

	// Add static methods to the constructor
	MockFeedbackModel.find = vi.fn(() => ({
		sort: vi.fn(() => ({
			lean: mockLean,
		})),
	}));
	MockFeedbackModel.findById = vi.fn(() => ({
		lean: mockLean,
	}));

	return {
		FeedbackModel: MockFeedbackModel,
	};
});

// Mock the HttpException
vi.mock("@/utils/exceptions/http.exception", () => ({
	HttpException: class MockHttpException extends Error {
		status: number;
		message: string;

		constructor(status: number, message: string) {
			super(message);
			this.status = status;
			this.message = message;
		}
	},
}));

describe("MongoFeedbackRepository", () => {
	let repository: MongoFeedbackRepository;

	const mockFeedbackData: FeedbackCreateInput = {
		_id: new Types.ObjectId(),
		message: "Test feedback message",
		azureAdObjectId: "test-azure-id",
		page_url: "https://test.com/page",
		page_title: "Test Page",
	};

	const mockFeedbackWithId: Feedback = {
		...mockFeedbackData,
		_id: new Types.ObjectId(),
		createdAt: new Date(),
		updatedAt: new Date(),
	};

	beforeEach(() => {
		repository = new MongoFeedbackRepository();
		vi.clearAllMocks();
	});

	afterEach(() => {
		vi.resetAllMocks();
	});

	describe("create", () => {
		it("should create a new feedback document", async () => {
			// Arrange
			// Configure mock functions for this test
			mockSave.mockResolvedValueOnce(undefined);
			mockToObject.mockReturnValueOnce(mockFeedbackWithId);

			// Act
			const result = await repository.create(mockFeedbackData);

			// Assert
			expect(mockSave).toHaveBeenCalled();
			expect(mockToObject).toHaveBeenCalled();
			expect(result).toEqual(mockFeedbackWithId);
		});

		it("should throw an error if save fails", async () => {
			// Arrange
			const mockError = new Error("Database error");
			mockSave.mockRejectedValueOnce(mockError);

			// Act & Assert
			await expect(repository.create(mockFeedbackData)).rejects.toThrow(
				mockError,
			);
			expect(mockSave).toHaveBeenCalled();
		});
	});

	describe("findAll", () => {
		it("should return all feedback documents", async () => {
			// Arrange
			const mockFeedbacks = [
				mockFeedbackWithId,
				{
					...mockFeedbackData,
					_id: new Types.ObjectId(),
					message: "Another feedback",
					createdAt: new Date(),
					updatedAt: new Date(),
				},
			];
			mockLean.mockReturnValueOnce(mockFeedbacks);

			// Act
			const result = await repository.findAll();

			// Assert
			expect(FeedbackModel.find).toHaveBeenCalled();
			expect(mockLean).toHaveBeenCalled();
			expect(result).toEqual(mockFeedbacks);
		});

		it("should return an empty array when no feedback exists", async () => {
			// Arrange
			mockLean.mockReturnValueOnce([]);

			// Act
			const result = await repository.findAll();

			// Assert
			expect(FeedbackModel.find).toHaveBeenCalled();
			expect(mockLean).toHaveBeenCalled();
			expect(result).toEqual([]);
		});
	});

	describe("findById", () => {
		it("should return feedback when ID exists", async () => {
			// Arrange
			const mockId = mockFeedbackWithId._id.toString();
			mockLean.mockReturnValueOnce(mockFeedbackWithId);

			// Act
			const result = await repository.findById(mockId);

			// Assert
			expect(FeedbackModel.findById).toHaveBeenCalledWith(mockId);
			expect(mockLean).toHaveBeenCalled();
			expect(result).toEqual(mockFeedbackWithId);
		});

		it("should return null when ID doesn't exist", async () => {
			// Arrange
			const mockId = new Types.ObjectId().toString();
			mockLean.mockReturnValueOnce(null);

			// Act
			const result = await repository.findById(mockId);

			// Assert
			expect(FeedbackModel.findById).toHaveBeenCalledWith(mockId);
			expect(mockLean).toHaveBeenCalled();
			expect(result).toBeNull();
		});

		it("should throw HttpException when ID is invalid", async () => {
			// Arrange
			const invalidId = "invalid-id";

			// Mock Types.ObjectId.isValid to return false for invalid ID
			vi.spyOn(Types.ObjectId, "isValid").mockReturnValueOnce(false);

			// Act & Assert
			await expect(repository.findById(invalidId)).rejects.toThrow(
				HttpException,
			);
			expect(Types.ObjectId.isValid).toHaveBeenCalledWith(invalidId);
			expect(FeedbackModel.findById).not.toHaveBeenCalled();
		});
	});

	describe("findByAzureAdObjectId", () => {
		it("should return feedback for a specific user", async () => {
			// Arrange
			const azureAdObjectId = "test-azure-id";
			const mockFeedbacks = [
				mockFeedbackWithId,
				{
					...mockFeedbackData,
					_id: new Types.ObjectId(),
					message: "Another feedback from same user",
					azureAdObjectId,
					createdAt: new Date(),
					updatedAt: new Date(),
				},
			];
			mockLean.mockReturnValueOnce(mockFeedbacks);

			// Act
			const result = await repository.findByAzureAdObjectId(azureAdObjectId);

			// Assert
			expect(FeedbackModel.find).toHaveBeenCalledWith({ azureAdObjectId });
			expect(mockLean).toHaveBeenCalled();
			expect(result).toEqual(mockFeedbacks);
		});

		it("should return empty array when user has no feedback", async () => {
			// Arrange
			const azureAdObjectId = "user-with-no-feedback";
			mockLean.mockReturnValueOnce([]);

			// Act
			const result = await repository.findByAzureAdObjectId(azureAdObjectId);

			// Assert
			expect(FeedbackModel.find).toHaveBeenCalledWith({ azureAdObjectId });
			expect(mockLean).toHaveBeenCalled();
			expect(result).toEqual([]);
		});
	});
});
