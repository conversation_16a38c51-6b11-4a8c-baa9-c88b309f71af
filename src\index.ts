import config from './config/config';
import { startServer, stopServer } from './infrastructure/server';
import logger from './utils/logging';

// Start the application
const startApp = async () => {
  try {
    await startServer(Number(config.port));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', { error });
      process.exit(1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection:', { reason, promise });
      process.exit(1);
    });
  } catch (error) {
    logger.error('Error starting server:', { error });
    process.exit(1);
  }
};

startApp();
