import type {
	CreateHowToGuideData,
	HowToGuide,
	UpdateHowToGuideData,
} from "../../models/howTo/howTo.model";

// =====================================================
// REPOSITORY TYPES
// =====================================================

export interface TaxonomyItem {
	id: string;
	label: string;
	description?: string;
}

export interface AttachedFileInput {
	url: string;
	label: string;
}

export interface FilterOptions {
	sectorId?: string;
	subsectorId?: string;
	buildTypeId?: string;
	page?: number;
	limit?: number;
}

export interface SearchOptions {
	searchTerm: string;
	page?: number;
	limit?: number;
}

export interface PaginationResult<T> {
	documents: T[];
	pagination: {
		page: number;
		limit: number;
		total: number;
		pages: number;
	};
}

export interface TaxonomyOption {
	_id: string;
	label: string;
	description?: string;
	count: number;
}

// =====================================================
// REPOSITORY INTERFACE
// =====================================================

export interface IHowToRepository {
	// CRUD Operations
	create(data: CreateHowToGuideData): Promise<HowToGuide>;
	findById(id: string): Promise<HowToGuide | null>;
	findAll(page?: number, limit?: number): Promise<PaginationResult<HowToGuide>>;
	update(id: string, data: UpdateHowToGuideData): Promise<HowToGuide | null>;
	delete(id: string): Promise<boolean>;

	// Specific Field Updates
	updateTitle(id: string, title: string): Promise<HowToGuide | null>;
	updateDescription(
		id: string,
		description: string,
	): Promise<HowToGuide | null>;
	updateOverview(id: string, overview: string): Promise<HowToGuide | null>;
	updateTaxonomy(
		id: string,
		sector?: TaxonomyItem,
		subsector?: TaxonomyItem,
		buildType?: TaxonomyItem,
	): Promise<HowToGuide | null>;
	updateOrder(id: string, order: number): Promise<HowToGuide | null>;
	updateParent(id: string, parentId: string | null): Promise<HowToGuide | null>;

	// File Operations
	addAttachedFile(
		id: string,
		file: AttachedFileInput,
	): Promise<HowToGuide | null>;
	removeAttachedFile(id: string, fileId: string): Promise<HowToGuide | null>;
	replaceAttachedFiles(
		id: string,
		files: AttachedFileInput[],
	): Promise<HowToGuide | null>;

	// Filter Operations
	getSectors(): Promise<TaxonomyOption[]>;
	getSubsectors(sectorId: string): Promise<TaxonomyOption[]>;
	getBuildTypes(
		sectorId: string,
		subsectorId: string,
	): Promise<TaxonomyOption[]>;

	// Search and Filter Operations
	findByFilters(options: FilterOptions): Promise<PaginationResult<HowToGuide>>;
	findByHierarchy(hierarchyLevel: string): Promise<HowToGuide[]>;
	search(options: SearchOptions): Promise<PaginationResult<HowToGuide>>;
	findWithAttachments(): Promise<HowToGuide[]>;
	findByParent(parentId: string): Promise<HowToGuide[]>;
	findRecent(limit?: number): Promise<HowToGuide[]>;

	// Utility Operations
	generateSasUrlForBlob(blobUrl: string, expiryMinutes?: number): string;
}
