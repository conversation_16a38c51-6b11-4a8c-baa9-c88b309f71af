import { Router } from "express";
import { SectorController } from "./sector.controller";

const router = Router();
const sectorController = new SectorController();

// Get all sectors
router.get("/", async (req, res, next) => {
	await sectorController.getAllSectors(req, res, next);
});

// Get sectors by type
router.get("/by-type", async (req, res, next) => {
	await sectorController.getSectorsByType(req, res, next);
});

// Create a new sector entry
router.post("/", async (req, res, next) => {
	await sectorController.createSector(req, res, next);
});

export default router;
