/**
 * This module defines the Feedback model for storing user feedback in the system.
 * It captures feedback messages from users along with user identification information.
 */

import {
	type Ref,
	getModelForClass,
	modelOptions,
	prop,
} from "@typegoose/typegoose";
import { User } from "./user.model";

/**
 * Configuration for the Feedback model
 * - Collection name: feedback
 * - Includes timestamps for document creation and updates
 */
@modelOptions({
	schemaOptions: {
		collection: "feedback",
		timestamps: true,
	},
})
/**
 * Class representing user feedback in the system.
 * Feedback is linked to the user who submitted it.
 */
class Feedback {
	// Timestamps added by Mongoose
	createdAt?: Date;
	updatedAt?: Date;
	/**
	 * The feedback message submitted by the user
	 * @prop required - Feedback must have a message
	 * @prop trim - Whitespace will be trimmed
	 */
	@prop({ required: true, trim: true, type: () => String })
	public message!: string;

	/**
	 * Reference to the User who submitted the feedback
	 * @prop required - false (user might not exist in our database)
	 * @prop ref - References the User model
	 */
	@prop({ required: false, ref: () => User })
	public userId?: Ref<User>;

	/**
	 * The Azure AD Object ID of the user who submitted the feedback
	 * This is stored directly for easier querying and in case the user record is deleted
	 * @prop required - true
	 * @prop trim - Whitespace will be trimmed
	 */
	@prop({ required: true, trim: true, type: () => String })
	public azureAdObjectId!: string;
}

// Create and export the Mongoose model for Feedback
const FeedbackModel = getModelForClass(Feedback);
export { FeedbackModel, Feedback };
