{"generated_at": "2025-07-18T18:30:23.174515", "version": "2.1.0", "source_collection": "accumulated_duration_data", "total_documents": 248, "common_filters": [{"field": "type", "label": "Sub Sector", "type": "dropdown", "options": ["commercial", "education", "health"], "count": 3}, {"field": "total_project_value", "label": "Project Value", "type": "range", "min": 0, "max": **********, "formatted_max": "3,200,000,000"}, {"field": "unique_items.project_location", "label": "Location", "type": "dropdown", "options": ["ACT", "NSW", "SA", "TAS", "UAE", "VIC", "WA"], "count": 7}, {"field": "contract_type.planned", "label": "Contract Type", "type": "dropdown", "options": ["Construct Only", "D&C", "Lump <PERSON>", "Other"], "count": 4}], "dynamic_filters": {"commercial": {"type": "commercial", "filters": [{"field": "unique_items.Building_Type", "label": "Building Type", "item_name": "Building_Type", "occurrences": 37, "type": "dropdown", "options": ["High Rise (>15 floors)", "Low Rise (3 floors or less)", "Medium Rise (4 - 15 Floors)"], "options_count": 3}, {"field": "unique_items.Total_no_of_Tower_Floors_(no)", "label": "Tower Floors", "item_name": "Total_no_of_Tower_Floors_(no)", "occurrences": 37, "type": "range", "min": 2, "max": 60}, {"field": "unique_items.Total_no_of_Podium_Floors_(no)", "label": "Podium Floors", "item_name": "Total_no_of_Podium_Floors_(no)", "occurrences": 37, "type": "range", "min": 1, "max": 11}, {"field": "unique_items.Total_no_of_Basement_Floors_(no)", "label": "Basement Floors", "item_name": "Total_no_of_Basement_Floors_(no)", "occurrences": 37, "type": "range", "min": 1, "max": 5}], "total_filters": 4, "configured": true}, "education": {"type": "education", "filters": [{"field": "unique_items.Education_Level", "label": "Education Level", "item_name": "Education_Level", "occurrences": 168, "type": "dropdown", "options": ["Other", "Primary", "Secondary", "Tertiary"], "options_count": 4}, {"field": "unique_items.Total_no_of_Building_Floors_above_Ground", "label": "Building Floors above Ground", "item_name": "Total_no_of_Building_Floors_above_Ground", "occurrences": 168, "type": "range", "min": 0, "max": 32}, {"field": "unique_items.Total_no_of_Basement_Floors_(no)", "label": "Basement Floors", "item_name": "Total_no_of_Basement_Floors_(no)", "occurrences": 168, "type": "range", "min": 0, "max": 4}, {"field": "unique_items.Project_Type", "label": "Project Type", "item_name": "Project_Type", "occurrences": 168, "type": "dropdown", "options": ["New Build", "Refurbishment", "Refurbishment & Extension"], "options_count": 3}], "total_filters": 4, "configured": true}, "health": {"type": "health", "filters": [{"field": "unique_items.Hospital_Type", "label": "Hospital Type", "item_name": "Hospital_Type", "occurrences": 34, "type": "dropdown", "options": ["Other", "Private", "Public"], "options_count": 3}, {"field": "unique_items.Building_Type", "label": "Building Type", "item_name": "Building_Type", "occurrences": 34, "type": "dropdown", "options": ["General", "Mental Health", "Specialty Hospital", "Women's Hospital"], "options_count": 4}, {"field": "unique_items.Total_no_of_Building_Floors_above_Ground", "label": "Building Floors above Ground", "item_name": "Total_no_of_Building_Floors_above_Ground", "occurrences": 34, "type": "range", "min": 1, "max": 18}, {"field": "unique_items.Total_no_of_Basement_Floors_(no)", "label": "Basement Floors", "item_name": "Total_no_of_Basement_Floors_(no)", "occurrences": 34, "type": "range", "min": 0, "max": 3}, {"field": "unique_items.Total_no_of_Beds", "label": "No. of Beds", "item_name": "Total_no_of_Beds", "occurrences": 34, "type": "range", "min": 12, "max": 372}, {"field": "unique_items.Project_Type", "label": "Project Type", "item_name": "Project_Type", "occurrences": 34, "type": "dropdown", "options": ["Extension", "New Build", "New Build & Extension", "New Build & Refurbishment", "New Build, Refurbishment, Extension", "Refurbishment", "Refurbishment & Extension"], "options_count": 7}], "total_filters": 6, "configured": true}}, "statistics": {"common_filters_count": 4, "dynamic_filter_types": 3, "total_dynamic_filters": 14, "total_dropdown_options": 14}}