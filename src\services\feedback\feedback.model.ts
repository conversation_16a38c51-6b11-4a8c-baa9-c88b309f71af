import { prop, getModelForClass, modelOptions, Ref } from '@typegoose/typegoose';
import { User } from '../../models/user.model';
import { Types } from 'mongoose';

@modelOptions({
  schemaOptions: {
    collection: 'feedback',
    timestamps: true,
  },
})
export class Feedback {
  _id!: Types.ObjectId;

  // Timestamps added by Mongoose
  createdAt?: Date;
  updatedAt?: Date;

  @prop({ required: true, trim: true, type: () => String })
  public message!: string;

  @prop({ required: false, ref: () => User })
  public userId?: Ref<User>;

  @prop({ required: true, trim: true, type: () => String })
  public azureAdObjectId!: string;

  @prop({ required: false, trim: true, type: () => String })
  public page_url?: string;

  @prop({ required: false, trim: true, type: () => String })
  public page_title?: string;
}

export const FeedbackModel = getModelForClass(Feedback);
