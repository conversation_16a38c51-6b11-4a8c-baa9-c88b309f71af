import type { PoolConfig } from "pg";
import config from "../../config/config";

const dbConfig = config.database.postgres;

export const poolConfig: PoolConfig = {
	user: dbConfig.username,
	password: dbConfig.password,
	host: dbConfig.host,
	port: Number(dbConfig.port),
	database: dbConfig.database,
	max: dbConfig.max,
	idleTimeoutMillis: dbConfig.idleTimeoutMillis,
	connectionTimeoutMillis: dbConfig.connectionTimeoutMillis,
	ssl: {
		rejectUnauthorized: false,
	},
};
