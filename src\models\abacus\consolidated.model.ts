// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { prop, getModelForClass, modelOptions } from '@typegoose/typegoose';

@modelOptions({
  schemaOptions: {
    timestamps: true,
    collection: 'consolidated',
  },
  options: {
    allowMixed: 0,
  },
})
export class Consolidated {
  @prop({ required: true })
  public project_code!: string;

  @prop({ required: false, default: null })
  public code!: string | null;

  @prop({ required: false, default: null })
  public description!: string | null;

  @prop({ required: false, default: null })
  public grouping!: string | null;

  @prop({ required: false, default: null })
  public quantity!: string | null;

  @prop({ required: false, default: null })
  public uom!: string | null;

  @prop({ required: false, default: null })
  public rate!: string | null;

  @prop({ required: false, default: null })
  public subtotal!: string | null;

  @prop({ required: false, default: null })
  public factor!: string | null;
}

const ConsolidatedModel = getModelForClass(Consolidated);

export default ConsolidatedModel;
