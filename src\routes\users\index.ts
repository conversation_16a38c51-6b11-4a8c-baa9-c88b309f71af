import {
	type Request,
	type Response,
	Router,
	type NextFunction,
} from "express";
import type { ParamsDictionary } from "express-serve-static-core";
import { UserController } from "../../controllers/users/user.controller";
import { UserService } from "@/services/user/user.service";
import { RoleMiddleware } from "../../middlewares/checkUserRole";
import type {
	CreateUserInput,
	UserResponse,
	ResponseMessage,
	PaginatedUsersResponse,
	UpdateUserInput,
} from "../../controllers/users/users.schema";

const router = Router();
const userService = new UserService();
const userController = new UserController(userService);
const roleMiddleware = RoleMiddleware.getInstance(userService);

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       required:
 *         - azureAdObjectId
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the user
 *           example: "507f1f77bcf86cd799439011"
 *         azureAdObjectId:
 *           type: string
 *           description: Azure AD Object ID - unique email identifier from Azure Active Directory
 *           example: "<EMAIL>"
 *         email:
 *           type: string
 *           format: email
 *           description: User email address
 *           example: "<EMAIL>"
 *         name:
 *           type: string
 *           description: User's full name
 *           example: "John Doe"
 *         roles:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               _id:
 *                 type: string
 *                 description: Role ID (MongoDB ObjectId)
 *                 example: "507f1f77bcf86cd799439013"
 *               name:
 *                 type: string
 *                 description: Role name
 *                 example: "SYSTEM_ADMIN"
 *               description:
 *                 type: string
 *                 description: Role description
 *                 example: "Full system administration privileges"
 *               permissions:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of permission IDs
 *                 example: ["507f1f77bcf86cd799439019", "507f1f77bcf86cd79943901a"]
 *           description: Array of populated role objects assigned to the user
 *         assignedBy:
 *           type: string
 *           description: ID of the user who assigned the roles
 *           example: "507f1f77bcf86cd799439012"
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the user was created
 *           example: "2024-01-15T10:30:00Z"
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the user was last updated
 *           example: "2024-01-15T10:30:00Z"
 *
 *     PaginatedUsersResponse:
 *       type: object
 *       properties:
 *         users:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/User'
 *         pagination:
 *           type: object
 *           properties:
 *             currentPage:
 *               type: integer
 *               description: Current page number
 *               example: 1
 *             totalPages:
 *               type: integer
 *               description: Total number of pages
 *               example: 5
 *             totalCount:
 *               type: integer
 *               description: Total number of users
 *               example: 47
 *             limit:
 *               type: integer
 *               description: Number of users per page
 *               example: 10
 *             hasNextPage:
 *               type: boolean
 *               description: Whether there is a next page
 *               example: true
 *             hasPreviousPage:
 *               type: boolean
 *               description: Whether there is a previous page
 *               example: false
 *
 *     CreateUserRequest:
 *       type: object
 *       required:
 *         - azureAdObjectId
 *       properties:
 *         azureAdObjectId:
 *           type: string
 *           minLength: 1
 *           description: Azure AD Object ID - user's email address from Azure Active Directory
 *           example: "<EMAIL>"
 *         roles:
 *           type: array
 *           items:
 *             type: string
 *           description: Initial role ObjectIds (MongoDB ObjectIds) to assign to the user
 *           example: ["6876f7054d0183ec33b0f40e"]
 *
 *     Role:
 *       type: object
 *       required:
 *         - name
 *         - description
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the role
 *           example: "507f1f77bcf86cd799439013"
 *         name:
 *           type: string
 *           description: Unique name of the role
 *           example: "SYSTEM_ADMIN"
 *         description:
 *           type: string
 *           description: Detailed description of what this role permits
 *           example: "Full system administration privileges"
 *         permissions:
 *           type: array
 *           items:
 *             type: string
 *           description: List of permission IDs (MongoDB ObjectIds) associated with this role
 *           example: ["507f1f77bcf86cd799439019", "507f1f77bcf86cd79943901a", "507f1f77bcf86cd79943901b"]
 *         createdAt:
 *           type: string
 *           format: date-time
 *           example: "2024-01-15T10:30:00Z"
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           example: "2024-01-15T10:30:00Z"
 *
 *     RoleWithPermissions:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the role
 *           example: "507f1f77bcf86cd799439013"
 *         name:
 *           type: string
 *           description: Unique name of the role
 *           example: "ADMIN"
 *         description:
 *           type: string
 *           description: Detailed description of what this role permits
 *           example: "Grant admin access to Einstein"
 *         permissions:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 description: The auto-generated id of the permission
 *                 example: "507f1f77bcf86cd799439019"
 *               name:
 *                 type: string
 *                 description: Unique name of the permission
 *                 example: "manage-users"
 *               description:
 *                 type: string
 *                 description: Detailed description of what this permission allows
 *                 example: "Can create, update, and delete users"
 *
 *     Permission:
 *       type: object
 *       required:
 *         - name
 *         - description
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the permission
 *           example: "507f1f77bcf86cd799439014"
 *         name:
 *           type: string
 *           description: Unique name of the permission
 *           example: "create-project"
 *         description:
 *           type: string
 *           description: Detailed description of what this permission allows
 *           example: "Allows creating new projects in the system"
 *         createdAt:
 *           type: string
 *           format: date-time
 *           example: "2024-01-15T10:30:00Z"
 *
 *     ErrorResponse:
 *       type: object
 *       properties:
 *         message:
 *           type: string
 *           description: Error message describing what went wrong
 *           example: "User not found"
 *         code:
 *           type: string
 *           description: Error code for programmatic error handling
 *           example: "USER_NOT_FOUND"
 *         statusCode:
 *           type: integer
 *           description: HTTP status code
 *           example: 404
 *
 *     ValidationError:
 *       type: object
 *       properties:
 *         message:
 *           type: string
 *           description: Validation error message
 *           example: "Validation failed"
 *         errors:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               field:
 *                 type: string
 *                 example: "azureAdObjectId"
 *               message:
 *                 type: string
 *                 example: "Azure AD Object ID is required"
 *
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *       description: "JWT token for authentication. Format: Bearer <token>"
 *
 * tags:
 *   - name: Users
 *     description: User management operations including roles and permissions
 */

/**
 * @swagger
 * /api/einstein/users:
 *   get:
 *     summary: Get all users with pagination
 *     description: |
 *       Retrieves a paginated list of users in the system. This endpoint returns user information
 *       including their assigned roles with pagination, sorting, and search capabilities.
 *       The response includes pagination metadata and supports searching across multiple fields.
 *
 *       **Required permissions**: `read-users` or `SYSTEM_ADMIN` role
 *
 *       **Features**:
 *       - Pagination with configurable page size
 *       - Sorting by multiple fields (createdAt, updatedAt, azureAdObjectId)
 *       - Search by azureAdObjectId field
 *       - Filter by role name
 *       - Comprehensive pagination metadata
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number to retrieve
 *         example: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of users per page (max 100)
 *         example: 10
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [createdAt, updatedAt, azureAdObjectId]
 *           default: createdAt
 *         description: Field to sort by
 *         example: createdAt
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort order (ascending or descending)
 *         example: desc
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term to filter users by azureAdObjectId
 *         example: "<EMAIL>"
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *         description: Filter users by role name (exact match)
 *         example: "SYSTEM_ADMIN"
 *     responses:
 *       200:
 *         description: Successfully retrieved paginated list of users
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PaginatedUsersResponse'
 *             example:
 *               users:
 *                 - id: "507f1f77bcf86cd799439011"
 *                   azureAdObjectId: "<EMAIL>"
 *                   roles: 
 *                     - _id: "507f1f77bcf86cd799439013"
 *                       name: "SYSTEM_ADMIN"
 *                       description: "Full system administration privileges"
 *                       permissions: ["507f1f77bcf86cd799439019", "507f1f77bcf86cd79943901a"]
 *                     - _id: "507f1f77bcf86cd799439016"
 *                       name: "USER"
 *                       description: "Basic user access"
 *                       permissions: ["507f1f77bcf86cd799439020"]
 *                   assignedBy: "<EMAIL>"
 *                   createdAt: "2024-01-15T10:30:00Z"
 *                   updatedAt: "2024-01-15T10:30:00Z"
 *                 - id: "507f1f77bcf86cd799439015"
 *                   azureAdObjectId: "<EMAIL>"
 *                   roles:
 *                     - _id: "507f1f77bcf86cd799439017"
 *                       name: "Data Reader"
 *                       description: "Read the data of a user"
 *                       permissions: ["507f1f77bcf86cd79943901e"]
 *                   assignedBy: "<EMAIL>"
 *                   createdAt: "2024-01-16T10:30:00Z"
 *                   updatedAt: "2024-01-16T10:30:00Z"
 *               pagination:
 *                 currentPage: 1
 *                 totalPages: 5
 *                 totalCount: 47
 *                 limit: 10
 *                 hasNextPage: true
 *                 hasPreviousPage: false
 *       400:
 *         description: Bad request - Invalid pagination parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationError'
 *             example:
 *               message: "Validation failed"
 *               errors:
 *                 - field: "page"
 *                   message: "Page must be a positive integer"
 *                 - field: "limit"
 *                   message: "Limit must be between 1 and 100"
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               message: "Invalid authentication token"
 *               code: "UNAUTHORIZED"
 *               statusCode: 401
 *       403:
 *         description: Forbidden - User lacks required permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get(
	"/",
	roleMiddleware.checkRole(["read-users", "SYSTEM_ADMIN"]),
	(
		req: Request<ParamsDictionary>,
		res: Response<PaginatedUsersResponse>,
		next: NextFunction,
	) => {
		userController.getUsers(req, res, next);
	},
);

/**
 * @swagger
 * /api/einstein/users:
 *   post:
 *     summary: Create a new user
 *     description: |
 *       Creates a new user in the system. This endpoint requires a valid Azure AD Object ID
 *       to integrate with Azure Active Directory. Optionally, initial roles can be assigned
 *       during user creation using role ObjectIds.
 *
 *       **Required permissions**: `create-users` or `SYSTEM_ADMIN` role
 *
 *       **Security**: The `assignedBy` field is automatically set from the authenticated user's token.
 *       Do not include `assignedBy` in the request body as it will be ignored for security reasons.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateUserRequest'
 *           examples:
 *             basicUser:
 *               summary: Create user without initial roles
 *               value:
 *                 azureAdObjectId: "<EMAIL>"
 *             userWithRoles:
 *               summary: Create user with initial roles
 *               value:
 *                 azureAdObjectId: "<EMAIL>"
 *                 roles: ["6876f7054d0183ec33b0f40e"]
 *     responses:
 *       201:
 *         description: User created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/User'
 *             example:
 *               id: "507f1f77bcf86cd799439011"
 *               azureAdObjectId: "<EMAIL>"
 *               roles: ["6876f7054d0183ec33b0f40e"]
 *               assignedBy: "<EMAIL>"
 *               createdAt: "2024-01-15T10:30:00Z"
 *               updatedAt: "2024-01-15T10:30:00Z"
 *       400:
 *         description: Bad request - Invalid input data or user already exists
 *         content:
 *           application/json:
 *             schema:
 *               oneOf:
 *                 - $ref: '#/components/schemas/ErrorResponse'
 *                 - $ref: '#/components/schemas/ValidationError'
 *             examples:
 *               userExists:
 *                 summary: User already exists
 *                 value:
 *                   message: "User with this Azure AD Object ID already exists"
 *                   code: "USER_EXISTS"
 *                   statusCode: 400
 *               validationError:
 *                 summary: Validation error
 *                 value:
 *                   message: "Validation failed"
 *                   errors:
 *                     - field: "azureAdObjectId"
 *                       message: "Azure AD Object ID is required"
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Forbidden - User lacks required permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post(
	"/",
	roleMiddleware.checkRole(["create-users", "SYSTEM_ADMIN"]),
	(
		req: Request<ParamsDictionary, UserResponse, CreateUserInput>,
		res: Response<UserResponse | ResponseMessage>,
		next: NextFunction,
	) => {
		userController.createUser(req, res, next);
	},
);

/**
 * @swagger
 * /api/einstein/users/roles:
 *   get:
 *     summary: Get user's roles
 *     description: |
 *       Retrieves all roles assigned to the authenticated user. The user is identified
 *       by the Azure AD Object ID passed in the `x-azure-ad-object-id` header.
 *
 *       **Note**: This endpoint returns roles for the currently authenticated user only.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: header
 *         name: x-azure-ad-object-id
 *         required: true
 *         schema:
 *           type: string
 *         description: Azure AD Object ID of the user (email address)
 *         example: "00000000-0000-0000-0000-000000000000"
 *     responses:
 *       200:
 *         description: Successfully retrieved user roles
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Role'
 *             example:
 *               - id: "507f1f77bcf86cd799439013"
 *                 name: "SYSTEM_ADMIN"
 *                 description: "Full system administration privileges"
 *                 permissions: ["507f1f77bcf86cd799439019", "507f1f77bcf86cd79943901a", "507f1f77bcf86cd79943901b"]
 *                 createdAt: "2024-01-15T10:30:00Z"
 *                 updatedAt: "2024-01-15T10:30:00Z"
 *               - id: "507f1f77bcf86cd799439016"
 *                 name: "project-manager"
 *                 description: "Can manage projects and team members"
 *                 permissions: ["507f1f77bcf86cd799439019", "507f1f77bcf86cd79943901c", "507f1f77bcf86cd79943901d"]
 *                 createdAt: "2024-01-15T10:30:00Z"
 *                 updatedAt: "2024-01-15T10:30:00Z"
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get("/roles", (req: Request, res: Response, next: NextFunction) => {
	userController.getRoles(req, res, next);
});

/**
 * @swagger
 * /api/einstein/users/roles:
 *   post:
 *     summary: Create a new role
 *     description: |
 *       Creates a new role in the system. Roles are used to group permissions
 *       and can be assigned to users to control their access to system features.
 *
 *       **Required permissions**: `manage-roles` or `SYSTEM_ADMIN` role
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - description
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 1
 *                 description: Unique name for the role (lowercase, no spaces)
 *                 pattern: "^[a-z-]+$"
 *                 example: "project-manager"
 *               description:
 *                 type: string
 *                 minLength: 1
 *                 description: Detailed description of the role's purpose
 *                 example: "Can manage projects and team members"
 *           example:
 *             name: "project-manager"
 *             description: "Can manage projects and team members"
 *     responses:
 *       201:
 *         description: Role created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Role'
 *             example:
 *               id: "507f1f77bcf86cd799439013"
 *               name: "project-manager"
 *               description: "Can manage projects and team members"
 *               permissions: []
 *               createdAt: "2024-01-15T10:30:00Z"
 *               updatedAt: "2024-01-15T10:30:00Z"
 *       400:
 *         description: Bad request - Invalid input or role already exists
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             examples:
 *               roleExists:
 *                 summary: Role already exists
 *                 value:
 *                   message: "Role with this name already exists"
 *                   code: "ROLE_EXISTS"
 *                   statusCode: 400
 *               invalidName:
 *                 summary: Invalid role name
 *                 value:
 *                   message: "Role name must be lowercase with no spaces"
 *                   code: "INVALID_ROLE_NAME"
 *                   statusCode: 400
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Forbidden - User lacks required permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post(
	"/roles",
	roleMiddleware.checkRole(["manage-roles", "SYSTEM_ADMIN"]),
	(req: Request, res: Response, next: NextFunction) => {
		userController.createRole(req, res, next);
	},
);

/**
 * @swagger
 * /api/einstein/users/roles:
 *   put:
 *     summary: Assign a role to a user
 *     description: |
 *       Assigns an existing role to a user. The role must already exist in the system.
 *       This operation will add the role to the user's existing roles without removing
 *       any previously assigned roles.
 *
 *       **Required permissions**: `manage-users` or `SYSTEM_ADMIN` role
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - azureAdObjectId
 *               - role
 *             properties:
 *               azureAdObjectId:
 *                 type: string
 *                 description: Azure AD Object ID of the user
 *                 example: "00000000-0000-0000-0000-000000000000"
 *               role:
 *                 type: string
 *                 description: ID (MongoDB ObjectId) of the role to assign
 *                 example: "507f1f77bcf86cd799439016"
 *           example:
 *             azureAdObjectId: "<EMAIL>"
 *             role: "507f1f77bcf86cd799439016"
 *     responses:
 *       200:
 *         description: Role assigned successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/User'
 *             example:
 *               id: "507f1f77bcf86cd799439011"
 *               azureAdObjectId: "<EMAIL>"
 *               email: "<EMAIL>"
 *               name: "John Doe"
 *               roles: ["507f1f77bcf86cd799439017", "507f1f77bcf86cd799439016"]
 *               assignedBy: "507f1f77bcf86cd799439012"
 *               createdAt: "2024-01-15T10:30:00Z"
 *               updatedAt: "2024-01-16T10:30:00Z"
 *       400:
 *         description: Bad request - Invalid input data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             examples:
 *               roleNotFound:
 *                 summary: Role not found
 *                 value:
 *                   message: "Role not found"
 *                   code: "ROLE_NOT_FOUND"
 *                   statusCode: 400
 *               userNotFound:
 *                 summary: User not found
 *                 value:
 *                   message: "User not found"
 *                   code: "USER_NOT_FOUND"
 *                   statusCode: 400
 *               alreadyAssigned:
 *                 summary: Role already assigned
 *                 value:
 *                   message: "User already has this role"
 *                   code: "ROLE_ALREADY_ASSIGNED"
 *                   statusCode: 400
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Forbidden - User lacks required permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.put(
	"/roles",
	roleMiddleware.checkRole(["manage-users", "SYSTEM_ADMIN"]),
	(req: Request, res: Response, next: NextFunction) => {
		userController.assignRole(req, res, next);
	},
);

/**
 * @swagger
 * /api/einstein/users/roles/all:
 *   get:
 *     summary: Get all available roles
 *     description: |
 *       Retrieves a list of all roles available in the system. This endpoint returns
 *       comprehensive information about each role including its permissions.
 *
 *       **Required permissions**: `read-roles` or `SYSTEM_ADMIN` role
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Successfully retrieved all roles
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/RoleWithPermissions'
 *             example:
 *               - id: "507f1f77bcf86cd799439013"
 *                 name: "SYSTEM_ADMIN"
 *                 description: "Grant admin access to Einstein"
 *                 permissions:
 *                   - id: "507f1f77bcf86cd799439019"
 *                     name: "manage-users"
 *                     description: "Can create, update, and delete users"
 *                   - id: "507f1f77bcf86cd79943901a"
 *                     name: "manage-projects"
 *                     description: "Can create, update, and delete projects"
 *                   - id: "507f1f77bcf86cd79943901b"
 *                     name: "view-analytics"
 *                     description: "Can view system analytics and reports"
 *               - id: "507f1f77bcf86cd799439016"
 *                 name: "USER"
 *                 description: "Grant basic user access to Einstein"
 *                 permissions:
 *                   - id: "507f1f77bcf86cd799439019"
 *                     name: "view-projects"
 *                     description: "Can view project details"
 *               - id: "507f1f77bcf86cd799439017"
 *                 name: "Data Reader"
 *                 description: "Read the data of a user"
 *                 permissions:
 *                   - id: "507f1f77bcf86cd79943901e"
 *                     name: "read-data"
 *                     description: "Can read user data"
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Forbidden - User lacks required permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get(
	"/roles/all",
	roleMiddleware.checkRole(["read-roles", "SYSTEM_ADMIN"]),
	(req: Request, res: Response, next: NextFunction) => {
		userController.getAllRoles(req, res, next);
	},
);

/**
 * @swagger
 * /api/einstein/users/permissions:
 *   post:
 *     summary: Create a new permission
 *     description: |
 *       Creates a new permission in the system. Permissions define specific actions
 *       that can be performed and are grouped together in roles.
 *
 *       **Required permissions**: `manage-permissions` or `SYSTEM_ADMIN` role
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - description
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 1
 *                 description: Unique name for the permission (lowercase, hyphen-separated)
 *                 pattern: "^[a-z-]+$"
 *                 example: "create-project"
 *               description:
 *                 type: string
 *                 minLength: 1
 *                 description: Detailed description of what this permission allows
 *                 example: "Allows creating new projects in the system"
 *           example:
 *             name: "create-project"
 *             description: "Allows creating new projects in the system"
 *     responses:
 *       201:
 *         description: Permission created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Permission'
 *             example:
 *               id: "507f1f77bcf86cd799439014"
 *               name: "create-project"
 *               description: "Allows creating new projects in the system"
 *               createdAt: "2024-01-15T10:30:00Z"
 *       400:
 *         description: Bad request - Invalid input or permission already exists
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             examples:
 *               permissionExists:
 *                 summary: Permission already exists
 *                 value:
 *                   message: "Permission with this name already exists"
 *                   code: "PERMISSION_EXISTS"
 *                   statusCode: 400
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Forbidden - User lacks required permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post(
	"/permissions",
	roleMiddleware.checkRole(["manage-permissions", "SYSTEM_ADMIN"]),
	(req: Request, res: Response, next: NextFunction) => {
		userController.createPermission(req, res, next);
	},
);

/**
 * @swagger
 * /api/einstein/users/permissions:
 *   put:
 *     summary: Assign a permission to a role
 *     description: |
 *       Assigns an existing permission to a role. Both the role and permission
 *       must already exist in the system. This allows building up roles with
 *       specific sets of permissions.
 *
 *       **Required permissions**: `manage-roles` or `SYSTEM_ADMIN` role
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - role
 *               - permission
 *             properties:
 *               role:
 *                 type: string
 *                 description: ID (MongoDB ObjectId) of the role
 *                 example: "507f1f77bcf86cd799439016"
 *               permission:
 *                 type: string
 *                 description: ID (MongoDB ObjectId) of the permission to assign
 *                 example: "507f1f77bcf86cd799439019"
 *           example:
 *             role: "507f1f77bcf86cd799439016"
 *             permission: "507f1f77bcf86cd799439019"
 *     responses:
 *       200:
 *         description: Permission assigned to role successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Role'
 *             example:
 *               id: "507f1f77bcf86cd799439013"
 *               name: "project-manager"
 *               description: "Can manage projects and team members"
 *               permissions: ["507f1f77bcf86cd799439019", "507f1f77bcf86cd79943901c", "507f1f77bcf86cd79943901d"]
 *               createdAt: "2024-01-15T10:30:00Z"
 *               updatedAt: "2024-01-16T10:30:00Z"
 *       400:
 *         description: Bad request - Invalid input data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             examples:
 *               roleNotFound:
 *                 summary: Role not found
 *                 value:
 *                   message: "Role not found"
 *                   code: "ROLE_NOT_FOUND"
 *                   statusCode: 400
 *               permissionNotFound:
 *                 summary: Permission not found
 *                 value:
 *                   message: "Permission not found"
 *                   code: "PERMISSION_NOT_FOUND"
 *                   statusCode: 400
 *               alreadyAssigned:
 *                 summary: Permission already assigned
 *                 value:
 *                   message: "Role already has this permission"
 *                   code: "PERMISSION_ALREADY_ASSIGNED"
 *                   statusCode: 400
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Forbidden - User lacks required permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.put(
	"/permissions",
	roleMiddleware.checkRole(["manage-roles", "SYSTEM_ADMIN"]),
	(req: Request, res: Response, next: NextFunction) => {
		userController.assignPermission(req, res, next);
	},
);

/**
 * @swagger
 * /api/einstein/users/{azureAdObjectId}:
 *   put:
 *     summary: Update a user's roles
 *     description: |
 *       Updates a user's roles by replacing all existing roles with the provided list.
 *       This endpoint accepts role ObjectIds and validates that all roles exist
 *       before updating the user.
 *
 *       **Required permissions**: `manage-users` or `SYSTEM_ADMIN` role
 *
 *       **Features**:
 *       - Replace all user roles with provided list
 *       - Accept role ObjectIds for reliable identification
 *       - Validate all role ObjectIds exist before updating
 *       - Optional assignedBy tracking for audit purposes
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: azureAdObjectId
 *         required: true
 *         schema:
 *           type: string
 *           format: email
 *         description: The Azure AD Object ID of the user to update
 *         example: "<EMAIL>"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - roles
 *             properties:
 *               roles:
 *                 type: array
 *                 items:
 *                   type: string
 *                 minItems: 1
 *                 description: Array of role ObjectIds to assign to the user (replaces all existing roles)
 *                 example: ["6876f7054d0183ec33b0f411", "507f1f77bcf86cd799439016"]
 *               assignedBy:
 *                 type: string
 *                 description: Azure AD Object ID of the user making the assignment (optional)
 *                 example: "<EMAIL>"
 *           examples:
 *             systemAdmin:
 *               summary: Assign system admin role
 *               value:
 *                 roles: ["6876f7054d0183ec33b0f411"]
 *             multipleRoles:
 *               summary: Assign multiple roles
 *               value:
 *                 roles: ["6876f7054d0183ec33b0f411", "507f1f77bcf86cd799439016"]
 *                 assignedBy: "<EMAIL>"
 *             basicUser:
 *               summary: Assign basic user role
 *               value:
 *                 roles: ["507f1f77bcf86cd799439017"]
 *     responses:
 *       200:
 *         description: User roles updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/User'
 *             example:
 *               azureAdObjectId: "<EMAIL>"
 *               roles: ["507f1f77bcf86cd799439013", "507f1f77bcf86cd799439016"]
 *               assignedBy: "<EMAIL>"
 *       400:
 *         description: Bad request - Invalid input data or role names don't exist
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             examples:
 *               invalidRoles:
 *                 summary: Invalid role ObjectIds
 *                 value:
 *                   message: "The following role IDs do not exist: 507f1f77bcf86cd799999999, 507f1f77bcf86cd799888888"
 *               missingRoles:
 *                 summary: No roles provided
 *                 value:
 *                   message: "At least one role is required"
 *               validationError:
 *                 summary: Validation error
 *                 value:
 *                   message: "Validation failed"
 *                   errors:
 *                     - field: "roles"
 *                       message: "At least one role is required"
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Forbidden - User lacks required permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               message: "User not found"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *   get:
 *     summary: Get a user by Azure AD Object ID
 *     description: |
 *       Retrieves detailed information about a specific user identified by their
 *       Azure AD Object ID. This includes all user properties, assigned roles,
 *       and metadata.
 *
 *       **Required permissions**: `read-users` or `SYSTEM_ADMIN` role
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: azureAdObjectId
 *         required: true
 *         schema:
 *           type: string
 *           format: email
 *         description: The Azure AD Object ID of the user (email address from Azure AD)
 *         example: "00000000-0000-0000-0000-000000000000"
 *     responses:
 *       200:
 *         description: User found and returned successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/User'
 *             example:
 *               id: "507f1f77bcf86cd799439011"
 *               azureAdObjectId: "<EMAIL>"
 *               email: "<EMAIL>"
 *               name: "John Doe"
 *               roles: ["507f1f77bcf86cd799439013", "507f1f77bcf86cd799439016"]
 *               assignedBy: "507f1f77bcf86cd799439012"
 *               createdAt: "2024-01-15T10:30:00Z"
 *               updatedAt: "2024-01-15T10:30:00Z"
 *       400:
 *         description: Bad request - Invalid Azure AD Object ID format
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               message: "Invalid Azure AD Object ID format"
 *               code: "INVALID_OBJECT_ID"
 *               statusCode: 400
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Forbidden - User lacks required permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               message: "User not found"
 *               code: "USER_NOT_FOUND"
 *               statusCode: 404
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.put(
	"/:azureAdObjectId",
	roleMiddleware.checkRole(["manage-users", "SYSTEM_ADMIN"]),
	(
		req: Request<ParamsDictionary, UserResponse, UpdateUserInput>,
		res: Response<UserResponse | ResponseMessage>,
		next: NextFunction,
	) => {
		userController.updateUser(req, res, next);
	},
);

/**
 * @swagger
 * /api/einstein/users/{azureAdObjectId}:
 *   delete:
 *     summary: Soft delete a user
 *     description: |
 *       Performs a soft delete on a user, marking them as deleted while preserving
 *       the record for audit purposes. The user will no longer appear in normal
 *       queries but the data is retained in the database.
 *
 *       **Required permissions**: `manage-users` or `SYSTEM_ADMIN` role
 *
 *       **Security**: The `deletedBy` field is automatically set from the authenticated user's token.
 *       
 *       **Soft Delete Features**:
 *       - User marked as deleted with timestamp
 *       - Audit trail maintained (who deleted, when)
 *       - User excluded from all normal operations
 *       - Data preserved for compliance/audit purposes
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: azureAdObjectId
 *         required: true
 *         schema:
 *           type: string
 *           format: email
 *         description: The Azure AD Object ID of the user to delete
 *         example: "<EMAIL>"
 *     responses:
 *       200:
 *         description: User soft deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Success message
 *                   example: "User deleted successfully"
 *             example:
 *               message: "User deleted successfully"
 *       400:
 *         description: Bad request - Invalid Azure AD Object ID or deleting user not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             examples:
 *               missingObjectId:
 *                 summary: Missing Azure AD Object ID
 *                 value:
 *                   message: "Azure AD Object ID is required"
 *               deletingUserNotFound:
 *                 summary: Deleting user not found
 *                 value:
 *                   message: "Deleting user not found in system"
 *       401:
 *         description: Unauthorized - Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               message: "Authentication required"
 *       403:
 *         description: Forbidden - User lacks required permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: User not found or already deleted
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               message: "User not found"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.delete(
	"/:azureAdObjectId",
	roleMiddleware.checkRole(["manage-users", "SYSTEM_ADMIN"]),
	(
		req: Request<ParamsDictionary>,
		res: Response<{ message: string } | ResponseMessage>,
		next: NextFunction,
	) => {
		userController.deleteUser(req, res, next);
	},
);

router.get(
	"/:azureAdObjectId",
	roleMiddleware.checkRole(["read-users", "SYSTEM_ADMIN"]),
	(
		req: Request<ParamsDictionary>,
		res: Response<UserResponse>,
		next: NextFunction,
	) => {
		userController.getUser(req, res, next);
	},
);

export default router;
