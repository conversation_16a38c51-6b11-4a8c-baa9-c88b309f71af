import config from "@/config/config";
import swaggerJsdoc from "swagger-jsdoc";

const swaggerOptions: swaggerJsdoc.Options = {
	definition: {
		openapi: "3.0.0",
		info: {
			title: "Einstein Backend API",
			version: "1.0.0",
			description: "API documentation for Einstein Backend services",
			contact: {
				name: "API Support",
				email: "<EMAIL>",
			},
		},
		servers: [
			{
				url: `http://localhost:${config.port}`,
				description: "Development server",
			},
			{
				url: "https://api.einstein.com",
				description: "Production server",
			},
		],
		components: {
			securitySchemes: {
				bearerAuth: {
					type: "http",
					scheme: "bearer",
					bearerFormat: "JWT",
				},
			},
		},
		security: [
			{
				bearerAuth: [],
			},
		],
	},
	apis: [
		"./src/routes/**/*.ts",
		"./src/controllers/**/*.ts",
		"./src/models/**/*.ts",
	],
};

export const swaggerSpec = swaggerJsdoc(swaggerOptions);
