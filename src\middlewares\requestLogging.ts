import logger from "@/utils/logging";
import type { NextFunction, Request, Response } from "express";
import { v4 as uuidv4 } from "uuid";

// Extend Express Request to include correlation ID
declare global {
	namespace Express {
		interface Request {
			correlationId?: string;
			startTime?: number;
		}
	}
}

/**
 * Middleware to add correlation ID to requests
 */
export const correlationIdMiddleware = (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	// Generate or extract correlation ID
	const correlationId = (req.headers["x-correlation-id"] as string) || uuidv4();
	req.correlationId = correlationId;

	// Add correlation ID to response headers
	res.setHeader("X-Correlation-Id", correlationId);

	next();
};

/**
 * HTTP request logging middleware
 */
export const requestLoggingMiddleware = (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	// Record start time
	req.startTime = Date.now();

	// Log incoming request
	logger.info("Incoming request", {
		correlationId: req.correlationId,
		method: req.method,
		url: req.url,
		path: req.path,
		query: req.query,
		headers: {
			"user-agent": req.headers["user-agent"],
			"content-type": req.headers["content-type"],
			"content-length": req.headers["content-length"],
		},
		ip: req.ip,
	});

	// Store original end function reference
	const originalEnd = res.end.bind(res);

	// Override the end function to log response
	res.end = ((...args: any[]) => {
		// Calculate response time
		const responseTime = req.startTime ? Date.now() - req.startTime : 0;

		// Log response
		logger.info("Outgoing response", {
			correlationId: req.correlationId,
			method: req.method,
			url: req.url,
			statusCode: res.statusCode,
			statusMessage: res.statusMessage,
			responseTime: `${responseTime}ms`,
			contentLength: res.get("content-length"),
		});

		// Call the original end function with whatever arguments were passed
		// @ts-ignore - TypeScript can't properly infer the overloaded signatures
		return originalEnd(...args);
	}) as any;

	next();
};

/**
 * Middleware to log slow requests
 */
export const slowRequestLoggingMiddleware = (threshold = 1000) => {
	return (req: Request, res: Response, next: NextFunction) => {
		// Store original end function reference
		const originalEnd = res.end.bind(res);

		res.end = ((...args: any[]) => {
			const responseTime = req.startTime ? Date.now() - req.startTime : 0;

			if (responseTime > threshold) {
				logger.warn("Slow request detected", {
					correlationId: req.correlationId,
					method: req.method,
					url: req.url,
					responseTime: `${responseTime}ms`,
					threshold: `${threshold}ms`,
				});
			}

			// Call the original end function with whatever arguments were passed
			// @ts-ignore - TypeScript can't properly infer the overloaded signatures
			return originalEnd(...args);
		}) as any;

		next();
	};
};
