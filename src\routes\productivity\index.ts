import { Router } from "express";
import { ProductivityRepository } from "@/services/repositories/productivity.repository";
import { ProductivityService } from "@/services/productivity/productivity.service";
import { ProductivityController } from "@/controllers/productivity/productivity.controller";
import notesRoutes from "@/services/notes/notes.routes";

const router = Router();
const productivityRepository = new ProductivityRepository();
const productivityService = new ProductivityService(productivityRepository);
const productivityController = new ProductivityController(productivityService);

// GET /api/productivity?phase=...&stage=...
router.get("/", (req, res, next) =>
	productivityController.getByPhaseAndStage(req, res, next),
);

// GET /api/productivity/phases
router.get("/phases", (req, res, next) =>
	productivityController.getPhases(req, res, next),
);

// POST /api/productivity/stages - get stages by phase
router.post("/stages", (req, res, next) =>
	productivityController.getStagesByPhase(req, res, next),
);

// POST /api/productivity/category-types - get category types by phase and stage
router.post("/category-types", (req, res, next) =>
	productivityController.getCategoryTypesByPhaseAndStage(req, res, next),
);

// POST /api/productivity/data - get data by phase, stage, and category type
router.post("/data", (req, res, next) =>
	productivityController.getDataByPhaseStageAndCategoryType(req, res, next),
);

router.use("/notes", notesRoutes);

export default router;
