import { Router } from "express";
import { AccumulatedDurationController } from "./accumulatedDuration.controller";
import { AccumulatedDurationService } from "./accumulatedDuration.service";
import sectorRoutes from "./sector.routes";

const router = Router();

// Initialize service and controller
const accumulatedDurationService = new AccumulatedDurationService();
const accumulatedDurationController = new AccumulatedDurationController(
	accumulatedDurationService,
);

// Get all available filters for accumulated duration data
router.get("/filters", async (req, res, next) => {
	await accumulatedDurationController.getFilters(req, res, next);
});

// Search accumulated duration data with filters
router.post("/search", async (req, res, next) => {
	await accumulatedDurationController.searchProjects(req, res, next);
});

// Get benchmark data for Gantt chart visualization
router.post("/benchmark/gantt", async (req, res, next) => {
	await accumulatedDurationController.getBenchmarkGanttData(req, res, next);
});

// Get benchmark data for scatterplot visualization
router.post("/benchmark/scatterplot", async (req, res, next) => {
	await accumulatedDurationController.getBenchmarkScatterplotData(
		req,
		res,
		next,
	);
});

// Get projects by type
router.get("/projects", async (req, res, next) => {
	await accumulatedDurationController.getProjectsByType(req, res, next);
});

// Mount sector routes
router.use("/sectors", sectorRoutes);

export default router;
