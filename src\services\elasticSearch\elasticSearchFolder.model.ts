import {
	getModelForClass,
	modelOptions,
	prop,
	Severity,
} from "@typegoose/typegoose";

@modelOptions({
	schemaOptions: {
		timestamps: true,
		collection: "folder_paths",
	},
	options: {
		allowMixed: Severity.ALLOW,
	},
})
export class ElasticSearchFolder {
	@prop({ required: true, type: () => String })
	public es_index!: string;

	@prop({ required: true, type: () => String })
	public file_path!: string;

	@prop({ required: true, type: () => Number })
	public path_depth!: number;

	@prop({ required: true, type: () => Date })
	public imported_at!: Date;
}

export const ElasticSearchFolderModel = getModelForClass(ElasticSearchFolder);
