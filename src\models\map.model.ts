import {
	Severity,
	getModelForClass,
	index,
	modelOptions,
	prop,
} from "@typegoose/typegoose";

@modelOptions({
	schemaOptions: {
		collection: "map_locations",
		timestamps: true,
	},
	options: {
		allowMixed: Severity.ALLOW,
	},
})
@index({ location: "2dsphere" })
class MapLocation {
	@prop({ required: true, type: () => String })
	public projectId!: string;

	@prop({ required: true, type: () => String })
	public company!: string;

	@prop({ required: true, type: () => String })
	public projectName!: string;

	@prop({ required: true, type: () => String })
	public customerName!: string;

	@prop({ required: true, type: () => String })
	public address!: string;

	@prop({ required: true, type: () => Number })
	public latitude!: number;

	@prop({ required: true, type: () => Number })
	public longitude!: number;

	@prop({ required: true, type: () => String })
	public formattedAddress!: string;

	@prop({ required: true, type: () => String })
	public dataSource!: string;

	@prop({ required: true, type: () => Boolean })
	public geocodingSuccess!: boolean;

	@prop({ type: () => Date })
	public uploadedAt?: Date;

	@prop({
		type: () => Object,
		required: true,
	})
	public location!: {
		type: string;
		coordinates: number[];
	};
}

const MapLocationModel = getModelForClass(MapLocation);

export { MapLocation, MapLocationModel };
