// import { ProjectController } from '../../controllers/project.controller';
// import chartsRoutes from '../analysis/charts/index';
import { Router } from "express";
import { createProjectsController } from "@/controllers/projects/projects.controller";
import { createMongoProjectService } from "@/services/mongoProject.service";
import type { ProjectRepository } from "@/services/repositories/ProjectRepository";

const router: Router = Router();

// Create the service - this is where you can easily switch implementations
const createProjectService = (): ProjectRepository => {
	return createMongoProjectService();
};

const projectsService = createProjectService();
const projectsController = createProjectsController(projectsService);

router.get("/", projectsController.getProjects);
router.get("/filters", projectsController.getAvailableProjectFilters);
router.get("/search", projectsController.searchItemFilter);
router.get("/:projectId", projectsController.getProjectById);
// Support both POST (for filter body) and GET (for pagination query params)
router.post("/filtered", projectsController.getFilteredProjects);
router.get("/filtered", projectsController.getFilteredProjects);
router.post("/filtered/download", projectsController.downloadFilteredProjects);
router.post(
	"/filtered/market-share",
	projectsController.getMarketSharePercentages,
);

export default router;
