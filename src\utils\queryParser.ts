/**
 * Advanced Query Parser for ElasticSearch
 *
 * Supports:
 * - Boolean operators: AND (+), OR, NOT (and -)
 * - Exact phrases: "exact phrase"
 * - Wildcards: wind*, test?ing
 * - Field queries: title:meeting, content:budget
 * - Grouping: (term1 OR term2) AND term3
 * - Operator precedence: NOT > AND > OR
 */

export interface ParsedQuery {
	type: "bool" | "match" | "phrase" | "wildcard" | "field" | "match_all";
	field?: string;
	value?: string;
	operator?: "and" | "or" | "not";
	left?: ParsedQuery;
	right?: ParsedQuery;
	boost?: number;
}

export interface QueryParseError {
	message: string;
	position?: number;
	token?: string;
}

export class QueryParser {
	private tokens: string[] = [];
	private position = 0;
	private current = "";

	/**
	 * Parse a query string into a structured format
	 */
	parse(query: string): ParsedQuery {
		if (!query?.trim()) {
			return { type: "match_all" };
		}

		try {
			this.tokenize(query.trim());
			this.position = 0;
			this.current = this.tokens[0] || "";

			const result = this.parseOr();

			if (this.position < this.tokens.length) {
				throw new Error(`Unexpected token: ${this.current}`);
			}

			return result;
		} catch (error) {
			throw new Error(
				`Query parsing error: ${error instanceof Error ? error.message : String(error)}`,
			);
		}
	}

	/**
	 * Tokenize the query string
	 */
	private tokenize(query: string): void {
		this.tokens = [];
		let i = 0;

		while (i < query.length) {
			const char = query[i];

			// Skip whitespace
			if (/\s/.test(char)) {
				i++;
				continue;
			}

			// Handle quoted phrases
			if (char === '"') {
				const phrase = this.extractPhrase(query, i);
				this.tokens.push(phrase);
				i += phrase.length;
				continue;
			}

			// Handle parentheses
			if (char === "(" || char === ")") {
				this.tokens.push(char);
				i++;
				continue;
			}

			// Handle minus and plus operators separately
			if (char === "-" || char === "+") {
				this.tokens.push(char);
				i++;
				continue;
			}

			// Handle operators and terms
			const token = this.extractToken(query, i);
			if (token) {
				this.tokens.push(token);
				i += token.length;
			} else {
				i++;
			}
		}
	}

	/**
	 * Extract quoted phrase including quotes
	 */
	private extractPhrase(query: string, start: number): string {
		let i = start + 1; // Skip opening quote
		let phrase = '"';

		while (i < query.length) {
			const char = query[i];
			phrase += char;

			if (char === '"') {
				break;
			}

			// Handle escaped quotes
			if (char === "\\" && i + 1 < query.length) {
				i++;
				phrase += query[i];
			}

			i++;
		}

		return phrase;
	}

	/**
	 * Extract a single token (word, operator, or field:value)
	 */
	private extractToken(query: string, start: number): string {
		let i = start;
		let token = "";

		while (i < query.length) {
			const char = query[i];

			// Stop at whitespace or special characters
			if (/\s/.test(char) || char === "(" || char === ")" || char === '"') {
				break;
			}

			token += char;
			i++;
		}

		return token;
	}

	/**
	 * Parse OR expressions (lowest precedence)
	 */
	private parseOr(): ParsedQuery {
		let left = this.parseAnd();

		while (this.current && this.current.toUpperCase() === "OR") {
			this.advance();
			const right = this.parseAnd();
			left = {
				type: "bool",
				operator: "or",
				left,
				right,
			};
		}

		return left;
	}

	/**
	 * Parse AND expressions (medium precedence)
	 */
	private parseAnd(): ParsedQuery {
		let left = this.parseNot();

		while (
			this.current &&
			(this.current.toUpperCase() === "AND" ||
				this.current === "+" ||
				this.isImplicitAnd())
		) {
			if (this.current.toUpperCase() === "AND" || this.current === "+") {
				this.advance();
			}
			const right = this.parseNot();
			left = {
				type: "bool",
				operator: "and",
				left,
				right,
			};
		}

		return left;
	}

	/**
	 * Parse NOT expressions (highest precedence)
	 */
	private parseNot(): ParsedQuery {
		if (
			this.current &&
			(this.current.toUpperCase() === "NOT" || this.current === "-")
		) {
			this.advance();
			const operand = this.parsePrimary();
			return {
				type: "bool",
				operator: "not",
				right: operand,
			};
		}

		return this.parsePrimary();
	}

	/**
	 * Parse primary expressions (terms, phrases, field queries, groups)
	 */
	private parsePrimary(): ParsedQuery {
		if (!this.current) {
			throw new Error("Unexpected end of query");
		}

		// Handle parentheses
		if (this.current === "(") {
			this.advance();
			const expr = this.parseOr();
			if (String(this.current) !== ")") {
				throw new Error("Expected closing parenthesis");
			}
			this.advance();
			return expr;
		}

		// Handle quoted phrases
		if (this.current.startsWith('"') && this.current.endsWith('"')) {
			const phrase = this.current.slice(1, -1);
			this.advance();
			return {
				type: "phrase",
				value: phrase,
			};
		}

		// Handle field queries (field:value)
		if (this.current.includes(":")) {
			const [field, ...valueParts] = this.current.split(":");
			const value = valueParts.join(":");
			this.advance();

			// Check if it's a wildcard query
			if (value.includes("*") || value.includes("?")) {
				return {
					type: "wildcard",
					field,
					value,
				};
			}

			return {
				type: "field",
				field,
				value,
			};
		}

		// Handle wildcard queries
		if (this.current.includes("*") || this.current.includes("?")) {
			const value = this.current;
			this.advance();
			return {
				type: "wildcard",
				value,
			};
		}

		// Handle regular terms
		const value = this.current;
		this.advance();
		return {
			type: "match",
			value,
		};
	}

	/**
	 * Check if current position represents an implicit AND
	 */
	private isImplicitAnd(): boolean {
		return Boolean(
			this.current &&
				this.current.toUpperCase() !== "OR" &&
				this.current.toUpperCase() !== "AND" &&
				this.current !== "+" &&
				this.current !== ")",
		);
	}

	/**
	 * Advance to the next token
	 */
	private advance(): void {
		this.position++;
		this.current = this.tokens[this.position] || "";
	}

	/**
	 * Convert parsed query to ElasticSearch DSL
	 */
	static toElasticsearchDSL(
		parsedQuery: ParsedQuery,
		defaultFields: string[] = [
			"content",
			"title",
			"file_name",
			"url",
			"file_path",
		],
	): Record<string, unknown> {
		return QueryParser.convertNode(parsedQuery, defaultFields);
	}

	/**
	 * Convert a single node to ElasticSearch DSL
	 */
	private static convertNode(
		node: ParsedQuery,
		defaultFields: string[],
	): Record<string, unknown> {
		switch (node.type) {
			case "match_all":
				return { match_all: {} };

			case "match":
				return {
					multi_match: {
						query: node.value,
						fields: defaultFields,
						type: "most_fields",
						operator: "and",
						fuzziness: "AUTO",
					},
				};

			case "phrase":
				return {
					multi_match: {
						query: node.value,
						fields: defaultFields,
						type: "phrase",
						boost: 2.0,
					},
				};

			case "wildcard":
				if (node.field) {
					return {
						wildcard: {
							[node.field]: {
								value: node.value,
								case_insensitive: true,
							},
						},
					};
				}
				return {
					query_string: {
						query: node.value,
						fields: defaultFields,
						default_operator: "AND",
					},
				};

			case "field":
				if (node.field && node.value) {
					// Check if it's a known searchable field
					const searchableFields = [
						...defaultFields,
						"project_code",
						"file_type",
						"content_type",
					];
					if (searchableFields.includes(node.field)) {
						return {
							match: {
								[node.field]: {
									query: node.value,
									operator: "and",
								},
							},
						};
					}
					// For unknown fields, use term query
					return {
						term: {
							[node.field]: node.value,
						},
					};
				}
				return { match_all: {} };

			case "bool":
				return QueryParser.convertBoolNode(node, defaultFields);

			default:
				return { match_all: {} };
		}
	}

	/**
	 * Convert boolean node to ElasticSearch DSL
	 */
	private static convertBoolNode(
		node: ParsedQuery,
		defaultFields: string[],
	): Record<string, unknown> {
		const boolQuery: {
			must?: unknown[];
			should?: unknown[];
			must_not?: unknown[];
			minimum_should_match?: number;
		} = {};

		switch (node.operator) {
			case "and":
				if (node.left && node.right) {
					boolQuery.must = [
						QueryParser.convertNode(node.left, defaultFields),
						QueryParser.convertNode(node.right, defaultFields),
					];
				}
				break;

			case "or":
				if (node.left && node.right) {
					boolQuery.should = [
						QueryParser.convertNode(node.left, defaultFields),
						QueryParser.convertNode(node.right, defaultFields),
					];
					boolQuery.minimum_should_match = 1;
				}
				break;

			case "not":
				if (node.right) {
					boolQuery.must_not = [
						QueryParser.convertNode(node.right, defaultFields),
					];
					boolQuery.must = [{ match_all: {} }];
				}
				break;
		}

		return { bool: boolQuery };
	}

	/**
	 * Validate query syntax without executing
	 */
	static validateQuery(query: string): { valid: boolean; error?: string } {
		try {
			const parser = new QueryParser();
			parser.parse(query);
			return { valid: true };
		} catch (error) {
			return {
				valid: false,
				error: error instanceof Error ? error.message : String(error),
			};
		}
	}
}

export default QueryParser;
