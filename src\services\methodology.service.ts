/**
 * Service responsible for managing methodologies, nodes, steps, and associated files.
 * Provides functionality for CRUD operations on methodology nodes and file management using Azure Blob Storage.
 */

import config from "@/config/config";
import type {
	CreateMethodologyInput,
	CreateNodeInput,
	CreateStepInput,
	UpdateNodeInput,
} from "@/controllers/methodology/methodology.schema";
import {
	type FileMetaData,
	MethodologyNodeModel,
} from "@/models/methodology.model";
import logger from "@/utils/logging";
import { BlobServiceClient, type ContainerClient } from "@azure/storage-blob";
import { Types } from "mongoose";
import { v4 as uuidv4 } from "uuid";

/**
 * Parameters for uploading a file to Azure Blob Storage
 * @interface UploadFileParams
 */
interface UploadFileParams {
	/** The file to be uploaded */
	file: Express.Multer.File;
	/** Optional ID of the node to associate the file with */
	nodeId?: Types.ObjectId;
	/** Optional ID of the step to associate the file with */
	stepId?: Types.ObjectId;
	/** Type of document being uploaded */
	docType: "framework" | "template" | "example";
}

/**
 * Result of a file upload operation
 * @interface FileUploadResult
 * @extends FileMetaData
 */
interface FileUploadResult extends FileMetaData {
	/** URL where the uploaded file can be accessed */
	url: string;
}

/**
 * Service class for managing methodologies, including nodes, steps, and associated files.
 * Handles CRUD operations and file management using Azure Blob Storage.
 */
export class MethodologyService {
	private blobServiceClient: BlobServiceClient;
	private containerClient: ContainerClient;

	/**
	 * Initializes the MethodologyService with Azure Blob Storage configuration
	 * @throws {Error} If Azure storage configuration is missing
	 */
	constructor() {
		const storageConnectionString = config.azure.storageConnectionString;
		const storageContainerName = config.azure.storageContainerName;
		if (!storageConnectionString || !storageContainerName) {
			throw new Error("Azure storage configuration is missing");
		}
		this.blobServiceClient = BlobServiceClient.fromConnectionString(
			storageConnectionString,
		);
		this.containerClient =
			this.blobServiceClient.getContainerClient(storageContainerName);
	}

	/**
	 * Retrieves methodologies based on optional parent ID
	 * @param parentId - Optional ID of the parent methodology
	 * @returns Promise resolving to an array of methodology nodes
	 */
	public async getMethodologies(parentId?: string) {
		if (parentId) {
			// Return children of the specified node
			return MethodologyNodeModel.find({ parentId })
				.sort({ createdAt: 1 })
				.lean();
		}

		// Return root level nodes (nodes with no parent)
		return MethodologyNodeModel.find({ parentId: null })
			.sort({ createdAt: 1 })
			.lean();
	}

	/**
	 * Retrieves a methodology by its name
	 * @param name - Name of the methodology to find
	 * @returns Promise resolving to the found methodology or null
	 */
	public async getMethodologyByName(name: string) {
		return MethodologyNodeModel.findOne({ name }).lean();
	}

	/**
	 * Creates a new methodology
	 * @param methodology - Data for creating the methodology
	 * @throws {Error} If parent methodology doesn't exist when parentId is provided
	 * @returns Promise resolving to the created methodology
	 */
	public async createMethodology(methodology: CreateMethodologyInput) {
		if (methodology.parentId) {
			const parentExists = await MethodologyNodeModel.exists({
				_id: methodology.parentId,
			});
			if (!parentExists) {
				throw new Error("Parent methodology not found");
			}
		}

		// Always set nodeType to 'regular' for methodologies
		const methodologyToCreate = {
			...methodology,
			nodeType: "regular",
			// Set default values for other required fields
			order: typeof methodology.order === "number" ? methodology.order : 0,
			files: [],
		};

		return MethodologyNodeModel.create(methodologyToCreate);
	}

	/**
	 * Creates a new node in the methodology
	 * @param nodeData - Data for creating the node
	 * @throws {Error} If parent node doesn't exist, or if required fields are missing
	 * @returns Promise resolving to the created node
	 */
	public async createNode(nodeData: CreateNodeInput) {
		// Validate parent exists if parentId is provided
		if (nodeData.parentId) {
			const parentExists = await MethodologyNodeModel.exists({
				_id: nodeData.parentId,
			});
			if (!parentExists) {
				throw new Error("Parent node not found");
			}
		}

		// Validate order is provided for step nodes
		if (nodeData.nodeType === "step" && typeof nodeData.order !== "number") {
			throw new Error("Order is required for step nodes");
		}

		// Validate nextLevelName for regular nodes
		if (
			nodeData.nodeType === "regular" &&
			!nodeData.nextLevelName &&
			!nodeData.order
		) {
			throw new Error("nextLevelName and order is required for regular nodes");
		}

		// Clean up data based on node type
		const nodeToCreate = {
			...nodeData,
			// For step nodes: remove regular node fields
			...(nodeData.nodeType === "step" && {
				nextLevelName: undefined,
				experts: undefined,
			}),
		};

		logger.debug("Creating node", { nodeToCreate });

		return MethodologyNodeModel.create(nodeToCreate);
	}

	/**
	 * Creates a new step in the methodology
	 * @param stepData - Data for creating the step
	 * @returns Promise resolving to the created step
	 */
	public async createStep(stepData: CreateStepInput) {
		return MethodologyNodeModel.create({
			...stepData,
			nodeType: "step",
		});
	}

	/**
	 * Uploads a single file to Azure Blob Storage and associates it with a node
	 * @param params - Upload parameters including file and metadata
	 * @throws {Error} If file is missing
	 * @returns Promise resolving to the upload result
	 */
	public async uploadFile({
		file,
		nodeId,
		docType,
	}: UploadFileParams): Promise<FileUploadResult> {
		if (!file) {
			throw new Error("File is required");
		}

		const blobName = `${uuidv4()}-${file.originalname}`;
		const blockBlobClient = this.containerClient.getBlockBlobClient(blobName);

		await blockBlobClient.uploadData(file.buffer, {
			blobHTTPHeaders: { blobContentType: file.mimetype },
		});

		const fileMetadata: FileMetaData = {
			fileId: new Types.ObjectId(),
			originalFileName: file.originalname,
			docType: docType,
			url: blockBlobClient.url,
		};

		await MethodologyNodeModel.updateOne(
			{ _id: nodeId },
			{ $push: { files: fileMetadata } },
		);

		return {
			...fileMetadata,
		};
	}

	/**
	 * Uploads multiple files and associates them with multiple nodes or steps
	 * @param files - Array of files to upload
	 * @param nodeIds - Optional array of node IDs to associate files with
	 * @param stepIds - Optional array of step IDs to associate files with
	 * @param docType - Type of documents being uploaded
	 * @throws {Error} If no files provided or if neither nodeIds nor stepIds are provided
	 * @returns Promise resolving to array of upload results
	 */
	public async bulkUploadFile({
		files,
		nodeIds,
		stepIds,
		docType,
	}: {
		files: Express.Multer.File[];
		nodeIds?: Types.ObjectId[];
		stepIds?: Types.ObjectId[];
		docType: "framework" | "template" | "example";
	}): Promise<FileUploadResult[]> {
		if (!files || files.length === 0) {
			throw new Error("At least one file is required");
		}

		const results: FileUploadResult[] = [];
		const allIds = [...(nodeIds || []), ...(stepIds || [])];

		if (allIds.length === 0) {
			throw new Error("Either nodeIds or stepIds must be provided");
		}

		// Process each file
		for (const file of files) {
			const blobName = `${uuidv4()}-${file.originalname}`;
			const blockBlobClient = this.containerClient.getBlockBlobClient(blobName);

			await blockBlobClient.uploadData(file.buffer, {
				blobHTTPHeaders: { blobContentType: file.mimetype },
			});

			const fileMetadata: FileMetaData = {
				fileId: new Types.ObjectId(),
				originalFileName: file.originalname,
				docType: docType,
				url: blockBlobClient.url,
			};

			// Update all nodes with this file
			await MethodologyNodeModel.updateMany(
				{ _id: { $in: allIds } },
				{ $push: { files: fileMetadata } },
			);

			// Add results for each node-file combination
			results.push(...allIds.map((id) => ({ ...fileMetadata, nodeId: id })));
		}

		return results;
	}

	/**
	 * Retrieves all methodologies in the system
	 * @returns Promise resolving to array of all methodology nodes
	 */
	public async getAllMethodologies() {
		return MethodologyNodeModel.find().lean();
	}

	/**
	 * Deletes a methodology by its ID
	 * @param methodologyId - ID of the methodology to delete
	 * @returns Promise resolving to the deletion result
	 */
	public async deleteMethodology(methodologyId: Types.ObjectId) {
		return MethodologyNodeModel.deleteOne({ _id: methodologyId });
	}

	/**
	 * Deletes a node by its ID
	 * @param nodeId - ID of the node to delete
	 * @returns Promise resolving to the deletion result
	 */
	public async deleteNode(nodeId: Types.ObjectId): Promise<void> {
		// Get the node and its children
		const nodeToDelete = await MethodologyNodeModel.findById(nodeId);

		if (!nodeToDelete) {
			throw new Error("Node not found");
		}

		// Find all child nodes recursively
		const childNodes = await MethodologyNodeModel.find({
			$or: [{ _id: nodeId }, { parentId: nodeId }],
		});

		const nodeIds = childNodes.map((node) => node._id);

		// Collect all file IDs from the nodes
		const fileUrls = childNodes.reduce((urls: string[], node) => {
			const nodeFileUrls = node.files.map((file) => file.url);
			urls.push(...nodeFileUrls);
			return urls;
		}, []);

		// Delete files from Azure Blob Storage
		for (const url of fileUrls) {
			try {
				const blobName = url.split("/").pop();
				if (blobName) {
					const blockBlobClient =
						this.containerClient.getBlockBlobClient(blobName);
					await blockBlobClient.delete();
				}
			} catch (error) {
				logger.error("Error deleting blob", { error, url });
				// Continue with other deletions even if one fails
			}
		}

		// Delete all nodes
		await MethodologyNodeModel.deleteMany({
			_id: { $in: nodeIds },
		});
	}

	/**
	 * Deletes a step by its ID
	 * @param stepId - ID of the step to delete
	 * @returns Promise resolving to the deletion result
	 */
	public async deleteStep(stepId: Types.ObjectId) {
		return MethodologyNodeModel.deleteOne({ _id: stepId });
	}

	/**
	 * Removes a file association from a node
	 * @param fileId - ID of the file to remove
	 * @param nodeId - ID of the node containing the file
	 * @returns Promise resolving to the update result
	 */
	public async deleteFile(fileId: Types.ObjectId, nodeId: Types.ObjectId) {
		return MethodologyNodeModel.updateOne(
			{ _id: nodeId, "files.fileId": fileId },
			{ $pull: { files: { fileId } } },
		);
	}

	/**
	 * Saves file metadata for multiple files and associates them with multiple nodes or steps
	 * @param files - Array of file metadata objects
	 * @param nodeIds - Optional array of node IDs to associate files with
	 * @param stepIds - Optional array of step IDs to associate files with
	 * @param docType - Type of documents being uploaded
	 * @throws {Error} If no files provided or if neither nodeIds nor stepIds are provided
	 * @returns Promise resolving to array of upload results
	 */
	public async bulkSaveFileMetadata({
		files,
		nodeIds,
		stepIds,
		docType,
	}: {
		files: { originalFileName: string; url: string }[];
		nodeIds?: Types.ObjectId[];
		stepIds?: Types.ObjectId[];
		docType: "framework" | "template" | "example";
	}): Promise<FileUploadResult[]> {
		if (!files || files.length === 0) {
			throw new Error("At least one file metadata object is required");
		}

		const results: FileUploadResult[] = [];
		const allIds = [...(nodeIds || []), ...(stepIds || [])];
		if (allIds.length === 0) {
			throw new Error("Either nodeIds or stepIds must be provided");
		}

		// Process each file metadata entry
		for (const fileData of files) {
			const fileMetadata: FileMetaData = {
				fileId: new Types.ObjectId(),
				originalFileName: fileData.originalFileName,
				docType: docType,
				url: fileData.url,
			};
			await MethodologyNodeModel.updateMany(
				{ _id: { $in: allIds } },
				{ $push: { files: fileMetadata } },
			);
			// Return results for each node id combined with file metadata
			results.push(...allIds.map((id) => ({ ...fileMetadata, nodeId: id })));
		}

		return results;
	}

	/**
	 * Updates a node by its ID
	 * @param nodeId - ID of the node to update
	 * @param updateData - Data to update on the node
	 * @returns Promise resolving to the updated node
	 */
	public async updateNode(nodeId: Types.ObjectId, updateData: UpdateNodeInput) {
		// Validate the node exists
		const nodeExists = await MethodologyNodeModel.exists({ _id: nodeId });
		if (!nodeExists) {
			throw new Error("Node not found");
		}

		// If parentId is being changed, validate the new parent exists
		if (updateData.parentId) {
			const parentExists = await MethodologyNodeModel.exists({
				_id: updateData.parentId,
			});
			if (!parentExists) {
				throw new Error("Parent node not found");
			}
		}

		// If nodeType is being changed, validate it's a valid change
		if (updateData.nodeType) {
			const existingNode = await MethodologyNodeModel.findById(nodeId).lean();
			if (existingNode && existingNode.nodeType !== updateData.nodeType) {
				// Check if the node has children - if so, changing type might be problematic
				const hasChildren = await MethodologyNodeModel.exists({
					parentId: nodeId,
				});
				if (hasChildren) {
					throw new Error("Cannot change node type for a node with children");
				}

				// Additional validation for step nodes
				if (
					updateData.nodeType === "step" &&
					!updateData.parentId &&
					!existingNode.parentId
				) {
					throw new Error("Step nodes must have a parent");
				}
			}
		}

		// Update the node with type-safe data
		const typedUpdateData = updateData as Record<string, unknown>;

		return MethodologyNodeModel.findByIdAndUpdate(
			nodeId,
			{ $set: typedUpdateData },
			{ new: true },
		).lean();
	}

	/**
	 * Updates a methodology by its ID
	 * @param methodologyId - ID of the methodology to update
	 * @param updateData - Data to update on the methodology
	 * @returns Promise resolving to the updated methodology
	 */
	public async updateMethodology(
		methodologyId: Types.ObjectId,
		updateData: Partial<CreateMethodologyInput> & {
			experts?: Array<{ name?: string; email?: string; title?: string }>;
			nodeType?: string;
		},
	) {
		// Validate the methodology exists
		const methodologyExists = await MethodologyNodeModel.exists({
			_id: methodologyId,
		});
		if (!methodologyExists) {
			throw new Error("Methodology not found");
		}

		// If parentId is being changed, validate the new parent exists
		if (updateData.parentId) {
			const parentExists = await MethodologyNodeModel.exists({
				_id: updateData.parentId,
			});
			if (!parentExists) {
				throw new Error("Parent methodology not found");
			}
		}

		// Prepare update data
		const updateFields = { ...updateData };

		// Handle experts field specially to ensure it's properly updated
		if (Array.isArray(updateData.experts)) {
			// Make sure experts is properly formatted
			updateFields.experts = updateData.experts.map((expert) => {
				const formattedExpert: {
					name: string;
					email?: string;
					title?: string;
				} = {
					name: expert.name || "",
				};

				// Only include email and title if they are provided and not empty strings
				if (expert.email && expert.email.trim() !== "") {
					formattedExpert.email = expert.email;
				}

				if (expert.title && expert.title.trim() !== "") {
					formattedExpert.title = expert.title;
				}

				return formattedExpert;
			});
		}

		// Update the methodology
		return MethodologyNodeModel.findByIdAndUpdate(
			methodologyId,
			{ $set: updateFields },
			{ new: true },
		).lean();
	}

	/**
	 * Uploads a file or saves file metadata for a methodology
	 * @param methodologyId - ID of the methodology to add the file to
	 * @param fileData - Either a file object or metadata with URL
	 * @param docType - Type of document being uploaded
	 * @returns Promise resolving to the file metadata
	 */
	public async uploadMethodologyFile({
		methodologyId,
		file,
		url,
		originalFileName,
		docType,
	}: {
		methodologyId: Types.ObjectId;
		file?: Express.Multer.File;
		url?: string;
		originalFileName?: string;
		docType: "framework" | "template" | "example";
	}): Promise<FileMetaData> {
		// Validate the methodology exists
		const methodologyExists = await MethodologyNodeModel.exists({
			_id: methodologyId,
		});
		if (!methodologyExists) {
			throw new Error("Methodology not found");
		}

		let fileMetadata: FileMetaData;

		// Handle file upload
		if (file) {
			const blobName = `${uuidv4()}-${file.originalname}`;
			const blockBlobClient = this.containerClient.getBlockBlobClient(blobName);

			await blockBlobClient.uploadData(file.buffer, {
				blobHTTPHeaders: { blobContentType: file.mimetype },
			});

			// fileMetadata = {
			// 	fileId: new Types.ObjectId(),
			// 	originalFileName: file.originalname,
			// 	docType,
			// 	url: blockBlobClient.url,
			// };

			// we need to strip the container name from the url
			const urlWithoutContainer = blockBlobClient.url
				.split("/")
				.slice(3)
				.join("/");
			const prefixpath = "/files/";
			const uploadedPath = prefixpath + urlWithoutContainer;
			logger.debug("uploaded relative path", { uploadedPath });
			fileMetadata = {
				fileId: new Types.ObjectId(),
				originalFileName: file.originalname,
				docType,
				url: uploadedPath,
			};
		}
		// Handle URL link
		else if (url && originalFileName) {
			fileMetadata = {
				fileId: new Types.ObjectId(),
				originalFileName,
				docType,
				url,
			};
		} else {
			throw new Error(
				"Either a file or URL with originalFileName must be provided",
			);
		}

		// Add the file to the methodology
		await MethodologyNodeModel.updateOne(
			{ _id: methodologyId },
			{ $push: { files: fileMetadata } },
		);

		return fileMetadata;
	}
}
