// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { prop, getModelForClass, modelOptions } from '@typegoose/typegoose';

@modelOptions({
  schemaOptions: {
    collection: 'sub_sector_specific_questions',
  },
})
class SubSectorQuestion {
  @prop({ required: true, type: () => String })
  public sector_code!: string;

  @prop({ required: true, type: () => String })
  public question!: string;
}

export const SubSectorQuestionModel = getModelForClass(SubSectorQuestion);
export default SubSectorQuestion;
