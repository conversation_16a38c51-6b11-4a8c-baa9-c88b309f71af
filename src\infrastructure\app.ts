import "express-async-errors"; // Import this before importing any other modules
import config from "@/config/config";
import { swaggerSpec } from "@/config/swagger";
import logger from "@/utils/logging";
import cors from "cors";
import express, {
	type Application,
	type Request,
	type Response,
	type NextFunction,
} from "express";
import swaggerUi from "swagger-ui-express";
import { errorHandler } from "../middlewares/errorHandling";
import { apiLimiter, generalLimiter } from "../middlewares/rateLimiting";
import {
	correlationIdMiddleware,
	requestLoggingMiddleware,
	slowRequestLoggingMiddleware,
} from "../middlewares/requestLogging";
import { TokenMiddleware } from "../middlewares/validateToken";
import { abacusRouter, einsteinRouter } from "../routes";

let app: Application = express();

const isDevelopmentEnv = () => {
	return process.env.NODE_ENV === "development";
};

const setupLoggingMiddlewares = (app: Application): Application => {
	if (isDevelopmentEnv()) return app;

	// Add correlation ID as the first middleware
	app.use(correlationIdMiddleware);

	// Add request logging before other middlewares
	app.use(requestLoggingMiddleware);

	// Add slow request detection
	app.use(slowRequestLoggingMiddleware(1000)); // Log requests taking more than 1 second

	// Apply general rate limiting to all routes
	app.use(generalLimiter);
	return app;
};

app = setupLoggingMiddlewares(app);

app.use(
	cors({
		origin: "*",
		methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
		allowedHeaders: [
			"Content-Type",
			"Authorization",
			"x-azure-ad-object-id",
			"x-correlation-id",
		],
		optionsSuccessStatus: 200,
	}),
);
app.use(express.json());

const tokenMiddleware = TokenMiddleware.getInstance();
const authMiddleware = config.auth.isEnabled
	? tokenMiddleware.validateToken()
	: (req: Request, res: Response, next: NextFunction) => next();

/**
 * @swagger
 * /api/health:
 *   get:
 *     summary: Health check endpoint
 *     tags: [Health]
 *     description: Check if the server is running
 *     responses:
 *       200:
 *         description: Server is healthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: ok
 *                 message:
 *                   type: string
 *                   example: Server is running
 */
app.use("/api/health", (req, res) => {
	res.status(200).send({
		status: "ok",
		message: "Server is running",
	});
});

// Swagger documentation route (no auth required for docs)
app.use(
	"/api-docs",
	swaggerUi.serve,
	swaggerUi.setup(swaggerSpec, {
		customCss: ".swagger-ui .topbar { display: none }",
		customSiteTitle: "Einstein Backend API Documentation",
		swaggerOptions: {
			persistAuthorization: true,
		},
	}),
);

app.use("/api/einstein", authMiddleware, apiLimiter, einsteinRouter);
app.use("/api/abacus/", authMiddleware, apiLimiter, abacusRouter);

// Use the enhanced error handler instead of the basic one
app.use(errorHandler);

export default app;
