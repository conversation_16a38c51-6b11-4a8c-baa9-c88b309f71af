import {
	FilterInput,
	type ProjectDocument,
	type FiltersResponse,
	ProjectLocationDTO,
	ProjectDetailDTO,
} from "@/services/maps/map.types";

/**
 * Repository interface for map data operations
 */
export interface MapRepository {
	/**
	 * Get available filters for projects
	 */
	getFilters(): Promise<FiltersResponse>;

	/**
	 * Find projects matching the given filters
	 * @param query - MongoDB query object built from filters
	 * @param projection - Fields to include in the results
	 * @param limit - Maximum number of results to return
	 */
	findFilteredProjects(
		query: Record<string, unknown>,
		projection: Record<string, number>,
		limit: number,
	): Promise<ProjectDocument[]>;

	/**
	 * Find a project by its ID
	 * @param projectId - The project ID to search for
	 * @param projection - Fields to include in the results
	 */
	findProjectById(
		projectId: string,
		projection: Record<string, number>,
	): Promise<ProjectDocument | null>;

	/**
	 * Find a map location by project ID
	 * @param projectId - The project ID to search for
	 * @param projection - Fields to include in the results
	 */
	findMapLocationByProjectId(
		projectId: string,
		projection: Record<string, number>,
	): Promise<Record<string, any> | null>;

	/**
	 * Get distinct values for a field
	 * @param field - The field to get distinct values for
	 * @param query - Optional query to filter the documents
	 */
	getDistinctValues(
		field: string,
		query?: Record<string, unknown>,
	): Promise<string[]>;

	/**
	 * Count documents matching a query
	 * @param query - The query to count documents for
	 */
	countDocuments(query: Record<string, unknown>): Promise<number>;
}
