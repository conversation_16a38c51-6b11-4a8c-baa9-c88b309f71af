import type { Request, Response, NextFunction } from "express";
import type { NotesService } from "./notes.service";
import { getNotesQuerySchema, getNoteByIdParamsSchema, bulkUpdateNotesSchema } from "./notes.types";
import logger from "@/utils/logging";
import { HttpException } from "@/utils/exceptions/http.exception";

export class NotesController {
	constructor(private readonly notesService: NotesService) {}

	/**
	 * Get all notes with optional filtering
	 * GET /notes?tradeId=123&activeOnly=true&visibleOnly=true
	 */
	public getAllNotes = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const queryValidation = getNotesQuerySchema.safeParse(req.query);
			if (!queryValidation.success) {
				throw new HttpException(400, "Invalid query parameters");
			}

			const { tradeId, tradeInfoId, activeOnly, visibleOnly } =
				queryValidation.data;

			const options = {
				tradeId: tradeId ? Number.parseInt(tradeId) : undefined,
				tradeInfoId: tradeInfoId ? Number.parseInt(tradeInfoId) : undefined,
				activeOnly,
				visibleOnly,
			};

			logger.info("NotesController: Getting all notes with options:", options);

			const notes = await this.notesService.getAllNotes(options);

			res.status(200).json({
				success: true,
				data: notes,
				count: notes.length,
			});
		} catch (error) {
			logger.error("NotesController: Error in getAllNotes:", error);
			next(error);
		}
	};

	/**
	 * Get a note by ID
	 * GET /notes/:id
	 */
	public getNoteById = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const paramsValidation = getNoteByIdParamsSchema.safeParse(req.params);
			if (!paramsValidation.success) {
				throw new HttpException(400, "Invalid note ID");
			}

			const { id } = paramsValidation.data;

			logger.info(`NotesController: Getting note by ID: ${id}`);

			const note = await this.notesService.getNoteById(id);

			res.status(200).json({
				success: true,
				data: note,
			});
		} catch (error) {
			logger.error("NotesController: Error in getNoteById:", error);
			next(error);
		}
	};

	/**
	 * Get notes by Trade ID
	 * GET /notes/trade/:tradeId
	 */
	public getNotesByTradeId = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const tradeId = Number.parseInt(req.params.tradeId);
			if (Number.isNaN(tradeId)) {
				throw new HttpException(400, "Invalid Trade ID");
			}

			logger.info(`NotesController: Getting notes by TradeID: ${tradeId}`);

			const notes = await this.notesService.getNotesByTradeId(tradeId);

			res.status(200).json({
				success: true,
				data: notes,
				count: notes.length,
			});
		} catch (error) {
			logger.error("NotesController: Error in getNotesByTradeId:", error);
			next(error);
		}
	};

	/**
	 * Get notes by Trade Info ID
	 * GET /notes/tradeinfo/:tradeInfoId
	 */
	public getNotesByTradeInfoId = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const tradeInfoId = Number.parseInt(req.params.tradeInfoId);
			if (Number.isNaN(tradeInfoId)) {
				throw new HttpException(400, "Invalid Trade Info ID");
			}

			logger.info(
				`NotesController: Getting notes by TradeInfoID: ${tradeInfoId}`,
			);

			const notes = await this.notesService.getNotesByTradeInfoId(tradeInfoId);

			res.status(200).json({
				success: true,
				data: notes,
				count: notes.length,
			});
		} catch (error) {
			logger.error("NotesController: Error in getNotesByTradeInfoId:", error);
			next(error);
		}
	};

	/**
	 * Get active notes only
	 * GET /notes/active
	 */
	public getActiveNotes = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			logger.info("NotesController: Getting active notes");

			const notes = await this.notesService.getActiveNotes();

			res.status(200).json({
				success: true,
				data: notes,
				count: notes.length,
			});
		} catch (error) {
			logger.error("NotesController: Error in getActiveNotes:", error);
			next(error);
		}
	};

	/**
	 * Get visible notes only
	 * GET /notes/visible
	 */
	public getVisibleNotes = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			logger.info("NotesController: Getting visible notes");

			const notes = await this.notesService.getVisibleNotes();

			res.status(200).json({
				success: true,
				data: notes,
				count: notes.length,
			});
		} catch (error) {
			logger.error("NotesController: Error in getVisibleNotes:", error);
			next(error);
		}
	};

	/**
	 * Bulk update multiple notes
	 * PATCH /notes/bulk
	 */
	public bulkUpdateNotes = async (
		req: Request,
		res: Response,
		next: NextFunction,
	) => {
		try {
			const bodyValidation = bulkUpdateNotesSchema.safeParse(req.body);
			if (!bodyValidation.success) {
				throw new HttpException(400, "Invalid request body");
			}

			const { updates } = bodyValidation.data;

			logger.info(`NotesController: Bulk updating ${updates.length} notes`);

			const result = await this.notesService.bulkUpdateNotes(updates);

			res.status(200).json({
				success: true,
				message: "Bulk update completed",
				data: {
					modifiedCount: result.modifiedCount,
					matchedCount: result.matchedCount,
				},
			});
		} catch (error) {
			logger.error("NotesController: Error in bulkUpdateNotes:", error);
			next(error);
		}
	};
}
