// src/utils/AzureBlobService.ts
import logger from "@/utils/logging";
import type { BlobServiceClient } from "@azure/storage-blob";
import {
	BlobSASPermissions,
	ContainerSASPermissions,
} from "@azure/storage-blob";
import { v4 as uuidv4 } from "uuid";
import type { BlobStreamResult } from "./file.types";

export class AzureBlobService {
	private ACCESS_TOKEN_EXPIRY_TIME = 1000 * 60 * 60 * 24;
	constructor(
		private blobServiceClient: BlobServiceClient,
		private defaultContainerName?: string,
	) {}

	async uploadFile(
		buffer: Buffer,
		originalName: string,
		mimeType: string,
		containerName?: string,
	) {
		const containerToUse = containerName || this.defaultContainerName;
		if (!containerToUse) {
			throw new Error(
				"Container name must be provided either in constructor or method parameter",
			);
		}

		const storageName = `${uuidv4()}-${originalName}`;
		const containerClient =
			this.blobServiceClient.getContainerClient(containerToUse);
		const blockBlobClient = containerClient.getBlockBlobClient(storageName);

		await blockBlobClient.uploadData(buffer, {
			blobHTTPHeaders: { blobContentType: mimeType },
		});

		const urlWithoutContainer = blockBlobClient.url
			.split("/")
			.slice(3)
			.join("/");
		const prefixpath = "/files/";
		const url = prefixpath + urlWithoutContainer;
		return { url, storageName };
	}

	async generateSASToken(blobName: string, containerName?: string) {
		const containerToUse = containerName || this.defaultContainerName;
		if (!containerToUse) {
			throw new Error(
				"Container name must be provided either in constructor or method parameter",
			);
		}

		const containerClient =
			this.blobServiceClient.getContainerClient(containerToUse);
		const blobClient = containerClient.getBlockBlobClient(blobName);
		try {
			const sasToken = await blobClient.generateSasUrl({
				permissions: BlobSASPermissions.from({ read: true }),
				expiresOn: new Date(Date.now() + this.ACCESS_TOKEN_EXPIRY_TIME),
			});
			return sasToken;
		} catch (error) {
			logger.error(`Error generating SAS token: ${error}`);
			throw error;
		}
	}

	async generateContainerSASToken(containerName?: string) {
		const containerToUse = containerName || this.defaultContainerName;
		if (!containerToUse) {
			throw new Error(
				"Container name must be provided either in constructor or method parameter",
			);
		}

		const containerClient =
			this.blobServiceClient.getContainerClient(containerToUse);
		const sasToken = await containerClient.generateSasUrl({
			permissions: ContainerSASPermissions.from({
				read: true,
			}),
			expiresOn: new Date(Date.now() + this.ACCESS_TOKEN_EXPIRY_TIME),
		});
		return sasToken;
	}

	/**
	 * Generate a presigned URL for uploading files directly to Azure Blob Storage
	 * @param originalName - The original filename
	 * @param mimeType - The MIME type of the file
	 * @param expiryMinutes - URL expiry time in minutes (default: 60 minutes)
	 * @returns Object containing the presigned URL and the storage name
	 */
	async generateUploadPresignedUrl(
		originalName: string,
		mimeType: string,
		expiryMinutes = 60,
		containerName?: string,
	): Promise<{ presignedUrl: string; storageName: string; blobUrl: string }> {
		try {
			const containerToUse = containerName || this.defaultContainerName;
			if (!containerToUse) {
				throw new Error(
					"Container name must be provided either in constructor or method parameter",
				);
			}

			const storageName = `${uuidv4()}-${originalName}`;
			const containerClient =
				this.blobServiceClient.getContainerClient(containerToUse);
			const blockBlobClient = containerClient.getBlockBlobClient(storageName);

			const expiryDate = new Date(Date.now() + expiryMinutes * 60 * 1000);

			const presignedUrl = await blockBlobClient.generateSasUrl({
				permissions: BlobSASPermissions.from({
					read: true,
					write: true,
					create: true,
				}),
				expiresOn: expiryDate,
			});

			return {
				presignedUrl,
				storageName,
				blobUrl: blockBlobClient.url,
			};
		} catch (error) {
			logger.error(`Error generating upload presigned URL: ${error}`);
			throw error;
		}
	}

	/**
	 * Generate multiple presigned URLs for batch uploads
	 * @param files - Array of file info objects
	 * @param expiryMinutes - URL expiry time in minutes (default: 60 minutes)
	 * @returns Array of presigned URL objects
	 */
	async generateBatchUploadPresignedUrls(
		files: Array<{ originalName: string; mimeType: string }>,
		expiryMinutes = 60,
		containerName?: string,
	): Promise<
		Array<{
			presignedUrl: string;
			storageName: string;
			blobUrl: string;
			originalName: string;
		}>
	> {
		try {
			const results = await Promise.all(
				files.map(async (file) => {
					const result = await this.generateUploadPresignedUrl(
						file.originalName,
						file.mimeType,
						expiryMinutes,
						containerName,
					);
					return {
						...result,
						originalName: file.originalName,
					};
				}),
			);

			return results;
		} catch (error) {
			logger.error(`Error generating batch upload presigned URLs: ${error}`);
			throw error;
		}
	}

	/**
	 * Delete a file from Azure Blob Storage using its URL
	 * @param blobUrl - The full URL of the blob to delete
	 * @returns Promise<boolean> - true if deleted successfully, false if file doesn't exist
	 */
	async deleteFileByUrl(
		blobUrl: string,
		containerName?: string,
	): Promise<boolean> {
		try {
			const blobInfo = this.extractBlobInfoFromUrl(blobUrl);
			if (!blobInfo) {
				throw new Error("Invalid blob URL: Unable to extract blob information");
			}

			// Use provided container name or extract from URL, with fallback to default
			const containerToUse =
				containerName || blobInfo.containerName || this.defaultContainerName;
			if (!containerToUse) {
				throw new Error(
					"Container name must be provided either in constructor, method parameter, or be extractable from URL",
				);
			}

			const containerClient =
				this.blobServiceClient.getContainerClient(containerToUse);
			logger.info(
				`Deleting blob: ${blobInfo.blobName} from container: ${containerToUse}`,
			);
			const blockBlobClient = containerClient.getBlockBlobClient(
				blobInfo.blobName,
			);

			const deleteResponse = await blockBlobClient.deleteIfExists();

			if (deleteResponse.succeeded) {
				logger.info(`Successfully deleted blob: ${blobInfo.blobName}`);
				return true;
			}

			logger.warn(`Blob not found or already deleted: ${blobInfo.blobName}`);
			return false;
		} catch (error) {
			logger.error(`Error deleting blob: ${error}`);
			throw error;
		}
	}

	/**
	 * Delete a file from Azure Blob Storage using its storage name
	 * @param storageName - The storage name (blob name) of the file to delete
	 * @returns Promise<boolean> - true if deleted successfully, false if file doesn't exist
	 */
	async deleteFileByStorageName(
		storageName: string,
		containerName?: string,
	): Promise<boolean> {
		try {
			const containerToUse = containerName || this.defaultContainerName;
			if (!containerToUse) {
				throw new Error(
					"Container name must be provided either in constructor or method parameter",
				);
			}

			const containerClient =
				this.blobServiceClient.getContainerClient(containerToUse);
			const blockBlobClient = containerClient.getBlockBlobClient(storageName);

			const deleteResponse = await blockBlobClient.deleteIfExists();

			if (deleteResponse.succeeded) {
				logger.info(`Successfully deleted blob: ${storageName}`);
				return true;
			}

			logger.warn(`Blob not found or already deleted: ${storageName}`);
			return false;
		} catch (error) {
			logger.error(`Error deleting blob: ${error}`);
			throw error;
		}
	}

	/**
	 * Delete multiple files from Azure Blob Storage
	 * @param blobUrls - Array of blob URLs to delete
	 * @returns Promise<Array<{url: string, success: boolean, error?: string}>>
	 */
	async deleteMultipleFiles(
		blobUrls: string[],
		containerName?: string,
	): Promise<Array<{ url: string; success: boolean; error?: string }>> {
		try {
			const deletePromises = blobUrls.map(async (url) => {
				try {
					const success = await this.deleteFileByUrl(url, containerName);
					return { url, success };
				} catch (error) {
					return {
						url,
						success: false,
						error: error instanceof Error ? error.message : "Unknown error",
					};
				}
			});

			const results = await Promise.all(deletePromises);
			return results;
		} catch (error) {
			logger.error(`Error deleting multiple files: ${error}`);
			throw error;
		}
	}

	/**
	 * Extract blob name and container name from Azure Blob Storage URL
	 * @param blobUrl - The full URL of the blob
	 * @returns {containerName: string, blobName: string} | null - The container and blob names or null if invalid URL
	 */
	private extractBlobInfoFromUrl(
		blobUrl: string,
	): { containerName: string; blobName: string } | null {
		try {
			const url = new URL(blobUrl);
			const pathSegments = url.pathname
				.split("/")
				.filter((segment) => segment.length > 0);

			// Azure Blob Storage URL format: https://<account>.blob.core.windows.net/<container>/<blobname>
			// First segment after domain is container, rest is blob name
			if (pathSegments.length < 2) {
				return null;
			}

			const containerName = pathSegments[0];
			const blobName = pathSegments.slice(1).join("/");

			return {
				containerName: decodeURIComponent(containerName),
				blobName: decodeURIComponent(blobName),
			};
		} catch (error) {
			logger.error(`Error extracting blob info from URL: ${error}`);
			return null;
		}
	}

	/**
	 * Check if a file exists in Azure Blob Storage
	 * @param blobUrl - The full URL of the blob
	 * @returns Promise<boolean> - true if file exists, false otherwise
	 */
	async fileExists(blobUrl: string, containerName?: string): Promise<boolean> {
		try {
			const blobInfo = this.extractBlobInfoFromUrl(blobUrl);
			if (!blobInfo) {
				return false;
			}

			// Use provided container name or extract from URL, with fallback to default
			const containerToUse =
				containerName || blobInfo.containerName || this.defaultContainerName;
			if (!containerToUse) {
				throw new Error(
					"Container name must be provided either in constructor, method parameter, or be extractable from URL",
				);
			}

			const containerClient =
				this.blobServiceClient.getContainerClient(containerToUse);
			const blockBlobClient = containerClient.getBlockBlobClient(
				blobInfo.blobName,
			);

			const exists = await blockBlobClient.exists();
			return exists;
		} catch (error) {
			logger.error(`Error checking if file exists: ${error}`);
			return false;
		}
	}

	/**
	 * Download blob content as a readable stream
	 * @param blobName - The name of the blob to download
	 * @param containerName - Optional container name (uses default if not provided)
	 * @returns Promise<{stream: NodeJS.ReadableStream, properties: BlobProperties}>
	 */
	async downloadBlobStream(
		blobName: string,
		containerName?: string,
	): Promise<BlobStreamResult> {
		try {
			const containerToUse = containerName || this.defaultContainerName;
			if (!containerToUse) {
				throw new Error(
					"Container name must be provided either in constructor or method parameter",
				);
			}

			const containerClient =
				this.blobServiceClient.getContainerClient(containerToUse);
			const blockBlobClient = containerClient.getBlockBlobClient(blobName);

			// Check if blob exists
			const exists = await blockBlobClient.exists();
			if (!exists) {
				throw new Error(
					`Blob ${blobName} not found in container ${containerToUse}`,
				);
			}

			// Get blob properties for metadata
			const properties = await blockBlobClient.getProperties();

			// Download blob as stream
			const downloadResponse = await blockBlobClient.download();

			if (!downloadResponse.readableStreamBody) {
				throw new Error("Failed to get readable stream from blob");
			}

			return {
				stream: downloadResponse.readableStreamBody,
				contentType: properties.contentType,
				contentLength: properties.contentLength,
				lastModified: properties.lastModified,
			};
		} catch (error) {
			logger.error(`Error downloading blob stream: ${error}`);
			throw error;
		}
	}
}
