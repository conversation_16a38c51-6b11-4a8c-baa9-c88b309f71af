# User Management API Documentation

## Overview
This document provides comprehensive API examples for the Einstein User Management system. All endpoints require proper authentication and role-based permissions.

## Base Configuration

**Base URL:** `/api/einstein/users`

**Required Headers for all requests:**
```json
{
  "Authorization": "Bearer <jwt_token>",
  "Content-Type": "application/json"
}
```

## API Endpoints

### 1. Get All Users

**Endpoint:** `GET /api/einstein/users`
**Required Roles:** `read-users` OR `SYSTEM_ADMIN`

```http
GET /api/einstein/users
Authorization: Bearer <jwt_token>
```

**Success Response (200):**
```json
[
  {
    "id": "507f1f77bcf86cd799439011",
    "azureAdObjectId": "<EMAIL>",
    "email": "<EMAIL>",
    "name": "<PERSON>",
    "roles": ["507f1f77bcf86cd799439013", "507f1f77bcf86cd799439016"],
    "assignedBy": "507f1f77bcf86cd799439012",
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  },
  {
    "id": "507f1f77bcf86cd799439015",
    "azureAdObjectId": "<EMAIL>",
    "email": "<EMAIL>",
    "name": "Jane Smith",
    "roles": ["507f1f77bcf86cd799439017"],
    "assignedBy": "507f1f77bcf86cd799439011",
    "createdAt": "2024-01-16T10:30:00Z",
    "updatedAt": "2024-01-16T10:30:00Z"
  }
]
```

---

### 2. Create New User

**Endpoint:** `POST /api/einstein/users`
**Required Roles:** `create-users` OR `SYSTEM_ADMIN`

```http
POST /api/einstein/users
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body (Basic User):**
```json
{
  "azureAdObjectId": "<EMAIL>"
}
```

**Request Body (User with Initial Roles):**
```json
{
  "azureAdObjectId": "<EMAIL>",
  "roles": ["507f1f77bcf86cd799439017", "507f1f77bcf86cd799439018"],
  "assignedBy": "507f1f77bcf86cd799439012"
}
```

**Success Response (201):**
```json
{
  "id": "507f1f77bcf86cd799439011",
  "azureAdObjectId": "<EMAIL>",
  "email": "<EMAIL>",
  "name": "John Doe",
  "roles": ["507f1f77bcf86cd799439017"],
  "assignedBy": "507f1f77bcf86cd799439012",
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:30:00Z"
}
```

**Error Response (400 - User Exists):**
```json
{
  "message": "User with this Azure AD Object ID already exists",
  "code": "USER_EXISTS",
  "statusCode": 400
}
```

---

### 3. Get Specific User

**Endpoint:** `GET /api/einstein/users/:azureAdObjectId`
**Required Roles:** `read-users` OR `SYSTEM_ADMIN`

```http
GET /api/einstein/users/<EMAIL>
Authorization: Bearer <jwt_token>
```

**Success Response (200):**
```json
{
  "id": "507f1f77bcf86cd799439011",
  "azureAdObjectId": "<EMAIL>",
  "email": "<EMAIL>",
  "name": "John Doe",
  "roles": ["507f1f77bcf86cd799439013", "507f1f77bcf86cd799439016"],
  "assignedBy": "507f1f77bcf86cd799439012",
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:30:00Z"
}
```

**Error Response (404):**
```json
{
  "message": "User not found",
  "code": "USER_NOT_FOUND",
  "statusCode": 404
}
```

---

### 4. Get Current User's Roles

**Endpoint:** `GET /api/einstein/users/roles`
**Required Roles:** No specific role (returns current user's roles)

```http
GET /api/einstein/users/roles
Authorization: Bearer <jwt_token>
x-azure-ad-object-id: <EMAIL>
```

**Success Response (200):**
```json
[
  {
    "id": "507f1f77bcf86cd799439013",
    "name": "SYSTEM_ADMIN",
    "description": "Full system administration privileges",
    "permissions": ["507f1f77bcf86cd799439019", "507f1f77bcf86cd79943901a"],
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
]
```

---

### 5. Create New Role

**Endpoint:** `POST /api/einstein/users/roles`
**Required Roles:** `manage-roles` OR `SYSTEM_ADMIN`

```http
POST /api/einstein/users/roles
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "project-manager",
  "description": "Can manage projects and team members"
}
```

**Success Response (201):**
```json
{
  "id": "507f1f77bcf86cd799439013",
  "name": "project-manager",
  "description": "Can manage projects and team members",
  "permissions": [],
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:30:00Z"
}
```

**Error Response (400 - Role Exists):**
```json
{
  "message": "Role with this name already exists",
  "code": "ROLE_EXISTS",
  "statusCode": 400
}
```

---

### 6. Assign Role to User

**Endpoint:** `PUT /api/einstein/users/roles`
**Required Roles:** `manage-users` OR `SYSTEM_ADMIN`

```http
PUT /api/einstein/users/roles
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "azureAdObjectId": "<EMAIL>",
  "role": "507f1f77bcf86cd799439016"
}
```

**Success Response (200):**
```json
{
  "id": "507f1f77bcf86cd799439011",
  "azureAdObjectId": "<EMAIL>",
  "email": "<EMAIL>",
  "name": "John Doe",
  "roles": ["507f1f77bcf86cd799439017", "507f1f77bcf86cd799439016"],
  "assignedBy": "507f1f77bcf86cd799439012",
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-16T10:30:00Z"
}
```

**Error Response (400 - User Not Found):**
```json
{
  "message": "User not found",
  "code": "USER_NOT_FOUND",
  "statusCode": 400
}
```

**Error Response (400 - Role Already Assigned):**
```json
{
  "message": "User already has this role",
  "code": "ROLE_ALREADY_ASSIGNED",
  "statusCode": 400
}
```

---

### 7. Get All Available Roles

**Endpoint:** `GET /api/einstein/users/roles/all`
**Required Roles:** `read-roles` OR `SYSTEM_ADMIN`

```http
GET /api/einstein/users/roles/all
Authorization: Bearer <jwt_token>
```

**Success Response (200):**
```json
[
  {
    "id": "507f1f77bcf86cd799439013",
    "name": "SYSTEM_ADMIN",
    "description": "Grant admin access to Einstein",
    "permissions": [
      {
        "id": "507f1f77bcf86cd799439019",
        "name": "manage-users",
        "description": "Can create, update, and delete users"
      },
      {
        "id": "507f1f77bcf86cd79943901a",
        "name": "manage-projects",
        "description": "Can create, update, and delete projects"
      },
      {
        "id": "507f1f77bcf86cd79943901b",
        "name": "view-analytics",
        "description": "Can view system analytics and reports"
      }
    ]
  },
  {
    "id": "507f1f77bcf86cd799439016",
    "name": "USER",
    "description": "Grant basic user access to Einstein",
    "permissions": [
      {
        "id": "507f1f77bcf86cd799439019",
        "name": "view-projects",
        "description": "Can view project details"
      }
    ]
  },
  {
    "id": "507f1f77bcf86cd799439017",
    "name": "Data Reader",
    "description": "Read the data of a user",
    "permissions": [
      {
        "id": "507f1f77bcf86cd79943901e",
        "name": "read-data",
        "description": "Can read user data"
      }
    ]
  }
]
```

---

### 8. Create New Permission

**Endpoint:** `POST /api/einstein/users/permissions`
**Required Roles:** `manage-permissions` OR `SYSTEM_ADMIN`

```http
POST /api/einstein/users/permissions
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "create-project",
  "description": "Allows creating new projects in the system"
}
```

**Success Response (201):**
```json
{
  "id": "507f1f77bcf86cd799439014",
  "name": "create-project",
  "description": "Allows creating new projects in the system",
  "createdAt": "2024-01-15T10:30:00Z"
}
```

**Error Response (400 - Permission Exists):**
```json
{
  "message": "Permission with this name already exists",
  "code": "PERMISSION_EXISTS",
  "statusCode": 400
}
```

---

### 9. Assign Permission to Role

**Endpoint:** `PUT /api/einstein/users/permissions`
**Required Roles:** `manage-roles` OR `SYSTEM_ADMIN`

```http
PUT /api/einstein/users/permissions
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "role": "507f1f77bcf86cd799439016",
  "permission": "507f1f77bcf86cd799439019"
}
```

**Success Response (200):**
```json
{
  "id": "507f1f77bcf86cd799439013",
  "name": "project-manager",
  "description": "Can manage projects and team members",
  "permissions": ["507f1f77bcf86cd799439019", "507f1f77bcf86cd79943901c", "507f1f77bcf86cd79943901d"],
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-16T10:30:00Z"
}
```

**Error Response (400 - Role Not Found):**
```json
{
  "message": "Role not found",
  "code": "ROLE_NOT_FOUND",
  "statusCode": 400
}
```

**Error Response (400 - Permission Already Assigned):**
```json
{
  "message": "Role already has this permission",
  "code": "PERMISSION_ALREADY_ASSIGNED",
  "statusCode": 400
}
```

---

## Common Error Responses

### 401 Unauthorized (Missing/Invalid Token)
```json
{
  "message": "Invalid authentication token",
  "code": "UNAUTHORIZED",
  "statusCode": 401
}
```

### 403 Forbidden (Insufficient Permissions)
```json
{
  "message": "Unauthorized - Request Access from Digital Team",
  "statusCode": 401
}
```

### 400 Validation Error
```json
{
  "message": "Validation failed",
  "errors": [
    {
      "field": "azureAdObjectId",
      "message": "Azure AD Object ID is required"
    }
  ]
}
```

### 500 Internal Server Error
```json
{
  "message": "Internal server error",
  "statusCode": 500
}
```

---

## Required Roles Summary

| Endpoint | Method | Required Roles | Description |
|----------|--------|----------------|-------------|
| `/users` | GET | `read-users` OR `SYSTEM_ADMIN` | Get all users |
| `/users` | POST | `create-users` OR `SYSTEM_ADMIN` | Create new user |
| `/users/:azureAdObjectId` | GET | `read-users` OR `SYSTEM_ADMIN` | Get specific user |
| `/users/roles` | GET | No specific role | Get current user's roles |
| `/users/roles` | POST | `manage-roles` OR `SYSTEM_ADMIN` | Create new role |
| `/users/roles` | PUT | `manage-users` OR `SYSTEM_ADMIN` | Assign role to user |
| `/users/roles/all` | GET | `read-roles` OR `SYSTEM_ADMIN` | Get all available roles |
| `/users/permissions` | POST | `manage-permissions` OR `SYSTEM_ADMIN` | Create new permission |
| `/users/permissions` | PUT | `manage-roles` OR `SYSTEM_ADMIN` | Assign permission to role |

---

## Predefined System Roles

The system comes with these predefined roles:

### SYSTEM_ADMIN
- **Description:** Full system administrator with all privileges
- **Permissions:** All permissions including:
  - `manage-users`
  - `manage-roles`
  - `manage-permissions`
  - `einstein-admin`
  - `assign-roles`

### ADMIN
- **Description:** Full system administrator with all privileges
- **Permissions:** `einstein-admin`

### USER
- **Description:** Basic user with read access and feedback capabilities
- **Permissions:** Basic read permissions

---

## Frontend Implementation Notes

### 1. Authentication
- Store JWT token securely (preferably in httpOnly cookies or secure storage)
- Include Authorization header in all requests
- Handle token expiration gracefully

### 2. Error Handling
- Implement comprehensive error handling for all HTTP status codes
- Show user-friendly messages for permission errors
- Provide feedback for validation errors

### 3. Role-Based UI
- Hide/show UI elements based on user's current roles
- Disable actions that user doesn't have permission for
- Provide clear feedback when access is denied

### 4. Form Validation
- Implement client-side validation before sending requests
- Validate Azure AD Object ID format
- Ensure required fields are populated

### 5. User Experience
- Provide loading states for API calls
- Show success/error notifications
- Implement proper form reset after successful operations

### 6. Data Management
- Cache role and permission data appropriately
- Refresh user data after role assignments
- Handle concurrent modifications gracefully

---

## TypeScript Interfaces

For TypeScript projects, here are the recommended interfaces:

```typescript
interface User {
  id: string;
  azureAdObjectId: string;
  email?: string;
  name?: string;
  roles: string[];
  assignedBy?: string;
  createdAt: string;
  updatedAt: string;
}

interface CreateUserRequest {
  azureAdObjectId: string;
  roles?: string[];
  assignedBy?: string;
}

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  createdAt: string;
  updatedAt: string;
}

interface RoleWithPermissions {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
}

interface Permission {
  id: string;
  name: string;
  description: string;
  createdAt: string;
}

interface CreateRoleRequest {
  name: string;
  description: string;
}

interface CreatePermissionRequest {
  name: string;
  description: string;
}

interface AssignRoleRequest {
  azureAdObjectId: string;
  role: string;
}

interface AssignPermissionRequest {
  role: string;
  permission: string;
}

interface ApiError {
  message: string;
  code?: string;
  statusCode: number;
  errors?: ValidationError[];
}

interface ValidationError {
  field: string;
  message: string;
}
```

---

## Testing Examples

### Using cURL

```bash
# Get all users
curl -X GET "http://localhost:3000/api/einstein/users" \
  -H "Authorization: Bearer your_jwt_token_here"

# Create a new user
curl -X POST "http://localhost:3000/api/einstein/users" \
  -H "Authorization: Bearer your_jwt_token_here" \
  -H "Content-Type: application/json" \
  -d '{"azureAdObjectId": "<EMAIL>"}'

# Assign role to user
curl -X PUT "http://localhost:3000/api/einstein/users/roles" \
  -H "Authorization: Bearer your_jwt_token_here" \
  -H "Content-Type: application/json" \
  -d '{"azureAdObjectId": "<EMAIL>", "role": "role_id_here"}'
```

### Using JavaScript/Fetch

```javascript
// Helper function for API calls
async function apiCall(endpoint, options = {}) {
  const token = localStorage.getItem('jwt_token'); // or wherever you store the token
  
  const defaultOptions = {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      ...options.headers
    }
  };
  
  const response = await fetch(`/api/einstein/users${endpoint}`, {
    ...defaultOptions,
    ...options
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'API call failed');
  }
  
  return response.json();
}

// Examples
const users = await apiCall('');
const newUser = await apiCall('', {
  method: 'POST',
  body: JSON.stringify({ azureAdObjectId: '<EMAIL>' })
});
const roles = await apiCall('/roles/all');
```

This documentation should provide everything needed to build a comprehensive user management interface! 🚀 