# HowTo API Documentation

## Overview

The HowTo API provides comprehensive functionality for managing HowTo guides, including CRUD operations, file management, search and filtering, and taxonomy management. The API is fully documented with OpenAPI/Swagger specifications.

## Base URL

```
http://localhost:{PORT}/api/einstein/how-to
```

## Swagger Documentation

Access the interactive Swagger UI documentation at:
```
http://localhost:{PORT}/api-docs
```

## API Endpoints Summary

### 🔧 **HowTo Guides Management**

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/` | Get all HowTo guides (paginated) |
| `GET` | `/{id}` | Get specific HowTo guide by ID |
| `POST` | `/` | Create new HowTo guide |
| `PUT` | `/{id}` | Update existing HowTo guide |
| `DELETE` | `/{id}` | Delete HowTo guide |
| `POST` | `/filter/` | Create HowTo template |

### 🔍 **Search & Filtering**

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/filter/search` | Search HowTo guides by term |
| `GET` | `/filter/filtered` | Filter by taxonomy (sector/subsector/buildType) |
| `GET` | `/filter/filtered/tree` | Get filtered results in tree structure |
| `GET` | `/filter/by-parent` | Get HowTo guides by parent ID |
| `GET` | `/filter/recent` | Get recently created HowTo guides |

### 🏷️ **Taxonomy Management**

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/taxonomy/sectors` | Get available sectors |
| `GET` | `/taxonomy/subsectors` | Get subsectors for a sector |
| `GET` | `/taxonomy/build-types` | Get build types for sector/subsector |

### 📁 **File Management**

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/upload/presigned-url` | Generate presigned URL for single file upload |
| `POST` | `/upload/batch-presigned-urls` | Generate presigned URLs for batch upload |
| `DELETE` | `/files/delete` | Delete single file from Azure Blob Storage |
| `DELETE` | `/files/delete-batch` | Delete multiple files from Azure Blob Storage |
| `GET` | `/files/exists` | Check if file exists in Azure Blob Storage |
| `GET` | `/files/sas-url` | Generate SAS URL for secure file access |
| `POST` | `/upload-image` | **[DEPRECATED]** Direct image upload |

## Data Models

### HowToGuide Schema

```json
{
  "_id": "string",
  "title": "string (required)",
  "description": "string",
  "overview": "string",
  "taxonomy": {
    "sector": {
      "id": "string",
      "label": "string",
      "description": "string"
    },
    "subsector": {
      "id": "string",
      "label": "string", 
      "description": "string"
    },
    "buildType": {
      "id": "string",
      "label": "string",
      "description": "string"
    },
    "hierarchyPath": ["string"]
  },
  "content": {
    "attachedFiles": [
      {
        "url": "string (uri)",
        "label": "string"
      }
    ]
  },
  "metadata": {
    "order": "number",
    "parentId": "string",
    "createdAt": "string (date-time)",
    "updatedAt": "string (date-time)"
  }
}
```

## Example Usage

### Create a New HowTo Guide

```bash
curl -X POST "http://localhost:3000/api/einstein/how-to" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "How to Setup Development Environment",
    "description": "A comprehensive guide to setting up your development environment",
    "taxonomy": {
      "sector": {
        "id": "tech",
        "label": "Technology"
      },
      "subsector": {
        "id": "development",
        "label": "Development"
      }
    },
    "metadata": {
      "order": 1
    }
  }'
```

### Search HowTo Guides

```bash
curl -X GET "http://localhost:3000/api/einstein/how-to/filter/search?searchTerm=development&page=1&limit=10"
```

### Generate Presigned URL for File Upload

```bash
curl -X POST "http://localhost:3000/api/einstein/how-to/upload/presigned-url" \
  -H "Content-Type: application/json" \
  -d '{
    "fileName": "guide-document.pdf",
    "fileType": "application/pdf",
    "expiryMinutes": 60
  }'
```

### Filter by Taxonomy

```bash
curl -X GET "http://localhost:3000/api/einstein/how-to/filter/filtered?sectorId=tech&subsectorId=development&page=1&limit=20"
```

## Response Format

### Success Response
```json
{
  "data": {
    // Response data here
  }
}
```

### Paginated Response
```json
{
  "data": {
    "documents": [
      // Array of HowTo guides
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "pages": 5
    }
  }
}
```

### Error Response
```json
{
  "error": "Error message",
  "details": [
    // Validation error details (if applicable)
  ]
}
```

## Status Codes

| Code | Description |
|------|-------------|
| `200` | Success |
| `201` | Created |
| `400` | Bad Request (validation error) |
| `404` | Not Found |
| `500` | Internal Server Error |

## File Upload Workflow

### Recommended Approach (Presigned URLs)

1. **Generate Presigned URL**: Call `/upload/presigned-url` to get upload URL
2. **Upload File**: Use the presigned URL to upload file directly to Azure Blob Storage
3. **Create/Update HowTo**: Include the file URL in the `attachedFiles` array

### Example Workflow

```javascript
// Step 1: Generate presigned URL
const presignedResponse = await fetch('/api/einstein/how-to/upload/presigned-url', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    fileName: 'document.pdf',
    fileType: 'application/pdf'
  })
});
const { uploadUrl, blobUrl } = await presignedResponse.json();

// Step 2: Upload file to Azure
await fetch(uploadUrl, {
  method: 'PUT',
  body: fileBlob,
  headers: { 'x-ms-blob-type': 'BlockBlob' }
});

// Step 3: Create HowTo with file reference
await fetch('/api/einstein/how-to', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    title: 'My Guide',
    attachedFiles: [{
      url: blobUrl,
      label: 'Supporting Document'
    }]
  })
});
```

## Authentication

Currently, the API endpoints don't require authentication in the examples above. If your application uses authentication, include the appropriate headers:

```bash
curl -X GET "http://localhost:3000/api/einstein/how-to" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Rate Limiting

The API may implement rate limiting. Check your application's rate limiting configuration for specific limits.

## Environment Variables

Ensure the following environment variables are set:

- `AZURE_STORAGE_CONNECTION_STRING`: Azure Blob Storage connection string
- `AZURE_STORAGE_ACCOUNT_NAME`: Azure Storage account name (optional if using connection string)
- `AZURE_STORAGE_ACCOUNT_KEY`: Azure Storage account key (optional if using connection string)

## Testing

The API includes comprehensive test coverage:

- **Unit Tests**: 45 tests covering controllers and services
- **Integration Tests**: 29 tests covering all endpoints
- **Total Coverage**: 74 tests with 100% pass rate

Run tests with:
```bash
npm test
```

## Support

For API support and questions, refer to the Swagger documentation at `/api-docs` or contact the development team.