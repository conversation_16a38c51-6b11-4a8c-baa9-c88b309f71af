---
description: 
globs: 
alwaysApply: true
---
# Design Patterns and Best Practices

This project follows established design patterns and best practices to ensure code quality, maintainability, and testability.

## Repository Pattern

The Repository pattern is implemented to abstract data access logic:

- Repository interfaces define contracts ([file.repository.ts](mdc:src/services/file/file.repository.ts))
- Concrete implementations provide database-specific logic ([mongoFile.repository.ts](mdc:src/services/file/mongoFile.repository.ts))
- Services depend on the interface, not the implementation

## Dependency Injection

Constructor-based dependency injection is used to:

- Make dependencies explicit
- Facilitate unit testing through mocking
- Reduce tight coupling between components

Example from [file.service.ts](mdc:src/services/file/file.service.ts):
```typescript
constructor(
  private fileRepo: MongoFileRepository,
  private azureBlobService: AzureBlobService
) {}
```

## Service Layer Pattern

Services encapsulate business logic:
- Pure business logic in service classes
- Services use repositories for data access
- External concerns (like HTTP) are handled in controllers

## Controller Pattern

Controllers handle HTTP-specific concerns:
- Request validation and parsing
- Response formatting
- Error handling
- No business logic (delegated to services)

## Error Handling

Consistent error handling approach:
- Domain-specific exceptions
- HTTP exceptions with status codes
- Appropriate error messages
- Logging for traceability

## Type Safety

Strong typing with TypeScript:
- Interfaces define contracts
- Models define data structures
- Generic types for reusable patterns
- No implicit 'any' types

## Domain-Driven Design

DDD principles applied:
- Bounded contexts through service organization
- Rich domain models with behavior
- Value objects when appropriate
- Clear domain entity definitions

