import {
	getModelForClass,
	modelOptions,
	prop,
	Severity,
} from "@typegoose/typegoose";

@modelOptions({
	options: {
		allowMixed: Severity.ALLOW,
	},
})
export class UniqueItem {
	@prop({ type: () => String })
	public item_name?: string;

	@prop({ type: () => Date })
	public planned_start?: Date;

	@prop({ type: () => Date })
	public planned_finish?: Date;

	@prop({ type: () => Date })
	public actual_start?: Date;

	@prop({ type: () => Date })
	public actual_finish?: Date;

	@prop({ type: () => String })
	public item_value?: string | number;
}

export class StageGroup {
	@prop()
	public planned_start_date?: Date;

	@prop()
	public planned_finish_date?: Date;

	@prop()
	public actual_start_date?: Date;

	@prop()
	public actual_finish_date?: Date;

	@prop()
	public planned_duration?: number;

	@prop()
	public actual_duration?: number;
}

export class TransitionGroup {
	@prop()
	public planned_gap?: number;

	@prop()
	public actual_gap?: number;

	@prop()
	public from_group?: string;

	@prop()
	public to_group?: string;
}

@modelOptions({
	schemaOptions: {
		timestamps: true,
		collection: "accumulated_duration_data",
	},
	options: {
		allowMixed: Severity.ALLOW,
	},
})
export class AccumulatedDuration {
	@prop({ required: true, type: () => String })
	public project_code!: string;

	@prop({ type: () => String })
	public sub_project_name?: string;

	@prop({ type: () => String })
	public project_sheet_name?: string;

	@prop({ type: () => String })
	public type?: string;

	@prop({ type: () => [UniqueItem] })
	public unique_items?: UniqueItem[];

	@prop({ type: () => Number })
	public total_items?: number;

	@prop({ type: () => Date })
	public migration_timestamp?: Date;

	@prop({ type: () => String })
	public migration_version?: string;

	// Derived Data containing stages, stage_groups, and transition_groups
	@prop({ type: () => Object })
	public "Derived Data"?: {
		stages?: Record<string, any>;
		stage_groups?: {
			Design?: StageGroup;
			Procurement?: StageGroup;
			Construction?: StageGroup;
			[key: string]: StageGroup | undefined;
		};
		transition_groups?: {
			Design_Procurement?: TransitionGroup;
			Procurement_Construction?: TransitionGroup;
			[key: string]: TransitionGroup | undefined;
		};
		stages_processed_at?: Date;
		transitions_processed_at?: Date;
	};

	@prop({ type: () => Date })
	public project_fields_processed_at?: Date;

	@prop({ type: () => Date })
	public cleanup_timestamp?: Date;

	@prop({ type: () => String })
	public cleanup_version?: string;

	@prop({ type: () => Object })
	public contract_type?: Record<string, any>;

	@prop({ type: () => String })
	public profit_centre?: string;

	@prop({ type: () => String })
	public project_city?: string;

	@prop({ type: () => Object })
	public total_project_value?: Record<string, any>;
}

export const AccumulatedDurationModel = getModelForClass(AccumulatedDuration);
