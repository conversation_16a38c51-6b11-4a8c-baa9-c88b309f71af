import type { Request, Response, NextFunction } from "express";
import type { ProductivityService } from "@/services/productivity/productivity.service";
import type {
	GetStagesByPhaseRequest,
	GetCategoryTypesByPhaseAndStageRequest,
	GetDataByPhaseStageAndCategoryTypeRequest,
} from "@/services/productivity/productivity.types";

export class ProductivityController {
	private productivityService: ProductivityService;

	constructor(productivityService: ProductivityService) {
		this.productivityService = productivityService;
	}

	public async getByPhaseAndStage(
		req: Request,
		res: Response,
		next: NextFunction,
	) {
		const { phase, stage } = req.query;
		if (typeof phase !== "string" && typeof stage !== "string") {
			res.status(400).json({
				error:
					"At least one of 'phase' or 'stage' query parameter must be provided.",
			});
			return;
		}
		try {
			const filter: Record<string, string> = {};
			if (typeof phase === "string") filter.phase = phase;
			if (typeof stage === "string") filter.stage = stage;
			const data = await this.productivityService.getByPhaseAndStage(filter);
			res.status(200).json(data);
		} catch (error) {
			next(error);
		}
	}

	public async getPhases(_req: Request, res: Response, next: NextFunction) {
		try {
			const phases = await this.productivityService.getPhases();
			res.status(200).json({ data: phases });
		} catch (error) {
			next(error);
		}
	}

	public async getStagesByPhase(
		req: Request<unknown, any, GetStagesByPhaseRequest>,
		res: Response,
		next: NextFunction,
	) {
		const { phase } = req.body;
		if (!phase) {
			res.status(400).json({ error: "Phase is required in request body." });
			return;
		}
		try {
			const stages = await this.productivityService.getStagesByPhase(phase);
			res.status(200).json({ data: stages });
		} catch (error) {
			next(error);
		}
	}

	public async getCategoryTypesByPhaseAndStage(
		req: Request<unknown, any, GetCategoryTypesByPhaseAndStageRequest>,
		res: Response,
		next: NextFunction,
	) {
		const { phase, stage } = req.body;
		if (!phase || !stage) {
			res
				.status(400)
				.json({ error: "Both phase and stage are required in request body." });
			return;
		}
		try {
			const categoryTypes =
				await this.productivityService.getCategoryTypesByPhaseAndStage(
					phase,
					stage,
				);
			res.status(200).json({ data: categoryTypes });
		} catch (error) {
			next(error);
		}
	}

	public async getDataByPhaseStageAndCategoryType(
		req: Request<unknown, any, GetDataByPhaseStageAndCategoryTypeRequest>,
		res: Response,
		next: NextFunction,
	) {
		const { phase, stage, categoryType } = req.body;
		if (!phase || !stage || !categoryType) {
			res.status(400).json({
				error: "Phase, stage, and categoryType are required in request body.",
			});
			return;
		}
		try {
			const data =
				await this.productivityService.getDataByPhaseStageAndCategoryType(
					phase,
					stage,
					categoryType,
				);
			res.status(200).json({ data });
		} catch (error) {
			next(error);
		}
	}
}
