generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model Estimate {
  project_code           String?  @db.VarChar(50)
  item_number            String?  @db.VarChar(50)
  item_text              String?
  description            String?
  unit                   String?  @db.VarChar(50)
  quantity               Float?
  categories_labour      Float?
  categories_material    Float?
  categories_plant       Float?
  categories_subcontract Float?
  categories_other       Float?
  rate                   Float?
  amount                 Float?
  icms_code              String?  @db.VarChar(50)
  is_header              Boolean?
  level                  String?  @db.VarChar(50)
  source                 String?  @db.VarChar(100)
  id                     Int      @id @default(autoincrement())

  @@map("estimates")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model consolidated {
  project_code String? @db.VarChar(50)
  code         String? @db.VarChar(50)
  description  String?
  grouping     String? @db.VarChar(255)
  quantity     Float?
  uom          String? @db.VarChar(50)
  rate         Float?
  subtotal     Float?
  factor       Float?
  id           Int     @id @default(autoincrement())

  @@map("consolidated")
}

model geodata {
  id        Int                      @id @default(autoincrement())
  projectid String?                  @unique @db.VarChar(50)
  latlng    Unsupported("geometry")?
  projects  projects?                @relation(fields: [projectid], references: [projectid], onDelete: NoAction, onUpdate: NoAction)
}

model projects {
  projectid                                      String                    @id @db.VarChar(50)
  company                                        String?                   @db.VarChar(100)
  profitcentre                                   String?                   @db.VarChar(100)
  location                                       String?                   @db.VarChar(100)
  projectname                                    String                    @db.VarChar(200)
  projectdirector                                String?                   @db.VarChar(100)
  projectmanager                                 String?                   @db.VarChar(100)
  estconsfees                                    Decimal?                  @db.Decimal(15, 2)
  totalrevenues                                  Decimal?                  @db.Decimal(15, 2)
  customername                                   String?                   @db.VarChar(200)
  contactperson                                  String?                   @db.VarChar(100)
  servicename                                    String?                   @db.VarChar(100)
  primarymarket                                  String?                   @db.VarChar(100)
  clienttype                                     String?                   @db.VarChar(50)
  projecttype                                    String?                   @db.VarChar(50)
  activedate                                     DateTime?                 @db.Date
  estprojectvalue                                Decimal?                  @db.Decimal(15, 2)
  salescurrency                                  String?                   @db.VarChar(3)
  region                                         String?                   @db.VarChar(100)
  startdate                                      DateTime?                 @db.Date
  enddate                                        DateTime?                 @db.Date
  latlng                                         Unsupported("geography")?
  projectaddress                                 String?                   @db.VarChar(200)
  designasplannedstartdate                       DateTime?                 @db.Date
  designasplannedenddate                         DateTime?                 @db.Date
  designasplannedduration                        Decimal?                  @db.Decimal(5, 1)
  designasbuiltstartdate                         DateTime?                 @db.Date
  designasbuiltenddate                           DateTime?                 @db.Date
  designasbuiltduration                          Decimal?                  @db.Decimal(5, 1)
  planningapprovalasplannedstartdate             DateTime?                 @db.Date
  planningapprovalasplannedenddate               DateTime?                 @db.Date
  planningapprovalasplannedduration              Decimal?                  @db.Decimal(5, 1)
  planningapprovalasbuiltstartdate               DateTime?                 @db.Date
  planningapprovalasbuiltenddate                 DateTime?                 @db.Date
  planningapprovalasbuiltduration                Decimal?                  @db.Decimal(5, 1)
  procurementasplannedstartdate                  DateTime?                 @db.Date
  procurementasplannedenddate                    DateTime?                 @db.Date
  procurementasplannedduration                   Decimal?                  @db.Decimal(5, 1)
  procurementasbuiltstartdate                    DateTime?                 @db.Date
  procurementasbuiltenddate                      DateTime?                 @db.Date
  procurementasbuiltduration                     Decimal?                  @db.Decimal(5, 1)
  constructionandcommissioningasplannedstartdate DateTime?                 @db.Date
  constructionandcommissioningasplannedenddate   DateTime?                 @db.Date
  constructionandcommissioningasplannedduration  Decimal?                  @db.Decimal(5, 1)
  constructionandcommissioningasbuiltstartdate   DateTime?                 @db.Date
  constructionandcommissioningasbuiltenddate     DateTime?                 @db.Date
  constructionandcommissioningasbuiltduration    Decimal?                  @db.Decimal(5, 1)
  gfa                                            Int?
  numberoffloors                                 Int?
  totalcostasplanned                             Decimal?                  @db.Decimal(15, 2)
  designasplannedcost                            Decimal?                  @db.Decimal(15, 2)
  planningasplannedcost                          Decimal?                  @db.Decimal(15, 2)
  procurementasplannedcost                       Decimal?                  @db.Decimal(15, 2)
  constructionasplannedcost                      Decimal?                  @db.Decimal(15, 2)
  totalcostasbuilt                               Decimal?                  @db.Decimal(15, 2)
  designasbuiltcost                              Decimal?                  @db.Decimal(15, 2)
  planningasbuiltcost                            Decimal?                  @db.Decimal(15, 2)
  procurementasbuiltcost                         Decimal?                  @db.Decimal(15, 2)
  constructionasbuiltcost                        Decimal?                  @db.Decimal(15, 2)
  geodata                                        geodata?
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model spatial_ref_sys {
  srid      Int     @id
  auth_name String? @db.VarChar(256)
  auth_srid Int?
  srtext    String? @db.VarChar(2048)
  proj4text String? @db.VarChar(2048)
}

model master_consolidated_codes {
  master_code        String  @id @db.VarChar(50)
  master_description String?
  master_grouping    String? @db.VarChar(255)
}
