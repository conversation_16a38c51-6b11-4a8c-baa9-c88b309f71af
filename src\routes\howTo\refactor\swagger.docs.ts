/**
 * @swagger
 * tags:
 *   - name: HowTo Guides
 *     description: HowTo guide management operations
 *   - name: File Management
 *     description: File upload and management operations
 *   - name: Search & Filter
 *     description: Search and filtering operations
 *   - name: Taxonomy
 *     description: Taxonomy management operations
 */

/**
 * @swagger
 * /api/einstein/how-to:
 *   get:
 *     summary: Get all HowTo guides
 *     description: Retrieve a paginated list of all HowTo guides
 *     tags: [HowTo Guides]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Successfully retrieved HowTo guides
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/PaginationResult'
 *             example:
 *               data:
 *                 documents:
 *                   - _id: "507f1f77bcf86cd799439011"
 *                     title: "How to Setup Development Environment"
 *                     description: "A comprehensive guide to setting up your development environment"
 *                     taxonomy:
 *                       sector:
 *                         id: "tech"
 *                         label: "Technology"
 *                 pagination:
 *                   page: 1
 *                   limit: 20
 *                   total: 50
 *                   pages: 3
 *       500:
 *         description: Server error
 *   post:
 *     summary: Create a new HowTo guide
 *     description: Create a new HowTo guide with optional file attachments
 *     tags: [HowTo Guides]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - taxonomy
 *             properties:
 *               title:
 *                 type: string
 *                 minLength: 1
 *               description:
 *                 type: string
 *               overview:
 *                 type: string
 *               taxonomy:
 *                 type: object
 *                 properties:
 *                   sector:
 *                     $ref: '#/components/schemas/TaxonomyItem'
 *                   subsector:
 *                     $ref: '#/components/schemas/TaxonomyItem'
 *                   buildType:
 *                     $ref: '#/components/schemas/TaxonomyItem'
 *               metadata:
 *                 type: object
 *                 properties:
 *                   order:
 *                     type: number
 *                   parentId:
 *                     type: string
 *               attachedFiles:
 *                 type: array
 *                 maxItems: 20
 *                 items:
 *                   $ref: '#/components/schemas/AttachedFile'
 *           example:
 *             title: "How to Deploy Applications"
 *             description: "Step-by-step guide for application deployment"
 *             taxonomy:
 *               sector:
 *                 id: "devops"
 *                 label: "DevOps"
 *               subsector:
 *                 id: "deployment"
 *                 label: "Deployment"
 *             metadata:
 *               order: 1
 *     responses:
 *       201:
 *         description: HowTo guide created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/HowToGuide'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/einstein/how-to/{id}:
 *   get:
 *     summary: Get HowTo guide by ID
 *     description: Retrieve a specific HowTo guide by its ID
 *     tags: [HowTo Guides]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: HowTo guide ID
 *     responses:
 *       200:
 *         description: HowTo guide retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/HowToGuide'
 *       400:
 *         description: Invalid ID format
 *       404:
 *         description: HowTo guide not found
 *       500:
 *         description: Server error
 *   put:
 *     summary: Update HowTo guide
 *     description: Update an existing HowTo guide
 *     tags: [HowTo Guides]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: HowTo guide ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               overview:
 *                 type: string
 *               taxonomy:
 *                 type: object
 *                 properties:
 *                   sector:
 *                     $ref: '#/components/schemas/TaxonomyItem'
 *                   subsector:
 *                     $ref: '#/components/schemas/TaxonomyItem'
 *                   buildType:
 *                     $ref: '#/components/schemas/TaxonomyItem'
 *               metadata:
 *                 type: object
 *                 properties:
 *                   order:
 *                     type: number
 *                   parentId:
 *                     type: string
 *               attachedFiles:
 *                 type: array
 *                 maxItems: 20
 *                 items:
 *                   $ref: '#/components/schemas/AttachedFile'
 *           example:
 *             title: "Updated: How to Deploy Applications"
 *             description: "Updated step-by-step guide for application deployment"
 *     responses:
 *       200:
 *         description: HowTo guide updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/HowToGuide'
 *       400:
 *         description: Validation error
 *       404:
 *         description: HowTo guide not found
 *       500:
 *         description: Server error
 *   delete:
 *     summary: Delete HowTo guide
 *     description: Delete a HowTo guide by its ID
 *     tags: [HowTo Guides]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: HowTo guide ID
 *     responses:
 *       200:
 *         description: HowTo guide deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "HowTo deleted successfully"
 *       400:
 *         description: Invalid ID format
 *       404:
 *         description: HowTo guide not found
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/einstein/how-to/filter/search:
 *   get:
 *     summary: Search HowTo guides
 *     description: Search HowTo guides by search term with pagination
 *     tags: [Search & Filter]
 *     parameters:
 *       - in: query
 *         name: searchTerm
 *         required: true
 *         schema:
 *           type: string
 *           minLength: 1
 *         description: Search term to find in titles and descriptions
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Search results retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/PaginationResult'
 *       400:
 *         description: Validation error (missing search term)
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/einstein/how-to/filter/filtered:
 *   get:
 *     summary: Filter HowTo guides by taxonomy
 *     description: Filter HowTo guides by sector, subsector, and build type
 *     tags: [Search & Filter]
 *     parameters:
 *       - in: query
 *         name: sectorId
 *         schema:
 *           type: string
 *         description: Filter by sector ID
 *       - in: query
 *         name: subsectorId
 *         schema:
 *           type: string
 *         description: Filter by subsector ID
 *       - in: query
 *         name: buildTypeId
 *         schema:
 *           type: string
 *         description: Filter by build type ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Filtered results retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/PaginationResult'
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/einstein/how-to/filter/filtered/tree:
 *   get:
 *     summary: Get filtered HowTo guides in tree structure
 *     description: Get filtered HowTo guides organized in a hierarchical tree structure
 *     tags: [Search & Filter]
 *     parameters:
 *       - in: query
 *         name: sectorId
 *         schema:
 *           type: string
 *         description: Filter by sector ID
 *       - in: query
 *         name: subsectorId
 *         schema:
 *           type: string
 *         description: Filter by subsector ID
 *       - in: query
 *         name: buildTypeId
 *         schema:
 *           type: string
 *         description: Filter by build type ID
 *     responses:
 *       200:
 *         description: Tree structure retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                       title:
 *                         type: string
 *                       order:
 *                         type: number
 *                       children:
 *                         type: array
 *                         items:
 *                           type: object
 *                 pagination:
 *                   type: object
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/einstein/how-to/filter/by-parent:
 *   get:
 *     summary: Get HowTo guides by parent ID
 *     description: Retrieve HowTo guides that have a specific parent ID
 *     tags: [Search & Filter]
 *     parameters:
 *       - in: query
 *         name: parentId
 *         schema:
 *           type: string
 *         description: Parent ID to filter by (omit for root level items)
 *     responses:
 *       200:
 *         description: Child HowTo guides retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/HowToGuide'
 *       400:
 *         description: Invalid parent ID format
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/einstein/how-to/filter/recent:
 *   get:
 *     summary: Get recent HowTo guides
 *     description: Retrieve the most recently created HowTo guides
 *     tags: [Search & Filter]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 10
 *         description: Number of recent items to retrieve
 *     responses:
 *       200:
 *         description: Recent HowTo guides retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/HowToGuide'
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/einstein/how-to/taxonomy/sectors:
 *   get:
 *     summary: Get available sectors
 *     description: Retrieve all available sectors for taxonomy filtering
 *     tags: [Taxonomy]
 *     responses:
 *       200:
 *         description: Sectors retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/TaxonomyItem'
 *             example:
 *               data:
 *                 - id: "tech"
 *                   label: "Technology"
 *                   description: "Technology related guides"
 *                 - id: "business"
 *                   label: "Business"
 *                   description: "Business process guides"
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/einstein/how-to/taxonomy/subsectors:
 *   get:
 *     summary: Get available subsectors
 *     description: Retrieve subsectors for a specific sector
 *     tags: [Taxonomy]
 *     parameters:
 *       - in: query
 *         name: sectorId
 *         required: true
 *         schema:
 *           type: string
 *         description: Sector ID to get subsectors for
 *     responses:
 *       200:
 *         description: Subsectors retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/TaxonomyItem'
 *       400:
 *         description: Sector ID is required
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/einstein/how-to/taxonomy/build-types:
 *   get:
 *     summary: Get available build types
 *     description: Retrieve build types for a specific sector and subsector
 *     tags: [Taxonomy]
 *     parameters:
 *       - in: query
 *         name: sectorId
 *         required: true
 *         schema:
 *           type: string
 *         description: Sector ID
 *       - in: query
 *         name: subsectorId
 *         required: true
 *         schema:
 *           type: string
 *         description: Subsector ID
 *     responses:
 *       200:
 *         description: Build types retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/TaxonomyItem'
 *       400:
 *         description: Sector ID and Subsector ID are required
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/einstein/how-to/files/delete-batch:
 *   delete:
 *     summary: Delete multiple files from Azure Blob Storage
 *     description: Delete multiple files from Azure Blob Storage using their URLs
 *     tags: [File Management]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - urls
 *             properties:
 *               urls:
 *                 type: array
 *                 minItems: 1
 *                 maxItems: 50
 *                 items:
 *                   type: string
 *                   format: uri
 *           example:
 *             urls:
 *               - "https://storage.blob.core.windows.net/container/file1.pdf"
 *               - "https://storage.blob.core.windows.net/container/file2.jpg"
 *     responses:
 *       200:
 *         description: Batch delete completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     results:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           url:
 *                             type: string
 *                           success:
 *                             type: boolean
 *                           error:
 *                             type: string
 *                     summary:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: number
 *                         successful:
 *                           type: number
 *                         failed:
 *                           type: number
 *       400:
 *         description: Validation error
 */

/**
 * @swagger
 * /api/einstein/how-to/files/exists:
 *   get:
 *     summary: Check if file exists in Azure Blob Storage
 *     description: Check if a file exists in Azure Blob Storage using its URL
 *     tags: [File Management]
 *     parameters:
 *       - in: query
 *         name: url
 *         required: true
 *         schema:
 *           type: string
 *           format: uri
 *         description: File URL to check
 *     responses:
 *       200:
 *         description: File existence check completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     url:
 *                       type: string
 *                     exists:
 *                       type: boolean
 *       400:
 *         description: URL parameter is required or invalid
 */

/**
 * @swagger
 * /api/einstein/how-to/files/sas-url:
 *   get:
 *     summary: Generate SAS URL for file access
 *     description: Generate a SAS (Shared Access Signature) URL for secure file access
 *     tags: [File Management]
 *     parameters:
 *       - in: query
 *         name: url
 *         required: true
 *         schema:
 *           type: string
 *           format: uri
 *         description: Original file URL
 *       - in: query
 *         name: expiryMinutes
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 1440
 *           default: 60
 *         description: SAS token expiry time in minutes
 *     responses:
 *       200:
 *         description: SAS URL generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     sasUrl:
 *                       type: string
 *                       format: uri
 *       400:
 *         description: Invalid URL format or validation error
 *       500:
 *         description: Server configuration error or unexpected error
 */

/**
 * @swagger
 * /api/einstein/how-to/filter/:
 *   post:
 *     summary: Create HowTo template
 *     description: Create a new HowTo template with specified taxonomy
 *     tags: [HowTo Guides]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - sector
 *               - subsector
 *               - buildType
 *             properties:
 *               title:
 *                 type: string
 *                 minLength: 1
 *               sector:
 *                 $ref: '#/components/schemas/TaxonomyItem'
 *               subsector:
 *                 $ref: '#/components/schemas/TaxonomyItem'
 *               buildType:
 *                 $ref: '#/components/schemas/TaxonomyItem'
 *           example:
 *             title: "Development Template"
 *             sector:
 *               id: "tech"
 *               label: "Technology"
 *               description: "Technology sector"
 *             subsector:
 *               id: "dev"
 *               label: "Development"
 *               description: "Software development"
 *             buildType:
 *               id: "web"
 *               label: "Web Application"
 *               description: "Web application development"
 *     responses:
 *       201:
 *         description: HowTo template created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/HowToGuide'
 *       400:
 *         description: Validation error
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/einstein/how-to/upload-image:
 *   post:
 *     summary: Upload image to Azure Blob Storage (Legacy)
 *     description: Upload an image file directly to Azure Blob Storage (deprecated - use presigned URLs instead)
 *     tags: [File Management]
 *     deprecated: true
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: Image file to upload
 *     responses:
 *       200:
 *         description: File uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     url:
 *                       type: string
 *                       format: uri
 *       400:
 *         description: No file uploaded
 *       500:
 *         description: Server error
 */
