import { z } from 'zod';

export const createUserSchema = z.object({
  azureAdObjectId: z.string().min(1, 'Azure AD Object ID is required'),
  roles: z.array(z.string()).optional(), // Array of role ObjectIds
});

// Response schema for type safety
export const rolePopulatedSchema = z.object({
  _id: z.string(),
  name: z.string(),
  description: z.string(),
  permissions: z.array(z.string()).optional(),
});

export const userResponseSchema = z.object({
  id: z.string().optional(),
  azureAdObjectId: z.string(),
  roles: z.array(z.string()).optional(),
  assignedBy: z.string().optional(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

export const userResponseWithPopulatedRolesSchema = z.object({
  id: z.string().optional(),
  azureAdObjectId: z.string(),
  roles: z.array(rolePopulatedSchema).optional(),
  assignedBy: z.string().optional(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

export const paginationQuerySchema = z.object({
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(10),
  sortBy: z.enum(['createdAt', 'updatedAt', 'azureAdObjectId']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  search: z.string().optional(),
  role: z.string().optional(),
});

export const paginationMetaSchema = z.object({
  currentPage: z.number(),
  totalPages: z.number(),
  totalCount: z.number(),
  limit: z.number(),
  hasNextPage: z.boolean(),
  hasPreviousPage: z.boolean(),
});

export const paginatedUsersResponseSchema = z.object({
  users: z.array(userResponseWithPopulatedRolesSchema),
  pagination: paginationMetaSchema,
});

export const createPermissionSchema = z.object({
  name: z.string().min(1, 'Permission name is required'),
  description: z.string().min(1, 'Description is required'),
});

export const assignPermissionSchema = z.object({
  role: z.string().min(1, 'Role is required'),
  permission: z.string().min(1, 'Permission is required'),
});

export const getRolesSchema = z.object({
  // azureAdObjectId: z.string().min(1, 'Azure AD Object ID is required'),
});

export const standardResponseSchema = z.object({
  message: z.record(z.string(), z.string()),
});

export const responseMessageSchema = z.object({
  message: z.string(),
});

export const assignRoleSchema = z.object({
  azureAdObjectId: z.string().min(1, 'Azure AD Object ID is required'),
  role: z.string().min(1, 'Role is required'),
});

export const updateUserSchema = z.object({
  roles: z.array(z.string().min(1, 'Role ID cannot be empty')).min(1, 'At least one role is required'),
  assignedBy: z.string().optional(),
});

export const roleResponseSchema = z.object({
  name: z.string(),
});

export const createRoleSchema = z.object({
  name: z.string().min(1, 'Role name is required'),
  description: z.string().min(1, 'Description is required'),
});

// Type inference
export type CreateUserInput = z.infer<typeof createUserSchema>;
export type UserResponse = z.infer<typeof userResponseSchema>;
export type ResponseMessage = z.infer<typeof responseMessageSchema>;
export type AssignRoleInput = z.infer<typeof assignRoleSchema>;
export type CreateRoleInput = z.infer<typeof createRoleSchema>;
export type RoleResponse = z.infer<typeof roleResponseSchema>;
export type StandardResponse = z.infer<typeof standardResponseSchema>;
export type CreatePermissionInput = z.infer<typeof createPermissionSchema>;
export type AssignPermissionInput = z.infer<typeof assignPermissionSchema>;
export type GetRolesInput = z.infer<typeof getRolesSchema>;
export type PaginationQuery = z.infer<typeof paginationQuerySchema>;
export type PaginationMeta = z.infer<typeof paginationMetaSchema>;
export type PaginatedUsersResponse = z.infer<typeof paginatedUsersResponseSchema>;
export type RolePopulated = z.infer<typeof rolePopulatedSchema>;
export type UserResponseWithPopulatedRoles = z.infer<typeof userResponseWithPopulatedRolesSchema>;
export type UpdateUserInput = z.infer<typeof updateUserSchema>;
