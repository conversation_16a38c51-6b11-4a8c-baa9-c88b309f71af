import { Request, RequestHandler, Response } from 'express';
import {
  getAllSectors,
  getSubSectorsForSector,
  getConstructionCostSources,
  getLandTypes,
  getLevelOfEstimate,
  getProcurementModels,
  getSectorSpecificQuestions,
  getSubSectorSpecificQuestions,
  getProjectDetails,
  getSuggestedProjectIds,
  getSuggestedInputProjectsIds,
  getAvailableSectors,
} from '@/services/abacus/referenceList.service';

const getAllSectorsController = async (req: Request, res: Response) => {
  try {
    const result = await getAllSectors();
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch sectors', error });
  }
};

const getAllSubSectorsController: RequestHandler = async (req: Request, res: Response) => {
  try {
    const sector = req.params.sector;
    if (!sector) {
      res.status(400).json({ message: 'Sector is required' });
    }
    const result = await getSubSectorsForSector(sector);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch sub sectors', error });
  }
};

const getConstructionCostSourcesController: RequestHandler = async (
  req: Request,
  res: Response
) => {
  try {
    const result = await getConstructionCostSources();
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch construction cost sources', error });
  }
};

const getProcurementModelsController: RequestHandler = async (req: Request, res: Response) => {
  try {
    const result = await getProcurementModels();
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch procurement models', error });
  }
};

const getLevelOfEstimateController: RequestHandler = async (req: Request, res: Response) => {
  try {
    const result = await getLevelOfEstimate();
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch level of estimate', error });
  }
};

const getLandTypesController: RequestHandler = async (req: Request, res: Response) => {
  try {
    const result = await getLandTypes();
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch land types', error });
  }
};

const getSectorSpecificQuestionsController: RequestHandler = async (
  req: Request,
  res: Response
) => {
  try {
    const sectorCode = req.params.sectorCode;
    if (!sectorCode) {
      res.status(400).json({ message: 'Sector code is required' });
    }
    const result = await getSectorSpecificQuestions(sectorCode);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch sector specific questions', error });
  }
};

const getSubSectorSpecificQuestionsController: RequestHandler = async (
  req: Request,
  res: Response
) => {
  try {
    const subSector = req.params.subSector;
    if (!subSector) {
      res.status(400).json({ message: 'Sub sector is required' });
    }
    const result = await getSubSectorSpecificQuestions(subSector);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch sub sector specific questions', error });
  }
};

const getProjectDetailsController: RequestHandler = async (req: Request, res: Response) => {
  try {
    const projectId = req.params.projectId;
    if (!projectId) {
      res.status(400).json({ message: 'Project id is required' });
    }
    const result = await getProjectDetails(projectId);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch project details', error });
  }
};

const getSuggestedProjectIdsController: RequestHandler = async (req: Request, res: Response) => {
  try {
    const projectId = req.params.projectId;
    if (!projectId) {
      res.status(400).json({ message: 'Project id is required' });
    }
    const result = await getSuggestedProjectIds(projectId);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch suggested project ids', error });
  }
};

const getSuggestedInputProjectIdsController: RequestHandler = async (
  req: Request,
  res: Response
) => {
  try {
    const projectId = req.params.projectId;
    if (!projectId) {
      res.status(400).json({ message: 'Project id is required' });
    }
    const result = await getSuggestedInputProjectsIds(projectId);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch suggested project ids', error });
  }
};

/**
 * Controller to get all available sectors from the ProjectInputData collection
 */
const getAvailableSectorsController: RequestHandler = async (req: Request, res: Response) => {
  try {
    const result = await getAvailableSectors();
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch available sectors', error });
  }
};

export {
  getAllSectorsController,
  getAllSubSectorsController,
  getConstructionCostSourcesController,
  getLevelOfEstimateController,
  getLandTypesController,
  getProcurementModelsController,
  getSectorSpecificQuestionsController,
  getSubSectorSpecificQuestionsController,
  getProjectDetailsController,
  getSuggestedProjectIdsController,
  getSuggestedInputProjectIdsController,
  getAvailableSectorsController,
};
