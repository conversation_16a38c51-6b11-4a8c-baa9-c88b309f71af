import {
	getModelForClass,
	modelOptions,
	prop,
	Severity,
} from "@typegoose/typegoose";

@modelOptions({
	schemaOptions: {
		timestamps: true,
		collection: "duration_sectors",
	},
	options: {
		allowMixed: Severity.ALLOW,
	},
})
export class DurationSectors {
	@prop({ required: true, type: () => String })
	public sector!: string;

	@prop({ required: true, type: () => String })
	public sub_sector!: string;
}

export const DurationSectorsModel = getModelForClass(DurationSectors);
