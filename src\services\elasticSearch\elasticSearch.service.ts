import logger from "@/utils/logging";
import { Client, errors as esErrors } from "@elastic/elasticsearch";
import QueryParser from "@/utils/queryParser";

// Define basic structure for document sources
export interface DocumentSource {
	id?: string;
	path?: string; // Keep for potential fallback or other indices?
	url?: string; // Add url field based on logs
	file_path?: string; // Add file_path field based on logs
	file_name?: string; // Add file_name field as potential fallback
	title?: string;
	project_code?: string; // Assuming this field exists
	file_type?: string;
	content_type?: string;
	file_size?: number;
	content_size?: number;
	accessible?: boolean;
	timestamp?: string;
	content?: string;
	// Add other potential fields from your Elasticsearch documents
}

// Interface for basic search results
export interface SearchResult {
	id: string;
	path: string;
	title: string;
	project_code?: string;
	type: string;
	size: number;
	accessible: boolean;
	scan_date: string;
	content?: string;
	highlights?: string[];
	score?: number; // Include search score
	index?: string; // The index this result came from
}

// Interface for search options provided to the service
export interface SearchOptions {
	indexes: string[]; // Target indices
	query?: string; // Search query string
	filters?: Record<string, string | number | boolean | string[]>; // Key-value filters (e.g., { project_code: '123', file_type: 'pdf' })
	file_path_filter?: string; // Wildcard pattern filter for file_path field
	field?: string; // Field to search query in (default: 'content')
	size?: number; // Number of results (default: 10)
	from?: number; // Pagination offset (default: 0)
	includeContent?: boolean; // Whether to include full content
	highlight?: boolean; // Whether to request highlights
}

// Interface for aggregation options
export interface AggregateOptions {
	indexes: string[]; // Target indices
	filters?: Record<string, string | number | boolean | string[]>; // Key-value filters
	aggregations: Record<string, unknown>; // Elasticsearch aggregation DSL part - Use unknown instead of any
}

// Interface for aggregation results (structure depends on the request)
export interface AggregationResults {
	[key: string]: unknown; // Generic structure for aggregations - Use unknown instead of any
}

class ElasticSearchService {
	private readonly client: Client;
	// Removed indexName property

	constructor(client: Client) {
		this.client = client;
		// Removed indexName initialization
	}

	static createClient(config: {
		node: string;
		username?: string;
		password?: string;
		rejectUnauthorized?: boolean;
	}): Client {
		const { node, username, password, rejectUnauthorized } = config;

		const auth = username && password ? { username, password } : undefined;

		return new Client({
			node,
			auth,
			tls: {
				rejectUnauthorized: rejectUnauthorized ?? true,
			},
		});
	}

	private buildQueryDSL(
		options: SearchOptions | AggregateOptions,
	): Record<string, unknown> {
		const { query: originalQuery, filters, file_path_filter } = options as SearchOptions;

		// Define search fields with boosts
		const searchFields = [
			"title^3",
			"file_name^2",
			"file_path^2",
			"content",
			"url",
		];

		// Create the bool query structure
		const boolQuery: {
			must: unknown[];
			filter: unknown[];
			should: unknown[];
			minimum_should_match?: number | string;
		} = {
			must: [],
			filter: [],
			should: [],
		};

		// Add filters first (filters don't affect score)
		this.addFilters(boolQuery.filter, filters, file_path_filter);

		// Handle search query if provided
		if (originalQuery?.trim()) {
			try {
				// Parse the query using the advanced query parser
				const parser = new QueryParser();
				const parsedQuery = parser.parse(originalQuery.trim());

				// Convert parsed query to Elasticsearch DSL
				const queryDSL = QueryParser.toElasticsearchDSL(
					parsedQuery,
					searchFields,
				);

				// Add the parsed query to the must clause
				boolQuery.must.push(queryDSL);

				// Add relevance boosting for exact matches in important fields
				if (parsedQuery.type !== "wildcard" && parsedQuery.type !== "phrase") {
					boolQuery.should.push({
						match_phrase: {
							title: { query: originalQuery.trim(), boost: 2.0 },
						},
					});
					boolQuery.should.push({
						match_phrase: {
							file_name: { query: originalQuery.trim(), boost: 1.5 },
						},
					});
				}
			} catch (parseError) {
				// Fallback to simple search if parsing fails
				logger.warn("Query parsing failed, falling back to simple search", {
					query: originalQuery,
					error:
						parseError instanceof Error
							? parseError.message
							: String(parseError),
				});

				boolQuery.must.push({
					multi_match: {
						query: originalQuery.trim(),
						fields: searchFields,
						type: "most_fields",
						operator: "OR",
						fuzziness: "AUTO",
					},
				});
			}
		} else {
			// If no query is provided, match all documents (respecting filters)
			boolQuery.must.push({ match_all: {} });
		}

		return { query: { bool: boolQuery } };
	}

	/**
	 * Adds term filters to the query
	 */
	private addFilters(
		filterArray: unknown[],
		filters?: Record<string, string | number | boolean | string[]>,
		file_path_filter?: string,
	): void {
		if (filters) {
			for (const [key, value] of Object.entries(filters)) {
				if (Array.isArray(value)) {
					filterArray.push({ terms: { [key]: value } });
				} else if (value !== undefined && value !== null) {
					filterArray.push({ term: { [key]: value } });
				}
			}
		}

		// Add file_path_filter as wildcard query if provided
		if (file_path_filter) {
			filterArray.push({
				wildcard: {
					file_path: {
						value: file_path_filter,
						case_insensitive: true,
					},
				},
			});
		}
	}

	async search(
		options: SearchOptions,
	): Promise<{ total: number; results: SearchResult[] }> {
		const {
			indexes,
			size = 10,
			from = 0,
			includeContent = false,
			highlight = true,
			field,
		} = options;

		// Build the search request body
		const searchBody = this.buildSearchBody(
			options,
			size,
			from,
			highlight,
			field,
		);

		logger.debug(
			`Executing ES Search: INDEX=${indexes.join(",")} BODY=${JSON.stringify(searchBody)}`,
		);

		try {
			// Execute the search
			const response = await this.client.search<DocumentSource>({
				index: indexes,
				body: searchBody,
			});

			// Convert Elasticsearch response to our expected format
			const processableResponse = {
				hits: {
					hits: response.hits.hits.map((hit) => ({
						_id: hit._id || "",
						_index: hit._index,
						_score: hit._score || 0,
						_source: hit._source,
						highlight: hit.highlight as Record<string, string[]> | undefined,
					})),
					total: response.hits.total,
				},
			};

			// Process results
			const results = this.processSearchResults(
				processableResponse,
				includeContent,
			);

			// Get total hits count
			const totalHits = this.getTotalHits(processableResponse);

			return {
				total: totalHits,
				results: results,
			};
		} catch (error) {
			this.handleSearchError(error);
		}
	}

	/**
	 * Builds the complete search request body
	 */
	private buildSearchBody(
		options: SearchOptions,
		size: number,
		from: number,
		highlight: boolean,
		field?: string,
	): Record<string, unknown> {
		const searchBody = this.buildQueryDSL(options);

		// Add pagination
		searchBody.size = size;
		searchBody.from = from;

		// Add sorting
		searchBody.sort = [{ _score: { order: "desc" } }];

		// Add highlighting if requested and there's a query
		if (highlight && options.query) {
			const highlightFields: Record<string, object> = {};

			// If a specific field is provided, highlight only that field
			// Otherwise, highlight all searchable fields
			if (field) {
				highlightFields[field] = {};
			} else {
				// Highlight all searchable fields (without the boost syntax)
				const searchableFields = [
					"title",
					"file_name",
					"content",
					"url",
					"file_path",
				];
				for (const fieldName of searchableFields) {
					highlightFields[fieldName] = {};
				}
			}

			searchBody.highlight = {
				pre_tags: ["<mark>"],
				post_tags: ["</mark>"],
				fields: highlightFields,
				fragment_size: 150,
				number_of_fragments: 3,
			};
		}

		return searchBody;
	}

	/**
	 * Processes search results into a standardized format
	 */
	private processSearchResults(
		response: {
			hits: {
				hits: Array<{
					_id: string;
					_index: string;
					_score: number;
					_source?: DocumentSource;
					highlight?: Record<string, string[]>;
				}>;
			};
		},
		includeContent: boolean,
	): SearchResult[] {
		return response.hits.hits.map((hit): SearchResult => {
			const source = hit._source || ({} as DocumentSource);
			const index = hit._index;

			// Extract title and path
			const { title, pathValue } = this.extractTitleAndPath(source);

			// Collect all highlights from all fields
			const allHighlights: string[] = [];
			if (hit.highlight) {
				for (const highlights of Object.values(hit.highlight)) {
					allHighlights.push(...highlights);
				}
			}

			// Create result object
			const result: SearchResult = {
				id: hit._id || `doc_${Date.now()}`,
				path: pathValue,
				title: title,
				project_code: source.project_code,
				type: source.file_type || source.content_type || "Unknown",
				size: source.file_size || source.content_size || 0,
				accessible: source.accessible !== undefined ? source.accessible : true,
				scan_date: source.timestamp || new Date().toISOString(),
				highlights: allHighlights,
				score: hit._score ?? 0,
				index: index,
			};

			if (includeContent) {
				result.content = source.content || "";
			}

			return result;
		});
	}

	/**
	 * Extracts title and path from document source
	 */
	private extractTitleAndPath(source: DocumentSource): {
		title: string;
		pathValue: string;
	} {
		let title = "N/A";
		let pathValue = "N/A";

		// Try to extract from URL
		if (typeof source.url === "string") {
			pathValue = source.url;
			try {
				const urlPath = new URL(pathValue).pathname;
				const decodedPath = decodeURIComponent(urlPath);
				const pathParts = decodedPath.split("/");
				title = pathParts.pop() || "N/A";
			} catch (e) {
				logger.warn(`Could not parse filename from URL: ${pathValue}`, {
					error: e instanceof Error ? e.message : String(e),
				});
			}
		}
		// Try to extract from file_path
		else if (typeof source.file_path === "string") {
			pathValue = source.file_path;
			try {
				const pathParts = pathValue.replace(/\\/g, "/").split("/");
				title = pathParts.pop() || "N/A";
			} catch (e) {
				logger.warn(`Could not parse filename from file_path: ${pathValue}`, {
					error: e instanceof Error ? e.message : String(e),
				});
			}
		}

		// Use file_name as fallback for title
		if (title === "N/A" && typeof source.file_name === "string") {
			title = source.file_name;
		}

		return { title, pathValue };
	}

	/**
	 * Gets the total number of hits from the response
	 */
	private getTotalHits(response: {
		hits: {
			total?: number | { value: number };
		};
	}): number {
		if (!response.hits.total) return 0;

		if (typeof response.hits.total === "number") {
			return response.hits.total;
		}

		if (
			typeof response.hits.total === "object" &&
			"value" in response.hits.total
		) {
			return response.hits.total.value;
		}

		return 0;
	}

	/**
	 * Handles search errors
	 */
	private handleSearchError(error: unknown): never {
		if (error instanceof esErrors.ResponseError) {
			logger.error("Elasticsearch Search Response Error:", {
				statusCode: error.statusCode,
				body: JSON.stringify(error.body),
				meta: error.meta,
			});

			// Re-throw 4xx errors for the controller to handle
			if (
				error.statusCode &&
				error.statusCode >= 400 &&
				error.statusCode < 500
			) {
				throw error;
			}

			// Wrap 5xx errors
			throw new Error(
				`Elasticsearch search failed with status ${error.statusCode}: ${JSON.stringify(error.body)}`,
			);
		}

		if (error instanceof Error) {
			logger.error("Elasticsearch Search Generic Error:", {
				message: error.message,
			});
			throw new Error(`Elasticsearch search failed: ${error.message}`);
		}

		logger.error("Elasticsearch Search Unknown Error:", { error });
		throw new Error("Elasticsearch search failed due to an unknown error");
	}
}

export default ElasticSearchService; // Export the class definition, not an instance
