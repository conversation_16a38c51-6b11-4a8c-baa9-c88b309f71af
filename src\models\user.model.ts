/**
 * This module defines the User model for managing user accounts in the system.
 * It integrates with Azure AD for user authentication and implements role-based
 * access control (RBAC) through role assignments.
 *
 * The User model maintains relationships with:
 * - Azure AD (for authentication and user identity)
 * - Roles (for access control)
 * - Other Users (for tracking role assignments)
 */

/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {
	type Ref,
	getModelForClass,
	modelOptions,
	prop,
} from "@typegoose/typegoose";
import { Role } from "./role.model";

/**
 * Configuration for the User model
 * - Collection name: users
 * - Includes timestamps for document creation and updates
 * - Supports automatic population of role references
 */
@modelOptions({
	schemaOptions: {
		collection: "users",
		timestamps: true,
	},
})
/**
 * Class representing a user in the system.
 * Users are authenticated via Azure AD and can be assigned multiple roles
 * for access control.
 *
 * @example
 * ```typescript
 * // Example user
 * {
 *   azureAdObjectId: "12345-67890-abcdef",
 *   roles: ["project_manager", "methodology_editor"],
 *   assignedBy: "98765-43210-ghijkl"  // Reference to admin user who assigned roles
 * }
 * ```
 */
export class User {
	/**
	 * Azure AD Object ID that uniquely identifies the user
	 * This ID is used to link the user with their Azure AD account
	 * @prop required - User must have an Azure AD Object ID
	 * @prop unique - No two users can have the same Azure AD Object ID
	 * @prop trim - Whitespace will be trimmed from the ID
	 * @example "12345-67890-abcdef"
	 */
	@prop({ required: true, unique: true, trim: true, type: () => String })
	public azureAdObjectId!: string;

	/**
	 * Array of references to Role documents
	 * Defines what roles and associated permissions this user has
	 * @prop required - false (a user can exist without roles)
	 * @prop trim - Whitespace will be trimmed
	 * @prop ref - References the Role model
	 * @example ["project_manager", "methodology_editor"]
	 */
	@prop({ required: false, trim: true, ref: Role })
	public roles!: Ref<Role>[];

	/**
	 * Reference to the User who assigned the roles to this user
	 * Used for auditing and tracking role assignments
	 * @prop required - false (system-created users might not have this)
	 * @prop trim - Whitespace will be trimmed
	 * @prop ref - References the User model (self-referential)
	 * @example "98765-43210-ghijkl" // Azure AD Object ID of the admin who assigned roles
	 */
	@prop({ required: false, trim: true, ref: User })
	public assignedBy!: Ref<User>;

	/**
	 * Soft delete flag - indicates if the user has been deleted
	 * @prop required - false (defaults to false for active users)
	 * @prop default - false (user is active by default)
	 * @example false (active user), true (soft deleted user)
	 */
	@prop({ required: false, default: false, type: () => Boolean })
	public isDeleted?: boolean;

	/**
	 * Timestamp when the user was soft deleted
	 * @prop required - false (only set when user is deleted)
	 * @example "2024-01-20T15:30:00Z"
	 */
	@prop({ required: false, type: () => Date })
	public deletedAt?: Date;

	/**
	 * Reference to the User who deleted this user
	 * Used for auditing and tracking deletions
	 * @prop required - false (only set when user is deleted)
	 * @prop ref - References the User model (self-referential)
	 * @example ObjectId referencing the admin who deleted this user
	 */
	@prop({ required: false, ref: User })
	public deletedBy?: Ref<User>;
}

// Create and export the Mongoose model for User
const UserModel = getModelForClass(User);
export default UserModel;
