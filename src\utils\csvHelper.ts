/**
 * Escapes a value for CSV, handling commas, quotes, and newlines.
 * @param value - The value to escape.
 * @returns The escaped string.
 */
function escapeCsvValue(value: unknown): string {
	if (value === null || value === undefined) {
		return "";
	}
	const stringValue = String(value);

	// If the value contains a comma, double quote, or newline, enclose it in double quotes
	if (
		stringValue.includes(",") ||
		stringValue.includes('"') ||
		stringValue.includes("\n") ||
		stringValue.includes("\r")
	) {
		// Escape existing double quotes by doubling them
		const escapedValue = stringValue.replace(/"/g, '""');
		return `"${escapedValue}"`;
	}

	return stringValue;
}

/**
 * Converts an array of objects to a CSV string.
 * @param headers - An array of strings representing the CSV headers.
 * @param data - An array of objects where keys correspond to headers.
 * @returns A string in CSV format.
 */
export function convertToCSV<T extends Record<string, unknown>>(
	headers: (keyof T)[],
	data: T[],
): string {
	if (!data || data.length === 0) {
		return "";
	}

	const headerRow = headers.map(escapeCsvValue).join(",");

	const dataRows = data.map((row) => {
		return headers.map((header) => escapeCsvValue(row[header])).join(",");
	});

	return [headerRow, ...dataRows].join("\r\n"); // Use CRLF for broad compatibility
}
