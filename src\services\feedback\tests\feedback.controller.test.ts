import { Feedback<PERSON><PERSON>roller } from "../feedback.controller";
import type { FeedbackService } from "../feedback.service";
import { vi, describe, it, expect, beforeEach, afterEach } from "vitest";
import type { Request, Response, NextFunction } from "express";
import { HttpException } from "@/utils/exceptions/http.exception";
import { z } from "zod";
import type { FeedbackResponse, FeedbackInput } from "../feedback.types";
import { Types } from "mongoose";

// Create mock functions using vi.fn() so TypeScript recognizes them as mocks
const mockCreateFeedback = vi.fn();
const mockGetAllFeedback = vi.fn();
const mockGetFeedbackById = vi.fn();
const mockGetFeedbackByUser = vi.fn();

// Mock the FeedbackService using the mock functions, but only cast to FeedbackService at the point of use
const mockFeedbackService = {
  createFeedback: mockCreateFeedback,
  getAllFeedback: mockGetAllFeedback,
  getFeedbackById: mockGetFeedbackById,
  getFeedbackByUser: mockGetFeedbackByUser
};
// When passing mockFeedbackService to the controller, cast as needed:
// e.g., new FeedbackController(mockFeedbackService as unknown as FeedbackService)




// Mock express objects
const mockRequest = () => {
	const req: Partial<Request> = {
		body: {},
		params: {},
		headers: {},
	};
	return req as Request;
};

const mockResponse = () => {
	const res: Partial<Response> = {
		status: vi.fn().mockReturnThis(),
		json: vi.fn().mockReturnThis(),
	};
	return res as Response;
};

const mockNext = vi.fn() as unknown as NextFunction;

describe("FeedbackController", () => {
	let controller: FeedbackController;

	beforeEach(() => {
		controller = new FeedbackController(mockFeedbackService as unknown as FeedbackService);
		vi.clearAllMocks();
	});

	afterEach(() => {
		vi.resetAllMocks();
	});

	describe("submitFeedback", () => {
		const validFeedbackData = {
			message: "Test feedback message",
			page_url: "https://test.com/page",
			page_title: "Test Page",
		};

		const mockFeedbackResponse: FeedbackResponse = {
			id: new Types.ObjectId().toString(),
			message: validFeedbackData.message,
			azureAdObjectId: "test-azure-id",
			createdAt: new Date(),
			page_url: validFeedbackData.page_url,
			page_title: validFeedbackData.page_title,
		};

		it("should submit feedback successfully", async () => {
			// Arrange
			const req = mockRequest();
			req.body = validFeedbackData;
			req.headers["x-azure-ad-object-id"] = "test-azure-id";

			const res = mockResponse();

			mockFeedbackService.createFeedback.mockResolvedValueOnce(mockFeedbackResponse);

			// Act
			await controller.submitFeedback(req as any, res, mockNext);

			// Assert
			expect(mockFeedbackService.createFeedback).toHaveBeenCalledWith({
				message: validFeedbackData.message,
				azureAdObjectId: "test-azure-id",
				page_url: validFeedbackData.page_url,
				page_title: validFeedbackData.page_title,
			});
			expect(res.status).toHaveBeenCalledWith(201);
			expect(res.json).toHaveBeenCalledWith({
				message: "Feedback submitted successfully",
				data: mockFeedbackResponse,
			});
		});

		it("should return 400 for invalid feedback data", async () => {
			// Arrange
			const req = mockRequest();
			req.body = { message: "" }; // Invalid - empty message
			req.headers["x-azure-ad-object-id"] = "test-azure-id";

			const res = mockResponse();

			// Create a real ZodError to throw
			const zodError = new z.ZodError([
				{
					code: "too_small",
					minimum: 1,
					type: "string",
					inclusive: true,
					exact: false,
					message: "Feedback message is required",
					path: ["message"]
				}
			]);

			// Mock the parse method to throw the ZodError
			const parseSpy = vi.spyOn(z.object({}), "parse").mockImplementation(() => {
				throw zodError;
			});

			// Act
			await controller.submitFeedback(req as any, res, mockNext);

			// Assert
			expect(res.status).toHaveBeenCalledWith(400);
			expect(res.json).toHaveBeenCalledWith({
				error: "Validation error",
				details: zodError.errors
			});

			// Clean up
			parseSpy.mockRestore();
		});

		it("should return 401 if user is not authenticated", async () => {
			// Arrange
			const req = mockRequest();
			req.body = validFeedbackData;
			// No x-azure-ad-object-id header

			const res = mockResponse();

			// Act
			await controller.submitFeedback(req as any, res, mockNext);

			// Assert
			expect(res.status).toHaveBeenCalledWith(401);
			expect(res.json).toHaveBeenCalledWith({
				error: "User not authenticated",
			});
			expect(mockFeedbackService.createFeedback).not.toHaveBeenCalled();
		});
	});

	describe("getAllFeedback", () => {
		it("should return all feedback", async () => {
			// Arrange
			const req = mockRequest();
			const res = mockResponse();

			const mockFeedbacks: FeedbackResponse[] = [
				{
					id: new Types.ObjectId().toString(),
					message: "Feedback 1",
					azureAdObjectId: "user1",
					createdAt: new Date(),
					page_url: "https://test.com/page1",
					page_title: "Test Page 1",
				},
				{
					id: new Types.ObjectId().toString(),
					message: "Feedback 2",
					azureAdObjectId: "user2",
					createdAt: new Date(),
					page_url: "https://test.com/page2",
					page_title: "Test Page 2",
				},
			];

			mockFeedbackService.getAllFeedback.mockResolvedValueOnce(mockFeedbacks);

			// Act
			await controller.getAllFeedback(req, res, mockNext);

			// Assert
			expect(mockFeedbackService.getAllFeedback).toHaveBeenCalled();
			expect(res.status).toHaveBeenCalledWith(200);
			expect(res.json).toHaveBeenCalledWith({ data: mockFeedbacks });
		});

		it("should return empty array when no feedback exists", async () => {
			// Arrange
			const req = mockRequest();
			const res = mockResponse();

			mockFeedbackService.getAllFeedback.mockResolvedValueOnce([]);

			// Act
			await controller.getAllFeedback(req, res, mockNext);

			// Assert
			expect(mockFeedbackService.getAllFeedback).toHaveBeenCalled();
			expect(res.status).toHaveBeenCalledWith(200);
			expect(res.json).toHaveBeenCalledWith({ data: [] });
		});
	});

	describe("getFeedbackById", () => {
		const mockFeedbackId = new Types.ObjectId().toString();
		const mockFeedback: FeedbackResponse = {
			id: mockFeedbackId,
			message: "Test feedback",
			azureAdObjectId: "test-user",
			createdAt: new Date(),
			page_url: "https://test.com/page",
			page_title: "Test Page",
		};

		it("should return feedback when ID exists", async () => {
			// Arrange
			const req = mockRequest();
			req.params = { id: mockFeedbackId };
			const res = mockResponse();

			mockFeedbackService.getFeedbackById.mockResolvedValueOnce(mockFeedback);

			// Act
			await controller.getFeedbackById(req as any, res, mockNext);

			// Assert
			expect(mockFeedbackService.getFeedbackById).toHaveBeenCalledWith(mockFeedbackId);
			expect(res.status).toHaveBeenCalledWith(200);
			expect(res.json).toHaveBeenCalledWith({ data: mockFeedback });
		});

		it("should return 404 when feedback ID doesn't exist", async () => {
			// Arrange
			const req = mockRequest();
			req.params = { id: "non-existent-id" };
			const res = mockResponse();

			mockFeedbackService.getFeedbackById.mockResolvedValueOnce(null);

			// Act
			await controller.getFeedbackById(req as any, res, mockNext);

			// Assert
			expect(mockFeedbackService.getFeedbackById).toHaveBeenCalledWith("non-existent-id");
			expect(res.status).toHaveBeenCalledWith(404);
			expect(res.json).toHaveBeenCalledWith({ error: "Feedback not found" });
		});
	});

	describe("getFeedbackByUser", () => {
		it("should return feedback for the authenticated user", async () => {
			// Arrange
			const req = mockRequest();
			req.headers["x-azure-ad-object-id"] = "test-user-id";
			const res = mockResponse();

			const mockFeedbacks: FeedbackResponse[] = [
				{
					id: new Types.ObjectId().toString(),
					message: "User Feedback 1",
					azureAdObjectId: "test-user-id",
					createdAt: new Date(),
					page_url: "https://test.com/page1",
					page_title: "Test Page 1",
				},
				{
					id: new Types.ObjectId().toString(),
					message: "User Feedback 2",
					azureAdObjectId: "test-user-id",
					createdAt: new Date(),
					page_url: "https://test.com/page2",
					page_title: "Test Page 2",
				},
			];

			mockFeedbackService.getFeedbackByUser.mockResolvedValueOnce(mockFeedbacks);

			// Act
			await controller.getFeedbackByUser(req, res, mockNext);

			// Assert
			expect(mockFeedbackService.getFeedbackByUser).toHaveBeenCalledWith("test-user-id");
			expect(res.status).toHaveBeenCalledWith(200);
			expect(res.json).toHaveBeenCalledWith({ data: mockFeedbacks });
		});

		it("should return empty array when user has no feedback", async () => {
			// Arrange
			const req = mockRequest();
			req.headers["x-azure-ad-object-id"] = "user-with-no-feedback";
			const res = mockResponse();

			mockFeedbackService.getFeedbackByUser.mockResolvedValueOnce([]);

			// Act
			await controller.getFeedbackByUser(req, res, mockNext);

			// Assert
			expect(mockFeedbackService.getFeedbackByUser).toHaveBeenCalledWith("user-with-no-feedback");
			expect(res.status).toHaveBeenCalledWith(200);
			expect(res.json).toHaveBeenCalledWith({ data: [] });
		});
	});
});
