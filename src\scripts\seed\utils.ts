import UserModel from "@/models/user.model";
import RoleModel from "@/models/role.model";
import PermissionModel from "@/models/permission.model";
import logger from "@/utils/logging";
import { Types } from "mongoose";

/**
 * Utility functions for the seeding system
 */

export interface SeedStats {
	permissions: {
		total: number;
		list: string[];
	};
	roles: {
		total: number;
		list: Array<{ name: string; permissionCount: number }>;
	};
	users: {
		total: number;
		list: Array<{ azureAdObjectId: string; roleCount: number }>;
	};
}

/**
 * Helper function to safely convert permission/role references to string IDs
 */
function getIdAsString(item: any): string {
	if (typeof item === "string") {
		return item;
	}
	if (item && typeof item === "object" && item._id) {
		return item._id.toString();
	}
	if (item instanceof Types.ObjectId) {
		return item.toString();
	}
	return String(item);
}

/**
 * Helper function to safely get name from permission/role object
 */
function getNameFromObject(item: any): string {
	if (item && typeof item === "object" && item.name) {
		return item.name;
	}
	return String(item);
}

/**
 * Get comprehensive statistics about seeded data
 */
export async function getSeedStats(): Promise<SeedStats> {
	logger.info("📊 Gathering seed statistics...");

	// Get all permissions
	const permissions = await PermissionModel.find({}).lean();

	// Get all roles with permission counts
	const roles = await RoleModel.find({}).lean();
	const roleStats = roles.map((role) => ({
		name: role.name,
		permissionCount: (role.permissions || []).length,
	}));

	// Get all users with role counts
	const users = await UserModel.find({}).lean();
	const userStats = users.map((user) => ({
		azureAdObjectId: user.azureAdObjectId,
		roleCount: (user.roles || []).length,
	}));

	const stats: SeedStats = {
		permissions: {
			total: permissions.length,
			list: permissions.map((p) => p.name),
		},
		roles: {
			total: roles.length,
			list: roleStats,
		},
		users: {
			total: users.length,
			list: userStats,
		},
	};

	return stats;
}

/**
 * Display comprehensive statistics about seeded data
 */
export async function displaySeedStats(): Promise<void> {
	const stats = await getSeedStats();

	logger.info("");
	logger.info("📊 SEED DATA STATISTICS");
	logger.info("========================");
	logger.info("");

	// Permissions
	logger.info(`📜 PERMISSIONS (${stats.permissions.total} total):`);
	for (const name of stats.permissions.list) {
		logger.info(`   • ${name}`);
	}
	logger.info("");

	// Roles
	logger.info(`👥 ROLES (${stats.roles.total} total):`);
	for (const role of stats.roles.list) {
		logger.info(`   • ${role.name} (${role.permissionCount} permissions)`);
	}
	logger.info("");

	// Users
	logger.info(`🧑‍💼 USERS (${stats.users.total} total):`);
	for (const user of stats.users.list) {
		logger.info(`   • ${user.azureAdObjectId} (${user.roleCount} roles)`);
	}
	logger.info("");
}

/**
 * Validate the integrity of seeded data
 */
export async function validateSeedData(): Promise<{
	isValid: boolean;
	errors: string[];
}> {
	const errors: string[] = [];

	try {
		logger.info("🔍 Validating seed data integrity...");

		// Get all data
		const permissions = await PermissionModel.find({}).lean();
		const roles = await RoleModel.find({}).populate("permissions").lean();
		const users = await UserModel.find({}).populate("roles").lean();

		// Create lookup sets for validation
		const permissionIds = new Set(permissions.map((p) => p._id.toString()));
		const roleIds = new Set(roles.map((r) => r._id.toString()));

		// Validate roles have valid permissions
		for (const role of roles) {
			if (role.permissions && Array.isArray(role.permissions)) {
				for (const permission of role.permissions) {
					const permId = getIdAsString(permission);
					if (!permissionIds.has(permId)) {
						errors.push(
							`Role "${role.name}" references non-existent permission: ${permId}`,
						);
					}
				}
			}
		}

		// Validate users have valid roles
		for (const user of users) {
			if (user.roles && Array.isArray(user.roles)) {
				for (const role of user.roles) {
					const roleId = getIdAsString(role);
					if (!roleIds.has(roleId)) {
						errors.push(
							`User "${user.azureAdObjectId}" references non-existent role: ${roleId}`,
						);
					}
				}
			}
		}

		// Check for orphaned data
		const usedPermissionIds = new Set<string>();
		for (const role of roles) {
			if (role.permissions) {
				for (const perm of role.permissions) {
					const permId = getIdAsString(perm);
					usedPermissionIds.add(permId);
				}
			}
		}

		const unusedPermissions = permissions.filter(
			(p) => !usedPermissionIds.has(p._id.toString()),
		);
		if (unusedPermissions.length > 0) {
			logger.warn(
				`⚠️  Found ${unusedPermissions.length} unused permissions: ${unusedPermissions.map((p) => p.name).join(", ")}`,
			);
		}

		const usedRoleIds = new Set<string>();
		for (const user of users) {
			if (user.roles) {
				for (const role of user.roles) {
					const roleId = getIdAsString(role);
					usedRoleIds.add(roleId);
				}
			}
		}

		const unusedRoles = roles.filter((r) => !usedRoleIds.has(r._id.toString()));
		if (unusedRoles.length > 0) {
			logger.warn(
				`⚠️  Found ${unusedRoles.length} unused roles: ${unusedRoles.map((r) => r.name).join(", ")}`,
			);
		}
	} catch (error) {
		errors.push(`Validation error: ${error}`);
	}

	const isValid = errors.length === 0;

	if (isValid) {
		logger.info("✅ Seed data validation passed!");
	} else {
		logger.error("❌ Seed data validation failed:");
		for (const error of errors) {
			logger.error(`   • ${error}`);
		}
	}

	return { isValid, errors };
}

/**
 * Clean up all seeded data (DANGEROUS!)
 */
export async function cleanupSeedData(): Promise<void> {
	logger.warn(
		"⚠️  WARNING: This will delete ALL users, roles, and permissions!",
	);
	logger.warn("⚠️  This action cannot be undone!");

	try {
		// Delete in reverse dependency order
		const userCount = await UserModel.countDocuments();
		await UserModel.deleteMany({});
		logger.info(`🗑️  Deleted ${userCount} users`);

		const roleCount = await RoleModel.countDocuments();
		await RoleModel.deleteMany({});
		logger.info(`🗑️  Deleted ${roleCount} roles`);

		const permissionCount = await PermissionModel.countDocuments();
		await PermissionModel.deleteMany({});
		logger.info(`🗑️  Deleted ${permissionCount} permissions`);

		logger.info("✅ Cleanup completed");
	} catch (error) {
		logger.error("❌ Error during cleanup:", error);
		throw error;
	}
}

/**
 * Export specific roles and permissions to a JSON file
 */
export async function exportSeedData(): Promise<{
	permissions: any[];
	roles: any[];
	users: any[];
}> {
	logger.info("📤 Exporting seed data...");

	const permissions = await PermissionModel.find({}).lean();
	const roles = await RoleModel.find({}).populate("permissions").lean();
	const users = await UserModel.find({}).populate("roles").lean();

	const exportData = {
		permissions: permissions.map((p) => ({
			name: p.name,
			description: p.description,
		})),
		roles: roles.map((r) => ({
			name: r.name,
			description: r.description,
			permissions: (r.permissions || []).map((p) => getNameFromObject(p)),
		})),
		users: users.map((u) => ({
			azureAdObjectId: u.azureAdObjectId,
			roles: (u.roles || []).map((r) => getNameFromObject(r)),
		})),
	};

	logger.info("✅ Export completed");
	return exportData;
}
