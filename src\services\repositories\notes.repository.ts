import type { NotesDocument } from "@/models/productivity/notes.model";

export interface BulkUpdateItem {
	id: string;
	updates: Partial<NotesDocument>;
}

export interface BulkUpdateResult {
	modifiedCount: number;
	matchedCount: number;
}

export interface INotesRepository {
	findAll(): Promise<NotesDocument[]>;
	findById(id: string): Promise<NotesDocument | null>;
	findByTradeId(tradeId: number): Promise<NotesDocument[]>;
	findByTradeInfoId(tradeInfoId: number): Promise<NotesDocument[]>;
	findActiveNotes(): Promise<NotesDocument[]>;
	findVisibleNotes(): Promise<NotesDocument[]>;
	bulkUpdate(updates: BulkUpdateItem[]): Promise<BulkUpdateResult>;
}
