import type { INotesRepository, BulkUpdateItem, BulkUpdateResult } from "@/services/repositories/notes.repository";
import type { NotesDocument } from "@/models/productivity/notes.model";
import logger from "@/utils/logging";
import { HttpException } from "@/utils/exceptions/http.exception";

export class NotesService {
	constructor(private readonly notesRepository: INotesRepository) {}

	/**
	 * Retrieves all notes with optional filtering
	 * @param options - Filtering options
	 * @returns Promise<NotesDocument[]>
	 */
	public async getAllNotes(options?: {
		tradeId?: number;
		tradeInfoId?: number;
		activeOnly?: boolean;
		visibleOnly?: boolean;
	}): Promise<NotesDocument[]> {
		try {
			logger.info("NotesService: Getting all notes with options:", options);

			// If specific filters are requested
			if (options?.tradeId) {
				return await this.notesRepository.findByTradeId(options.tradeId);
			}

			if (options?.tradeInfoId) {
				return await this.notesRepository.findByTradeInfoId(options.tradeInfoId);
			}

			if (options?.activeOnly) {
				return await this.notesRepository.findActiveNotes();
			}

			if (options?.visibleOnly) {
				return await this.notesRepository.findVisibleNotes();
			}

			// Return all notes with projection
			return await this.notesRepository.findAll();
		} catch (error) {
			logger.error("NotesService: Error getting all notes:", error);
			throw new HttpException(500, "Failed to retrieve notes");
		}
	}

	/**
	 * Retrieves a note by its ID
	 * @param id - Note ID
	 * @returns Promise<NotesDocument>
	 */
	public async getNoteById(id: string): Promise<NotesDocument> {
		try {
			logger.info(`NotesService: Getting note by ID: ${id}`);

			const note = await this.notesRepository.findById(id);
			if (!note) {
				throw new HttpException(404, `Note with ID ${id} not found`);
			}

			return note;
		} catch (error) {
			if (error instanceof HttpException) {
				throw error;
			}
			logger.error(`NotesService: Error getting note by ID ${id}:`, error);
			throw new HttpException(500, "Failed to retrieve note");
		}
	}

	/**
	 * Retrieves notes by Trade ID
	 * @param tradeId - Trade ID
	 * @returns Promise<NotesDocument[]>
	 */
	public async getNotesByTradeId(tradeId: number): Promise<NotesDocument[]> {
		try {
			logger.info(`NotesService: Getting notes by TradeID: ${tradeId}`);
			return await this.notesRepository.findByTradeId(tradeId);
		} catch (error) {
			logger.error(
				`NotesService: Error getting notes by TradeID ${tradeId}:`,
				error,
			);
			throw new HttpException(500, "Failed to retrieve notes by Trade ID");
		}
	}

	/**
	 * Retrieves notes by Trade Info ID
	 * @param tradeInfoId - Trade Info ID
	 * @returns Promise<NotesDocument[]>
	 */
	public async getNotesByTradeInfoId(
		tradeInfoId: number,
	): Promise<NotesDocument[]> {
		try {
			logger.info(`NotesService: Getting notes by TradeInfoID: ${tradeInfoId}`);
			return await this.notesRepository.findByTradeInfoId(tradeInfoId);
		} catch (error) {
			logger.error(
				`NotesService: Error getting notes by TradeInfoID ${tradeInfoId}:`,
				error,
			);
			throw new HttpException(500, "Failed to retrieve notes by Trade Info ID");
		}
	}

	/**
	 * Retrieves only active notes (NotActive = 0)
	 * @returns Promise<NotesDocument[]>
	 */
	public async getActiveNotes(): Promise<NotesDocument[]> {
		try {
			logger.info("NotesService: Getting active notes");
			return await this.notesRepository.findActiveNotes();
		} catch (error) {
			logger.error("NotesService: Error getting active notes:", error);
			throw new HttpException(500, "Failed to retrieve active notes");
		}
	}

	/**
	 * Retrieves only visible notes (NotShow = 0)
	 * @returns Promise<NotesDocument[]>
	 */
	public async getVisibleNotes(): Promise<NotesDocument[]> {
		try {
			logger.info("NotesService: Getting visible notes");
			return await this.notesRepository.findVisibleNotes();
		} catch (error) {
			logger.error("NotesService: Error getting visible notes:", error);
			throw new HttpException(500, "Failed to retrieve visible notes");
		}
	}

	/**
	 * Performs bulk update on multiple notes
	 * @param updates - Array of note updates with ID and update data
	 * @returns Promise<BulkUpdateResult>
	 */
	public async bulkUpdateNotes(updates: BulkUpdateItem[]): Promise<BulkUpdateResult> {
		try {
			if (!updates || updates.length === 0) {
				throw new HttpException(400, "No updates provided");
			}

			logger.info(`NotesService: Performing bulk update on ${updates.length} notes`);
			
			// Validate all IDs are provided
			const invalidUpdates = updates.filter(update => !update.id || !update.updates);
			if (invalidUpdates.length > 0) {
				throw new HttpException(400, "All updates must have valid id and updates fields");
			}

			const result = await this.notesRepository.bulkUpdate(updates);
			
			logger.info(`NotesService: Bulk update completed. Modified: ${result.modifiedCount}, Matched: ${result.matchedCount}`);
			
			return result;
		} catch (error) {
			if (error instanceof HttpException) {
				throw error;
			}
			logger.error("NotesService: Error performing bulk update:", error);
			throw new HttpException(500, "Failed to perform bulk update");
		}
	}
}
