import { describe, it, expect, beforeAll, afterAll, beforeEach } from "vitest";
import request from "supertest";
import type { Application } from "express";
import { Types } from "mongoose";
import app from "../../src/infrastructure/app";
import { HowToGuideModel } from "../../src/models/howTo/howTo.model";

describe("HowTo Routes Integration Tests", () => {
	let testApp: Application;
	let testHowToId: string;
	let testHowTo: any;

	beforeAll(async () => {
		testApp = app;
		// Setup test data
		await setupTestData();
	});

	afterAll(async () => {
		// Cleanup test data
		await cleanupTestData();
	});

	beforeEach(async () => {
		// Reset test data state if needed
	});

	async function setupTestData() {
		// Create test HowTo guide
		testHowTo = await HowToGuideModel.create({
			title: "Test HowTo Guide",
			description: "This is a test HowTo guide for integration testing",
			overview: "Test overview content",
			taxonomy: {
				sector: { id: "test-sector", label: "Test Sector" },
				subsector: { id: "test-subsector", label: "Test Subsector" },
				buildType: { id: "test-buildtype", label: "Test Build Type" },
				hierarchyPath: ["test-sector", "test-subsector", "test-buildtype"],
			},
			content: {
				attachedFiles: [],
			},
			metadata: {
				order: 1,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		});
		testHowToId = testHowTo._id.toString();
	}

	async function cleanupTestData() {
		// Clean up test data
		await HowToGuideModel.deleteMany({
			title: { $regex: /^Test.*/ },
		});
	}

	describe("GET /api/einstein/how-to", () => {
		it("should return all HowTo guides with pagination", async () => {
			const response = await request(testApp)
				.get("/api/einstein/how-to")
				.expect(200);

			expect(response.body).toHaveProperty("data");
			expect(response.body.data).toHaveProperty("documents");
			expect(response.body.data).toHaveProperty("pagination");
			expect(Array.isArray(response.body.data.documents)).toBe(true);
			expect(response.body.data.pagination).toHaveProperty("page");
			expect(response.body.data.pagination).toHaveProperty("limit");
			expect(response.body.data.pagination).toHaveProperty("total");
			expect(response.body.data.pagination).toHaveProperty("pages");
		});

		it("should handle pagination parameters", async () => {
			const response = await request(testApp)
				.get("/api/einstein/how-to?page=1&limit=5")
				.expect(200);

			expect(response.body.data.pagination.page).toBe(1);
			expect(response.body.data.pagination.limit).toBe(5);
		});
	});

	describe("GET /api/einstein/how-to/:id", () => {
		it("should return specific HowTo guide by ID", async () => {
			// Create a fresh HowTo guide for this test to ensure it exists
			const freshHowTo = await HowToGuideModel.create({
				title: "Fresh Test HowTo Guide",
				description: "This is a fresh test HowTo guide",
				overview: "Fresh test overview content",
				taxonomy: {
					sector: { id: "fresh-sector", label: "Fresh Sector" },
					subsector: { id: "fresh-subsector", label: "Fresh Subsector" },
					buildType: { id: "fresh-buildtype", label: "Fresh Build Type" },
					hierarchyPath: ["fresh-sector", "fresh-subsector", "fresh-buildtype"],
				},
				content: {
					attachedFiles: [],
				},
				metadata: {
					order: 1,
					createdAt: new Date(),
					updatedAt: new Date(),
				},
			});

			const response = await request(testApp)
				.get(`/api/einstein/how-to/${freshHowTo._id}`)
				.expect(200);

			expect(response.body).toHaveProperty("data");
			expect(response.body.data._id).toBe(freshHowTo._id.toString());
			expect(response.body.data.title).toBe("Fresh Test HowTo Guide");

			// Cleanup
			await HowToGuideModel.findByIdAndDelete(freshHowTo._id);
		});

		it("should return 404 for non-existent HowTo guide", async () => {
			const nonExistentId = new Types.ObjectId().toString();
			const response = await request(testApp)
				.get(`/api/einstein/how-to/${nonExistentId}`)
				.expect(404);

			expect(response.body).toHaveProperty("error", "HowTo not found");
		});

		it("should return 400 for invalid ID format", async () => {
			const response = await request(testApp)
				.get("/api/einstein/how-to/invalid-id")
				.expect(400);

			expect(response.body).toHaveProperty("error", "Validation error");
		});
	});

	describe("POST /api/einstein/how-to", () => {
		it("should create new HowTo guide successfully", async () => {
			const newHowToData = {
				title: "Test New HowTo Guide",
				description: "New test description",
				overview: "New test overview",
				taxonomy: {
					sector: { id: "new-sector", label: "New Sector" },
					subsector: { id: "new-subsector", label: "New Subsector" },
					buildType: { id: "new-buildtype", label: "New Build Type" },
				},
				metadata: {
					order: 2,
				},
			};

			const response = await request(testApp)
				.post("/api/einstein/how-to")
				.send(newHowToData)
				.expect(201);

			expect(response.body).toHaveProperty("data");
			expect(response.body.data.title).toBe(newHowToData.title);
			expect(response.body.data.description).toBe(newHowToData.description);
			expect(response.body.data.taxonomy.sector.id).toBe(
				newHowToData.taxonomy.sector.id,
			);

			// Cleanup created HowTo
			await HowToGuideModel.findByIdAndDelete(response.body.data._id);
		});

		it("should create HowTo guide with attached files", async () => {
			const newHowToData = {
				title: "Test HowTo with Files",
				description: "Test with attached files",
				taxonomy: {
					sector: { id: "file-sector", label: "File Sector" },
				},
				attachedFiles: [
					{
						url: "https://example.com/test-file.pdf",
						label: "Test File",
					},
				],
			};

			const response = await request(testApp)
				.post("/api/einstein/how-to")
				.send(newHowToData)
				.expect(201);

			expect(response.body.data.title).toBe(newHowToData.title);
			// Note: Attached files handling depends on your model implementation

			// Cleanup
			await HowToGuideModel.findByIdAndDelete(response.body.data._id);
		});

		it("should return 400 for invalid data", async () => {
			const invalidData = {
				title: "", // Invalid: empty title
				taxonomy: {},
			};

			const response = await request(testApp)
				.post("/api/einstein/how-to")
				.send(invalidData)
				.expect(400);

			expect(response.body).toHaveProperty("error", "Validation error");
			expect(response.body).toHaveProperty("details");
		});
	});

	describe("PUT /api/einstein/how-to/:id", () => {
		it("should update existing HowTo guide", async () => {
			// Create a fresh HowTo guide for this test to ensure it exists
			const howToToUpdate = await HowToGuideModel.create({
				title: "HowTo to Update",
				description: "This will be updated",
				taxonomy: {
					sector: { id: "update-sector", label: "Update Sector" },
				},
				content: { attachedFiles: [] },
				metadata: {
					order: 1,
					createdAt: new Date(),
					updatedAt: new Date(),
				},
			});

			const updateData = {
				title: "Updated Test HowTo Guide",
				description: "Updated description",
			};

			const response = await request(testApp)
				.put(`/api/einstein/how-to/${howToToUpdate._id}`)
				.send(updateData)
				.expect(200);

			expect(response.body).toHaveProperty("data");
			expect(response.body.data.title).toBe(updateData.title);
			expect(response.body.data.description).toBe(updateData.description);

			// Cleanup
			await HowToGuideModel.findByIdAndDelete(howToToUpdate._id);
		});

		it("should return 404 for non-existent HowTo guide", async () => {
			const nonExistentId = new Types.ObjectId().toString();
			const updateData = { title: "Updated Title" };

			const response = await request(testApp)
				.put(`/api/einstein/how-to/${nonExistentId}`)
				.send(updateData)
				.expect(404);

			expect(response.body).toHaveProperty("error", "HowTo not found");
		});
	});

	describe("DELETE /api/einstein/how-to/:id", () => {
		it("should delete existing HowTo guide", async () => {
			// Create a HowTo guide specifically for deletion test
			const howToToDelete = await HowToGuideModel.create({
				title: "Test HowTo to Delete",
				description: "This will be deleted",
				taxonomy: {
					sector: { id: "delete-sector", label: "Delete Sector" },
				},
				content: { attachedFiles: [] },
				metadata: {
					order: 1,
					createdAt: new Date(),
					updatedAt: new Date(),
				},
			});

			const response = await request(testApp)
				.delete(`/api/einstein/how-to/${howToToDelete._id}`)
				.expect(200);

			expect(response.body).toHaveProperty(
				"message",
				"HowTo deleted successfully",
			);

			// Verify deletion
			const deletedHowTo = await HowToGuideModel.findById(howToToDelete._id);
			expect(deletedHowTo).toBeNull();
		});

		it("should return 404 for non-existent HowTo guide", async () => {
			const nonExistentId = new Types.ObjectId().toString();

			const response = await request(testApp)
				.delete(`/api/einstein/how-to/${nonExistentId}`)
				.expect(404);

			expect(response.body).toHaveProperty("error", "HowTo not found");
		});
	});

	describe("GET /api/einstein/how-to/filter/search", () => {
		it("should search HowTo guides by search term", async () => {
			const response = await request(testApp)
				.get("/api/einstein/how-to/filter/search?searchTerm=Test")
				.expect(200);

			expect(response.body).toHaveProperty("data");
			expect(response.body.data).toHaveProperty("documents");
			expect(response.body.data).toHaveProperty("pagination");
		});

		it("should return 400 for missing search term", async () => {
			const response = await request(testApp)
				.get("/api/einstein/how-to/filter/search")
				.expect(400);

			expect(response.body).toHaveProperty("error", "Validation error");
		});
	});

	describe("GET /api/einstein/how-to/filter/filtered", () => {
		it("should filter HowTo guides by taxonomy", async () => {
			const response = await request(testApp)
				.get("/api/einstein/how-to/filter/filtered?sectorId=test-sector")
				.expect(200);

			expect(response.body).toHaveProperty("data");
			expect(response.body.data).toHaveProperty("documents");
			expect(response.body.data).toHaveProperty("pagination");
		});
	});

	describe("GET /api/einstein/how-to/filter/filtered/tree", () => {
		it("should return filtered HowTo guides in tree structure", async () => {
			const response = await request(testApp)
				.get("/api/einstein/how-to/filter/filtered/tree?sectorId=test-sector")
				.expect(200);

			expect(response.body).toHaveProperty("data");
			expect(Array.isArray(response.body.data)).toBe(true);
			expect(response.body).toHaveProperty("pagination");
		});
	});

	describe("GET /api/einstein/how-to/filter/by-parent", () => {
		it("should return HowTo guides by parent ID", async () => {
			const response = await request(testApp)
				.get("/api/einstein/how-to/filter/by-parent")
				.expect(200);

			expect(response.body).toHaveProperty("data");
			expect(Array.isArray(response.body.data)).toBe(true);
		});

		it("should handle specific parent ID", async () => {
			const response = await request(testApp)
				.get(`/api/einstein/how-to/filter/by-parent?parentId=${testHowToId}`)
				.expect(200);

			expect(response.body).toHaveProperty("data");
		});
	});

	describe("GET /api/einstein/how-to/filter/recent", () => {
		it("should return recent HowTo guides", async () => {
			const response = await request(testApp)
				.get("/api/einstein/how-to/filter/recent")
				.expect(200);

			expect(response.body).toHaveProperty("data");
			expect(Array.isArray(response.body.data)).toBe(true);
		});

		it("should handle custom limit", async () => {
			const response = await request(testApp)
				.get("/api/einstein/how-to/filter/recent?limit=5")
				.expect(200);

			expect(response.body).toHaveProperty("data");
			expect(response.body.data.length).toBeLessThanOrEqual(5);
		});
	});

	describe("GET /api/einstein/how-to/taxonomy/sectors", () => {
		it("should return available sectors", async () => {
			const response = await request(testApp)
				.get("/api/einstein/how-to/taxonomy/sectors")
				.expect(200);

			expect(response.body).toHaveProperty("data");
			expect(Array.isArray(response.body.data)).toBe(true);
		});
	});

	describe("GET /api/einstein/how-to/taxonomy/subsectors", () => {
		it("should return subsectors for valid sector ID", async () => {
			const response = await request(testApp)
				.get("/api/einstein/how-to/taxonomy/subsectors?sectorId=test-sector")
				.expect(200);

			expect(response.body).toHaveProperty("data");
			expect(Array.isArray(response.body.data)).toBe(true);
		});

		it("should return 400 for missing sector ID", async () => {
			const response = await request(testApp)
				.get("/api/einstein/how-to/taxonomy/subsectors")
				.expect(400);

			expect(response.body).toHaveProperty("error", "Sector ID is required");
		});
	});

	describe("GET /api/einstein/how-to/taxonomy/build-types", () => {
		it("should return build types for valid sector and subsector IDs", async () => {
			const response = await request(testApp)
				.get(
					"/api/einstein/how-to/taxonomy/build-types?sectorId=test-sector&subsectorId=test-subsector",
				)
				.expect(200);

			expect(response.body).toHaveProperty("data");
			expect(Array.isArray(response.body.data)).toBe(true);
		});

		it("should return 400 for missing required parameters", async () => {
			const response = await request(testApp)
				.get("/api/einstein/how-to/taxonomy/build-types?sectorId=test-sector")
				.expect(400);

			expect(response.body).toHaveProperty(
				"error",
				"Sector ID and Subsector ID are required",
			);
		});
	});

	describe("POST /api/einstein/how-to/upload/presigned-url", () => {
		it("should generate presigned URL for file upload", async () => {
			const requestData = {
				fileName: "test-file.pdf",
				fileType: "application/pdf",
				expiryMinutes: 60,
			};

			// Note: This test might fail if Azure Blob Service is not properly configured
			// You may want to mock the Azure service for unit tests
			const response = await request(testApp)
				.post("/api/einstein/how-to/upload/presigned-url")
				.send(requestData);

			// Expect either success (200) or configuration error (500)
			expect([200, 500]).toContain(response.status);

			if (response.status === 200) {
				expect(response.body).toHaveProperty(
					"message",
					"Upload presigned URL generated successfully",
				);
				expect(response.body).toHaveProperty("data");
			}
		});

		it("should return 400 for invalid request data", async () => {
			const invalidData = {
				fileName: "", // Invalid: empty fileName
				fileType: "application/pdf",
			};

			const response = await request(testApp)
				.post("/api/einstein/how-to/upload/presigned-url")
				.send(invalidData)
				.expect(400);

			expect(response.body).toHaveProperty("error", "Validation error");
		});
	});

	describe("POST /api/einstein/how-to/upload/batch-presigned-urls", () => {
		it("should generate batch presigned URLs", async () => {
			const requestData = {
				files: [
					{ fileName: "file1.pdf", fileType: "application/pdf" },
					{ fileName: "file2.jpg", fileType: "image/jpeg" },
				],
				expiryMinutes: 60,
			};

			const response = await request(testApp)
				.post("/api/einstein/how-to/upload/batch-presigned-urls")
				.send(requestData);

			// Expect either success (200) or configuration error (500)
			expect([200, 500]).toContain(response.status);

			if (response.status === 200) {
				expect(response.body).toHaveProperty(
					"message",
					"Batch upload presigned URLs generated successfully",
				);
				expect(response.body).toHaveProperty("data");
			}
		});
	});

	describe("POST /api/einstein/how-to/filter/", () => {
		it("should create HowTo template", async () => {
			const templateData = {
				title: "Test Template",
				sector: { id: "template-sector", label: "Template Sector" },
				subsector: { id: "template-subsector", label: "Template Subsector" },
				buildType: { id: "template-buildtype", label: "Template Build Type" },
			};

			const response = await request(testApp)
				.post("/api/einstein/how-to/filter/")
				.send(templateData)
				.expect(201);

			expect(response.body).toHaveProperty(
				"message",
				"HowTo template created successfully",
			);
			expect(response.body).toHaveProperty("data");
			expect(response.body.data.title).toBe(templateData.title);

			// Cleanup
			await HowToGuideModel.findByIdAndDelete(response.body.data._id);
		});
	});
});
