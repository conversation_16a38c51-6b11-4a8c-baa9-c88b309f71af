import { MongoMemoryServer } from 'mongodb-memory-server';
import mongoose from 'mongoose';
import { afterAll, afterEach, beforeAll } from 'vitest';

let mongo: MongoMemoryServer;

beforeAll(async () => {
  // Starting the in-memory database
  mongo = await MongoMemoryServer.create();
  const uri = mongo.getUri();
  await mongoose.connect(uri);

  // TODO: We will seed the database with test data here
});

beforeEach(async () => {
  // TODO: We will seed the database with test data here
});

afterEach(async () => {
  // Cleaning up the database after each test
  const collections = mongoose.connection.collections;
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }
});

afterAll(async () => {
  // Closing the connection to the database after all tests are done
  await mongoose.disconnect();
  await mongo.stop();
});
