---
description: 
globs: 
alwaysApply: true
---
# Error Handling <PERSON>s

This project uses a consistent approach to error handling for maintainability and proper error propagation.

## Express Error Handling

The application uses the `express-async-errors` package to automatically catch rejected promises in async route handlers, which eliminates the need for extensive try-catch blocks throughout the code.

## Route Handler Pattern

All route handlers should use the async/await pattern with explicit error passing to the next middleware:

```typescript
// Correct pattern for Express routes
router.post('/resource', async (req, res, next) => {
  await resourceController.createResource(req, res, next);
});
```

Example implementation in [feedback.routes.ts](mdc:src/services/feedback/feedback.routes.ts).

## Controller Pattern

Controllers should:

1. **Avoid unnecessary try-catch blocks** - Express-async-errors will catch and handle most errors
2. **Use targeted try-catch only for specific cases** that need custom error handling:
   - Input validation
   - Format conversions
   - Specific error status codes

```typescript
// Example of proper try-catch usage for validation only
public submitFeedback = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const validatedData = schema.parse(req.body);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Validation error', details: error.errors });
    }
    throw error; // Let express-async-errors handle other errors
  }
  
  // Proceed with business logic without try-catch
  const result = await this.service.performOperation();
  return res.status(200).json(result);
};
```

## Service Layer Pattern

Services should:

1. **Throw appropriate errors** without catching them
2. **Use descriptive error messages** or custom error classes
3. **Not handle HTTP concerns** (status codes, responses) - that's the controller's job

```typescript
// Service method should throw errors, not catch them
public async performOperation(data: SomeType): Promise<ResultType> {
  const entity = await this.repository.findById(data.id);
  
  if (!entity) {
    throw new HttpException(404, 'Resource not found');
  }
  
  return this.repository.update(entity);
}
```

## Repository Pattern

Repositories should:

1. **Throw clear errors for database operations**
2. **Not catch errors unless transforming them**
3. **Provide detailed error information** for debugging

This pattern ensures errors are properly propagated and handled at the appropriate layer of the application.

