import { defineConfig } from "vitest/config";
import path from "node:path";
import tsconfigPaths from "vite-tsconfig-paths";
export default defineConfig({
  plugins: [tsconfigPaths()],
  test: {
    globals: true,
    environment: "node",
    setupFiles: ["./test/setup.ts"],
    include: ["test/**/*.test.ts", "src/**/*.test.ts"],
    coverage: {
      provider: "v8",
      reporter: ["text", "json", "html"],
      exclude: ["node_modules/", "test/setup.ts"],
    },
    pool: "threads",
    poolOptions: {
      threads: {
        singleThread: false,
        isolate: true,
        maxThreads: 8,
        minThreads: 2,
      },
    },
    sequence: {
      hooks: "stack",
    },
    testTimeout: 10000,
    maxWorkers: 10,
  },
});
