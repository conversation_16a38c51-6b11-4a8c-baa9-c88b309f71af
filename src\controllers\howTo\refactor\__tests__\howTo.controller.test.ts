import {
	describe,
	it,
	expect,
	vi,
	beforeEach,
	type MockedFunction,
} from "vitest";
import type { Request, Response, NextFunction } from "express";
import { Types } from "mongoose";
import { HowToController } from "../howTo.controller";
import type { IHowToRepository } from "../../../../services/repositories/howTo.repository";
import type { AzureBlobService } from "../../../../services/file/azureBlob.service";

// Mock dependencies
const mockHowToRepository = {
	findAll: vi.fn(),
	findById: vi.fn(),
	create: vi.fn(),
	update: vi.fn(),
	delete: vi.fn(),
	findByFilters: vi.fn(),
	findByParent: vi.fn(),
	search: vi.fn(),
	findRecent: vi.fn(),
	getSectors: vi.fn(),
	getSubsectors: vi.fn(),
	getBuildTypes: vi.fn(),
	addAttachedFile: vi.fn(),
	removeAttachedFile: vi.fn(),
	replaceAttachedFiles: vi.fn(),
	generateSasUrlForBlob: vi.fn(),
} as unknown as IHowToRepository;

const mockAzureBlobService = {
	generateUploadPresignedUrl: vi.fn(),
	generateBatchUploadPresignedUrls: vi.fn(),
	deleteFileByUrl: vi.fn(),
	deleteMultipleFiles: vi.fn(),
	fileExists: vi.fn(),
	uploadFile: vi.fn(),
} as unknown as AzureBlobService;

// Mock request/response objects
const mockRequest = (body = {}, params = {}, query = {}) =>
	({
		body,
		params,
		query,
	}) as Request;

const mockResponse = () => {
	const res = {} as Response;
	res.status = vi.fn().mockReturnValue(res);
	res.json = vi.fn().mockReturnValue(res);
	return res;
};

const mockNext = vi.fn() as NextFunction;

describe("HowToController", () => {
	let controller: HowToController;
	let req: Request;
	let res: Response;
	let next: NextFunction;

	beforeEach(() => {
		vi.clearAllMocks();
		controller = new HowToController(mockHowToRepository, mockAzureBlobService);
		res = mockResponse();
		next = mockNext;
	});

	describe("getAllHowTos", () => {
		it("should return all HowTos with default pagination", async () => {
			const mockResult = {
				documents: [{ _id: "1", title: "Test HowTo" }],
				pagination: { page: 1, limit: 20, total: 1, pages: 1 },
			};

			(mockHowToRepository.findAll as MockedFunction<any>).mockResolvedValue(
				mockResult,
			);
			req = mockRequest({}, {}, {});

			await controller.getAllHowTos(req, res, next);

			expect(mockHowToRepository.findAll).toHaveBeenCalledWith(1, 20);
			expect(res.status).toHaveBeenCalledWith(200);
			expect(res.json).toHaveBeenCalledWith({ data: mockResult });
		});

		it("should handle custom pagination parameters", async () => {
			const mockResult = {
				documents: [],
				pagination: { page: 2, limit: 10, total: 0, pages: 0 },
			};

			(mockHowToRepository.findAll as MockedFunction<any>).mockResolvedValue(
				mockResult,
			);
			req = mockRequest({}, {}, { page: "2", limit: "10" });

			await controller.getAllHowTos(req, res, next);

			expect(mockHowToRepository.findAll).toHaveBeenCalledWith(2, 10);
		});

		it("should handle errors", async () => {
			const error = new Error("Database error");
			(mockHowToRepository.findAll as MockedFunction<any>).mockRejectedValue(
				error,
			);
			req = mockRequest();

			await controller.getAllHowTos(req, res, next);

			expect(next).toHaveBeenCalledWith(error);
		});
	});

	describe("getHowToById", () => {
		const validId = new Types.ObjectId().toString();

		it("should return HowTo by valid ID", async () => {
			const mockHowTo = { _id: validId, title: "Test HowTo" };
			(mockHowToRepository.findById as MockedFunction<any>).mockResolvedValue(
				mockHowTo,
			);
			req = mockRequest({}, { id: validId });

			await controller.getHowToById(req, res, next);

			expect(mockHowToRepository.findById).toHaveBeenCalledWith(validId);
			expect(res.status).toHaveBeenCalledWith(200);
			expect(res.json).toHaveBeenCalledWith({ data: mockHowTo });
		});

		it("should return 404 when HowTo not found", async () => {
			(mockHowToRepository.findById as MockedFunction<any>).mockResolvedValue(
				null,
			);
			req = mockRequest({}, { id: validId });

			await controller.getHowToById(req, res, next);

			expect(res.status).toHaveBeenCalledWith(404);
			expect(res.json).toHaveBeenCalledWith({ error: "HowTo not found" });
		});

		it("should return 400 for invalid ID format", async () => {
			req = mockRequest({}, { id: "invalid-id" });

			await controller.getHowToById(req, res, next);

			expect(res.status).toHaveBeenCalledWith(400);
			expect(res.json).toHaveBeenCalledWith({
				error: "Validation error",
				details: expect.any(Array),
			});
		});
	});

	describe("createHowTo", () => {
		const validHowToData = {
			title: "Test HowTo",
			description: "Test description",
			taxonomy: {
				sector: { id: "sector1", label: "Sector 1" },
			},
		};

		it("should create HowTo successfully", async () => {
			const mockCreatedHowTo = { _id: "new-id", ...validHowToData };
			(mockHowToRepository.create as MockedFunction<any>).mockResolvedValue(
				mockCreatedHowTo,
			);
			req = mockRequest(validHowToData);

			await controller.createHowTo(req, res, next);

			expect(mockHowToRepository.create).toHaveBeenCalledWith(validHowToData);
			expect(res.status).toHaveBeenCalledWith(201);
			expect(res.json).toHaveBeenCalledWith({ data: mockCreatedHowTo });
		});

		it("should create HowTo with attached files", async () => {
			const dataWithFiles = {
				...validHowToData,
				attachedFiles: [
					{ url: "https://example.com/file.pdf", label: "Test File" },
				],
			};
			const mockCreatedHowTo = { _id: "new-id", ...validHowToData };
			const mockUpdatedHowTo = {
				...mockCreatedHowTo,
				attachedFiles: dataWithFiles.attachedFiles,
			};

			(mockHowToRepository.create as MockedFunction<any>).mockResolvedValue(
				mockCreatedHowTo,
			);
			(
				mockHowToRepository.replaceAttachedFiles as MockedFunction<any>
			).mockResolvedValue(mockUpdatedHowTo);
			req = mockRequest(dataWithFiles);

			await controller.createHowTo(req, res, next);

			expect(mockHowToRepository.create).toHaveBeenCalledWith(validHowToData);
			expect(mockHowToRepository.replaceAttachedFiles).toHaveBeenCalledWith(
				"new-id",
				dataWithFiles.attachedFiles,
			);
			expect(res.status).toHaveBeenCalledWith(201);
			expect(res.json).toHaveBeenCalledWith({ data: mockUpdatedHowTo });
		});

		it("should return 400 for invalid data", async () => {
			req = mockRequest({ title: "" }); // Invalid: empty title

			await controller.createHowTo(req, res, next);

			expect(res.status).toHaveBeenCalledWith(400);
			expect(res.json).toHaveBeenCalledWith({
				error: "Validation error",
				details: expect.any(Array),
			});
		});
	});

	describe("updateHowTo", () => {
		const validId = new Types.ObjectId().toString();
		const updateData = { title: "Updated Title" };

		it("should update HowTo successfully", async () => {
			const mockUpdatedHowTo = { _id: validId, ...updateData };
			(mockHowToRepository.update as MockedFunction<any>).mockResolvedValue(
				mockUpdatedHowTo,
			);
			req = mockRequest(updateData, { id: validId });

			await controller.updateHowTo(req, res, next);

			expect(mockHowToRepository.update).toHaveBeenCalledWith(
				validId,
				updateData,
			);
			expect(res.status).toHaveBeenCalledWith(200);
			expect(res.json).toHaveBeenCalledWith({ data: mockUpdatedHowTo });
		});

		it("should return 404 when HowTo not found", async () => {
			(mockHowToRepository.update as MockedFunction<any>).mockResolvedValue(
				null,
			);
			req = mockRequest(updateData, { id: validId });

			await controller.updateHowTo(req, res, next);

			expect(res.status).toHaveBeenCalledWith(404);
			expect(res.json).toHaveBeenCalledWith({ error: "HowTo not found" });
		});
	});

	describe("deleteHowTo", () => {
		const validId = new Types.ObjectId().toString();

		it("should delete HowTo successfully", async () => {
			(mockHowToRepository.delete as MockedFunction<any>).mockResolvedValue(
				true,
			);
			req = mockRequest({}, { id: validId });

			await controller.deleteHowTo(req, res, next);

			expect(mockHowToRepository.delete).toHaveBeenCalledWith(validId);
			expect(res.status).toHaveBeenCalledWith(200);
			expect(res.json).toHaveBeenCalledWith({
				message: "HowTo deleted successfully",
			});
		});

		it("should return 404 when HowTo not found", async () => {
			(mockHowToRepository.delete as MockedFunction<any>).mockResolvedValue(
				false,
			);
			req = mockRequest({}, { id: validId });

			await controller.deleteHowTo(req, res, next);

			expect(res.status).toHaveBeenCalledWith(404);
			expect(res.json).toHaveBeenCalledWith({ error: "HowTo not found" });
		});
	});

	describe("generateUploadPresignedUrl", () => {
		const validRequest = {
			fileName: "test.pdf",
			fileType: "application/pdf",
		};

		it("should generate presigned URL successfully", async () => {
			const mockResult = {
				uploadUrl: "https://example.com/upload",
				blobUrl: "https://example.com/blob",
			};
			(
				mockAzureBlobService.generateUploadPresignedUrl as MockedFunction<any>
			).mockResolvedValue(mockResult);
			req = mockRequest(validRequest);

			await controller.generateUploadPresignedUrl(req, res, next);

			expect(
				mockAzureBlobService.generateUploadPresignedUrl,
			).toHaveBeenCalledWith(
				validRequest.fileName,
				validRequest.fileType,
				undefined,
			);
			expect(res.status).toHaveBeenCalledWith(200);
			expect(res.json).toHaveBeenCalledWith({
				message: "Upload presigned URL generated successfully",
				data: mockResult,
			});
		});

		it("should return 400 for invalid request", async () => {
			req = mockRequest({ fileName: "" }); // Invalid: empty fileName

			await controller.generateUploadPresignedUrl(req, res, next);

			expect(res.status).toHaveBeenCalledWith(400);
			expect(res.json).toHaveBeenCalledWith({
				error: "Validation error",
				details: expect.any(Array),
			});
		});
	});

	describe("searchHowTos", () => {
		it("should search HowTos successfully", async () => {
			const mockResult = {
				documents: [{ _id: "1", title: "Search Result" }],
				pagination: { page: 1, limit: 20, total: 1, pages: 1 },
			};
			(mockHowToRepository.search as MockedFunction<any>).mockResolvedValue(
				mockResult,
			);
			req = mockRequest({}, {}, { searchTerm: "test", page: "1", limit: "20" });

			await controller.searchHowTos(req, res, next);

			expect(mockHowToRepository.search).toHaveBeenCalledWith({
				searchTerm: "test",
				page: 1,
				limit: 20,
			});
			expect(res.status).toHaveBeenCalledWith(200);
			expect(res.json).toHaveBeenCalledWith({ data: mockResult });
		});

		it("should return 400 for missing search term", async () => {
			req = mockRequest({}, {}, {}); // Missing searchTerm

			await controller.searchHowTos(req, res, next);

			expect(res.status).toHaveBeenCalledWith(400);
			expect(res.json).toHaveBeenCalledWith({
				error: "Validation error",
				details: expect.any(Array),
			});
		});
	});

	describe("getSectors", () => {
		it("should return sectors successfully", async () => {
			const mockSectors = [{ id: "sector1", label: "Sector 1" }];
			(mockHowToRepository.getSectors as MockedFunction<any>).mockResolvedValue(
				mockSectors,
			);
			req = mockRequest();

			await controller.getSectors(req, res, next);

			expect(mockHowToRepository.getSectors).toHaveBeenCalled();
			expect(res.status).toHaveBeenCalledWith(200);
			expect(res.json).toHaveBeenCalledWith({ data: mockSectors });
		});
	});

	describe("getSubsectors", () => {
		it("should return subsectors for valid sector ID", async () => {
			const mockSubsectors = [{ id: "subsector1", label: "Subsector 1" }];
			(
				mockHowToRepository.getSubsectors as MockedFunction<any>
			).mockResolvedValue(mockSubsectors);
			req = mockRequest({}, {}, { sectorId: "sector1" });

			await controller.getSubsectors(req, res, next);

			expect(mockHowToRepository.getSubsectors).toHaveBeenCalledWith("sector1");
			expect(res.status).toHaveBeenCalledWith(200);
			expect(res.json).toHaveBeenCalledWith({ data: mockSubsectors });
		});

		it("should return 400 when sector ID is missing", async () => {
			req = mockRequest({}, {}, {});

			await controller.getSubsectors(req, res, next);

			expect(res.status).toHaveBeenCalledWith(400);
			expect(res.json).toHaveBeenCalledWith({ error: "Sector ID is required" });
		});
	});

	describe("getBuildTypes", () => {
		it("should return build types for valid sector and subsector IDs", async () => {
			const mockBuildTypes = [{ id: "buildtype1", label: "Build Type 1" }];
			(
				mockHowToRepository.getBuildTypes as MockedFunction<any>
			).mockResolvedValue(mockBuildTypes);
			req = mockRequest(
				{},
				{},
				{ sectorId: "sector1", subsectorId: "subsector1" },
			);

			await controller.getBuildTypes(req, res, next);

			expect(mockHowToRepository.getBuildTypes).toHaveBeenCalledWith(
				"sector1",
				"subsector1",
			);
			expect(res.status).toHaveBeenCalledWith(200);
			expect(res.json).toHaveBeenCalledWith({ data: mockBuildTypes });
		});

		it("should return 400 when required IDs are missing", async () => {
			req = mockRequest({}, {}, { sectorId: "sector1" }); // Missing subsectorId

			await controller.getBuildTypes(req, res, next);

			expect(res.status).toHaveBeenCalledWith(400);
			expect(res.json).toHaveBeenCalledWith({
				error: "Sector ID and Subsector ID are required",
			});
		});
	});
});
