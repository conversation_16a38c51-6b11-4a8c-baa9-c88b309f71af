import http, { type Server as HttpServer } from "node:http";
import config from "@/config/config";
import logger from "@/utils/logging";
import { PrismaClient } from "@prisma/client";
import app from "./app";
import { connectMongo, disconnectMongo } from "./database/mongo.connection";
import { db } from "./database/postgres.connection";
export const prisma = new PrismaClient();

export async function startServer(port: number): Promise<HttpServer> {
	// Connect to MongoDB using DatabaseManager
	const mongoUri = config.database.mongo.uri;
	const productivityUri = config.database.mongo.productivityUri;

	// Connect to the main database using the default connection
	await connectMongo(mongoUri);
	await connectMongo(productivityUri, "productivity");
	logger.info("MongoDB connected");

	// You can add more database connections here if needed
	// Example: await connectMongo(`${mongoUri}/analytics`, "analytics");

	// Connect to Postgres
	await db.query("SELECT NOW()");
	logger.info("Postgres connected");

	// Connect to Prisma
	await prisma.$connect();
	logger.info("Prisma connected");

	// Start HTTP server
	const server = http.createServer(app);
	await new Promise<void>((resolve) => server.listen(port, resolve));
	logger.info(`Server running on port ${port}`);

	// Graceful shutdown
	process.on("SIGINT", async () => {
		await stopServer(server);
		process.exit(0);
	});

	return server;
}

export async function stopServer(server: HttpServer) {
	await new Promise<void>((resolve, reject) => {
		server.close((err) => (err ? reject(err) : resolve()));
	});
	await disconnectMongo();
	await db.end();
	await prisma.$disconnect();
	logger.info("All connections closed, server stopped");
}
