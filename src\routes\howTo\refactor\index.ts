import { BlobServiceClient } from "@azure/storage-blob";
import { Router } from "express";
import multer from "multer";
import { HowToController } from "../../../controllers/howTo/refactor/howTo.controller";
import { AzureBlobService } from "../../../services/file/azureBlob.service";
import { howToService } from "../../../services/howTo/refactored/howTo.service";

// Import Swagger documentation
import "./swagger.docs";

// Configure multer for file uploads (only for legacy endpoints)
const upload = multer({
	storage: multer.memoryStorage(),
	limits: {
		fileSize: 10 * 1024 * 1024, // 10MB limit
	},
});

const router = Router();

// --- Azure Blob Service Setup ---
// IMPORTANT: Ensure AZURE_STORAGE_CONNECTION_STRING is set in your environment variables
const AZURE_STORAGE_CONNECTION_STRING =
	process.env.AZURE_STORAGE_CONNECTION_STRING;

if (!AZURE_STORAGE_CONNECTION_STRING) {
	throw new Error(
		"Azure Storage Connection String not found. Please set AZURE_STORAGE_CONNECTION_STRING environment variable.",
	);
}

const blobServiceClient = BlobServiceClient.fromConnectionString(
	AZURE_STORAGE_CONNECTION_STRING,
);

const howToAzureBlobService = new AzureBlobService(
	blobServiceClient,
	"howto-attachments",
);

// Separate service for public image uploads
const howToImagesBlobService = new AzureBlobService(
	blobServiceClient,
	"howto-images",
);

// --- End Azure Blob Service Setup ---

// Initialize controller with dependencies
const howToController = new HowToController(
	howToService,
	howToAzureBlobService,
);

// Create a separate controller instance for image uploads using the public container
const howToImageController = new HowToController(
	howToService,
	howToImagesBlobService,
);

// =====================================================
// PRESIGNED URL ROUTES (New optimized approach)
// =====================================================

// Generate presigned URL for single file upload
router.post(
	"/upload/presigned-url",
	howToController.generateUploadPresignedUrl,
);

// Generate presigned URLs for batch file uploads
router.post(
	"/upload/batch-presigned-urls",
	howToController.generateBatchUploadPresignedUrls,
);

// Delete single file from Azure Blob Storage
router.delete("/files/delete", howToController.deleteFile);

// Delete multiple files from Azure Blob Storage
router.delete("/files/delete-batch", howToController.deleteMultipleFiles);

// Check if a file exists in Azure Blob Storage
router.get("/files/exists", howToController.checkFileExists);

// =====================================================
// HOWTO ROUTES
// =====================================================

// Basic CRUD routes (no multer needed - files handled via presigned URLs)
router.get("/", howToController.getAllHowTos);
router.get("/:id", howToController.getHowToById);
router.post("/", howToController.createHowTo);
router.put("/:id", howToController.updateHowTo);
router.delete("/:id", howToController.deleteHowTo);

// Filter and search routes
router.post("/filter/", howToController.createTemplate);
router.get("/filter/filtered/tree", howToController.getFilteredHowTosTree);
router.get("/filter/filtered", howToController.getFilteredHowTos);
router.get("/filter/by-parent", howToController.getHowTosByParentId);
router.get("/filter/search", howToController.searchHowTos);
router.get("/filter/recent", howToController.getRecentHowTos);

// Taxonomy/filter option routes
router.get("/taxonomy/sectors", howToController.getSectors);
router.get("/taxonomy/subsectors", howToController.getSubsectors);
router.get("/taxonomy/build-types", howToController.getBuildTypes);

// File management routes (no multer needed)
// Note: File attachments are now handled directly in the updateHowTo endpoint
// router.post("/:id/files", howToController.addAttachedFile);
// router.delete("/:id/files", howToController.removeAttachedFile);
router.get("/files/sas-url", howToController.generateSasUrl);

// =====================================================
// LEGACY ROUTES (Deprecated - use presigned URLs instead)
// =====================================================

// Legacy single file upload (kept for backward compatibility)
router.post(
	"/upload-image",
	upload.single("file"),
	howToImageController.uploadImageToAzure,
);

export default router;
