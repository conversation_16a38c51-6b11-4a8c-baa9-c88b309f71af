import z from 'zod';

const fileUploadSchema = z.object({
  type: z.enum(['file', 'url']),
  value: z.string(),
  fileName: z.string().nullable(),
  fileType: z.string().nullable(),
  fileSize: z.number().nullable(),
  isUrl: z.boolean(),
});

const createProjectInputSchema = z.object({
  projectId: z.string({ required_error: 'Project ID is required' }),
  projectName: z
    .string({ required_error: 'Project name is required' })
    .min(1, { message: 'Project name cannot be empty' }),
  projectDescription: z
    .string({ required_error: 'Project description is required' })
    .min(1, { message: 'Project description cannot be empty' }),
  sector: z
    .string({ required_error: 'Sector is required' })
    .min(1, { message: 'Sector is required' }),
  subSector: z.string().optional(),
  sourceOfConstructionCost: z
    .string({ required_error: 'Source of construction cost is required' })
    .min(1, { message: 'Source of construction cost is required' }),
  levelOfEstimate: z
    .string({ required_error: 'Level of estimate is required' })
    .min(1, { message: 'Level of estimate is required' }),
  procurementModel: z.string().optional(),
  yearOfHeadContractNUM: z.string().optional(),
  landType: z.string().optional(),
  siteArea: z.string().optional(),
  fullyEnclosedCoveredArea: z.string().optional(),
  unenclosedCoveredArea: z.string().optional(),
  sectorSpecificQuestions: z.array(
    z.object({
      _id: z.string(),
      sector_code: z.string(),
      question: z.string(),
      answer: z.string(),
    })
  ),
  subSectorSpecificQuestions: z.array(
    z.object({
      _id: z.string(),
      sub_sector: z.string(),
      question: z.string(),
      answer: z.string(),
    })
  ),
  uploadEstimate: fileUploadSchema.optional(),
  uploadOriginalEstimate: fileUploadSchema.optional(),
});

const filterProjectsSchema = z.object({
  sector: z.string().optional(),
  subSector: z.string().optional(),
  location: z.string().optional(),
  earliestYear: z.string().optional(),
  sourceOfConstructionCost: z.string().optional(),
  classEstimate: z.string().optional(),
  projectCode: z.string().optional(),
  entryId: z.string().optional(),
  sectorSpecificAnswers: z
    .array(
      z.object({
        _id: z.string(),
        sector_code: z.string(),
        question: z.string(),
        answer: z.string(),
      })
    )
    .optional(),
  subSectorSpecificAnswers: z
    .array(
      z.object({
        _id: z.string(),
        sub_sector: z.string(),
        question: z.string(),
        answer: z.string(),
      })
    )
    .optional(),
});

const benchmarkRequestSchema = z.object({
  projects: z.array(
    z.object({
      projectCode: z.string({ required_error: 'Project code is required' }),
      level: z.string({ required_error: 'Level is required' }),
    })
  ).min(1, {
    message: 'At least one project is required',
  }),
});

const benchmarkForProjectRequestSchema = z.object({
  projectId: z.string({ required_error: 'Project ID is required' }),
});

export {
  benchmarkForProjectRequestSchema,
  benchmarkRequestSchema,
  createProjectInputSchema,
  filterProjectsSchema,
};

// Type inference
export type CreateProjectInput = z.infer<typeof createProjectInputSchema>;
export type FileUpload = z.infer<typeof fileUploadSchema>;
