import { ProjectModel } from "@/models/project.model";
import { MapLocationModel } from "@/models/map.model";
import logger from "@/utils/logging";
import type { MapRepository } from "./map.repository";
import type { ProjectDocument, FiltersResponse } from "@/services/maps/map.types";
import { STATIC_SERVICE_OFFERINGS } from "@/services/maps/map.constants";

export class MongoMapRepository implements MapRepository {
	/**
	 * Get available filters for projects
	 */
	async getFilters(): Promise<FiltersResponse> {
		const count = await this.countDocuments({
			latlng: { $exists: true, $ne: null },
		});
		const companies = await this.getDistinctValues("company", {
			company: { $ne: null },
		});
		const locations = await this.getDistinctValues("location", {
			location: { $ne: null },
		});
		const primaryMarkets = await this.getDistinctValues("primarymarket", {
			primarymarket: { $ne: null },
		});
		const projectManagers = await this.getDistinctValues("projectmanager", {
			projectmanager: { $ne: null },
		});
		const projectDirectors = await this.getDistinctValues("projectdirector", {
			projectdirector: { $ne: null },
		});

		// Use constant for service offerings
		const serviceOffering = STATIC_SERVICE_OFFERINGS;

		// Revenue ranges with labels
		const plannedRevenue = [
			{ value: "OVER_1M", label: "Over $1M" },
			{ value: "500K_1M", label: "$500K to $1M" },
			{ value: "250K_500K", label: "$250K to $500K" },
			{ value: "UNDER_250K", label: "Under $250K" },
		];

		// Status options
		const status = ["ACTIVE", "PLANNED", "COMPLETED"];

		return {
			count,
			company: companies.sort(),
			city: locations.sort(),
			industry: primaryMarkets.sort(),
			serviceOffering,
			plannedRevenue,
			status,
			projectManager: projectManagers.sort(),
			projectDirector: projectDirectors.sort(),
		};
	}

	/**
	 * Find projects matching the given filters
	 */
	async findFilteredProjects(
		query: Record<string, unknown>,
		projection: Record<string, number>,
		limit: number,
	): Promise<ProjectDocument[]> {
		try {
			const projects = await ProjectModel.find(query, projection)
				.limit(limit)
				.lean();
			return projects as unknown as ProjectDocument[];
		} catch (error) {
			logger.error("Error finding filtered projects:", { error });
			throw error;
		}
	}

	/**
	 * Find a project by its ID
	 */
	async findProjectById(
		projectId: string,
		projection: Record<string, number>,
	): Promise<ProjectDocument | null> {
		try {
			const project = await ProjectModel.findOne(
				{ projectid: projectId },
				projection,
			).lean();
			return project as unknown as ProjectDocument | null;
		} catch (error) {
			logger.error("Error finding project by ID:", { error, projectId });
			throw error;
		}
	}

	/**
	 * Find a map location by project ID
	 */
	async findMapLocationByProjectId(
		projectId: string,
		projection: Record<string, number>,
	): Promise<Record<string, any> | null> {
		try {
			const mapLocation = await MapLocationModel.findOne(
				{ "Project ID": projectId },
				projection,
			).lean();
			return mapLocation;
		} catch (error) {
			logger.error("Error finding map location by project ID:", {
				error,
				projectId,
			});
			throw error;
		}
	}

	/**
	 * Get distinct values for a field
	 */
	async getDistinctValues(
		field: string,
		query?: Record<string, unknown>,
	): Promise<string[]> {
		try {
			return await ProjectModel.distinct(field, query || {});
		} catch (error) {
			logger.error(`Error getting distinct values for ${field}:`, { error });
			throw error;
		}
	}

	/**
	 * Count documents matching a query
	 */
	async countDocuments(query: Record<string, unknown>): Promise<number> {
		try {
			return await ProjectModel.countDocuments(query);
		} catch (error) {
			logger.error("Error counting documents:", { error });
			throw error;
		}
	}
}
