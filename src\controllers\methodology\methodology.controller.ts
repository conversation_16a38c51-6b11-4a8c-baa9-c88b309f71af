import { Request, Response, NextFunction } from 'express';
import { MethodologyService } from '@/services/methodology.service';
import {
  createMethodologySchema,
  createNodeSchema,
  createStepSchema,
  updateNodeSchema,
  CreateMethodologyInput,
  CreateNodeInput,
  CreateStepInput,
  UpdateNodeInput,
  BulkUploadInput,
  getMethodologiesSchema,
} from './methodology.schema';
import { Types } from 'mongoose';

interface UploadFileBody {
  nodeId?: string;
  stepId?: string;
  docType: 'framework' | 'template' | 'example';
}

export class MethodologyController {
  private methodologyService: MethodologyService;

  constructor(methodologyService: MethodologyService) {
    this.methodologyService = methodologyService;
  }

  public getMethodologies = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { parentId } = getMethodologiesSchema.parse(req.query);
      const methodologies = await this.methodologyService.getMethodologies(parentId);
      res.status(200).json({ data: methodologies });
    } catch (error) {
      next(error);
    }
  };

  public createMethodology = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedData = createMethodologySchema.parse(req.body);
      const methodologyData: CreateMethodologyInput = {
        ...validatedData,
        parentId: validatedData.parentId ?? null,
      };
      const methodology = await this.methodologyService.createMethodology(methodologyData);
      res.status(201).json({ data: methodology });
    } catch (error) {
      next(error);
    }
  };

  public createNode = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedData: CreateNodeInput = createNodeSchema.parse(req.body);

      // Additional validation if needed
      if (validatedData.nodeType === 'step' && !validatedData.parentId) {
        throw new Error('Step nodes must have a parent');
      }

      if (
        validatedData.nodeType === 'regular' &&
        !validatedData.nextLevelName &&
        !validatedData.order
      ) {
        throw new Error('Regular nodes must have a nextLevelName and order');
      }
      console.log('validatedData', validatedData);

      const node = await this.methodologyService.createNode(validatedData);
      res.status(201).json({ data: node });
    } catch (error) {
      next(error);
    }
  };

  public createStep = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedData: CreateStepInput = createStepSchema.parse(req.body);
      const step = await this.methodologyService.createStep(validatedData);
      res.status(201).json({ data: step });
    } catch (error) {
      next(error);
    }
  };

  public uploadFile = async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.file) {
        throw new Error('No file uploaded');
      }
      const { nodeId, stepId, docType } = req.body as UploadFileBody;
      const file = await this.methodologyService.uploadFile({
        file: req.file as Express.Multer.File,
        nodeId: nodeId ? new Types.ObjectId(nodeId) : undefined,
        stepId: stepId ? new Types.ObjectId(stepId) : undefined,
        docType,
      });
      res.status(201).json({ data: file });
    } catch (error) {
      next(error);
    }
  };

  public bulkUploadFile = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { nodeIds, stepIds, docType, files } = req.body as BulkUploadInput & {
        files?: Array<{ originalFileName: string; url: string }>;
      };

      // Validate that at least one node or step is provided
      if ((!nodeIds || nodeIds.length === 0) && (!stepIds || stepIds.length === 0)) {
        throw new Error('At least one nodeId or stepId must be provided');
      }

      let results;
      if (req.files && Array.isArray(req.files) && req.files.length > 0) {
        // If files were uploaded via multer then process as before.
        results = await this.methodologyService.bulkUploadFile({
          files: req.files as Express.Multer.File[],
          nodeIds: nodeIds?.map((id: string) => new Types.ObjectId(id)),
          stepIds: stepIds?.map((id: string) => new Types.ObjectId(id)),
          docType,
        });
      } else if (files && Array.isArray(files) && files.length > 0) {
        // If file metadata is provided instead of file uploads, handle accordingly.
        results = await this.methodologyService.bulkSaveFileMetadata({
          files,
          nodeIds: nodeIds?.map((id: string) => new Types.ObjectId(id)),
          stepIds: stepIds?.map((id: string) => new Types.ObjectId(id)),
          docType,
        });
      } else {
        throw new Error('No files or file metadata provided');
      }

      res.status(201).json({ data: results });
    } catch (error) {
      next(error);
    }
  };

  public getAllMethodologies = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const methodologies = await this.methodologyService.getAllMethodologies();
      res.status(200).json({ data: methodologies });
    } catch (error) {
      next(error);
    }
  };

  public deleteFile = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { fileId, nodeId } = req.body as { fileId: string; nodeId: string };
      if (!fileId || !nodeId) {
        throw new Error('File ID and node ID are required');
      }
      await this.methodologyService.deleteFile(
        new Types.ObjectId(fileId),
        new Types.ObjectId(nodeId)
      );
      res.status(200).json({ message: 'File deleted successfully' });
    } catch (error) {
      next(error);
    }
  };

  public deleteNode = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { nodeId } = req.params;

      if (!nodeId) {
        throw new Error('Node ID is required');
      }

      await this.methodologyService.deleteNode(new Types.ObjectId(nodeId));

      res.status(200).json({
        message: 'Node deleted successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  public updateNode = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { nodeId } = req.params;

      if (!nodeId) {
        throw new Error('Node ID is required');
      }

      // Validate the update data
      const validatedData = updateNodeSchema.parse(req.body);

      const updatedNode = await this.methodologyService.updateNode(
        new Types.ObjectId(nodeId),
        validatedData
      );

      res.status(200).json({ data: updatedNode });
    } catch (error) {
      next(error);
    }
  };

  public updateMethodology = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;

      if (!id) {
        throw new Error('Methodology ID is required');
      }

      // Validate the update data
      const validatedData = createMethodologySchema.parse(req.body);

      const updatedMethodology = await this.methodologyService.updateMethodology(
        new Types.ObjectId(id),
        validatedData
      );

      res.status(200).json({ data: updatedMethodology });
    } catch (error) {
      next(error);
    }
  };

  public deleteFileByParams = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { nodeId, fileId } = req.params;

      if (!fileId || !nodeId) {
        throw new Error('File ID and node ID are required');
      }

      await this.methodologyService.deleteFile(
        new Types.ObjectId(fileId),
        new Types.ObjectId(nodeId)
      );

      res.status(200).json({ message: 'File deleted successfully' });
    } catch (error) {
      next(error);
    }
  };

  public uploadMethodologyFile = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { methodologyId } = req.params;

      if (!methodologyId) {
        throw new Error('Methodology ID is required');
      }

      const docType = req.body.docType as 'framework' | 'template' | 'example';
      if (!docType || !['framework', 'template', 'example'].includes(docType)) {
        throw new Error('Valid docType is required (framework, template, or example)');
      }

      let fileData;

      // Handle file upload
      if (req.file) {
        fileData = await this.methodologyService.uploadMethodologyFile({
          methodologyId: new Types.ObjectId(methodologyId),
          file: req.file,
          docType,
        });
      }
      // Handle URL link
      else if (req.body.url && req.body.originalFileName) {
        fileData = await this.methodologyService.uploadMethodologyFile({
          methodologyId: new Types.ObjectId(methodologyId),
          url: req.body.url,
          originalFileName: req.body.originalFileName,
          docType,
        });
      } else {
        throw new Error('Either a file or URL with originalFileName must be provided');
      }

      res.status(201).json({ data: fileData });
    } catch (error) {
      next(error);
    }
  };
}
