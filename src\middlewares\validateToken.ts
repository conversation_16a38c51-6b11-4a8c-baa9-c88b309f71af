import { HttpException } from "@/utils/exceptions/http.exception";
import type * as msal from "@azure/msal-node";
import type { NextFunction, Request, Response } from "express";
import jwt from "jsonwebtoken";
import UserModel from "../models/user.model";
import logger from "../utils/logging";

interface AzureADPayload extends jwt.JwtPayload {
	name?: string;
	unique_name?: string; // Azure AD Object ID
	preferred_username?: string; // User's email or username
}

export class TokenMiddleware {
	private static instance: TokenMiddleware;
	private msalConfig: msal.Configuration;

	private constructor() {
		this.msalConfig = {
			auth: {
				clientId: process.env.AZURE_CLIENT_ID || "",
				authority: `https://login.microsoftonline.com/${process.env.AZURE_TENANT_ID}`,
				clientSecret: process.env.AZURE_CLIENT_SECRET,
			},
		};
	}

	public static getInstance(): TokenMiddleware {
		if (!TokenMiddleware.instance) {
			TokenMiddleware.instance = new TokenMiddleware();
		}
		return TokenMiddleware.instance;
	}

	public validateToken() {
		return async (
			req: Request,
			res: Response,
			next: NextFunction,
		): Promise<void> => {
			try {
				const authHeader = req.headers.authorization;
				// Bypass token validation in test environment
				if (process.env.NODE_ENV === "test") {
					return next();
				}
				if (!authHeader?.startsWith("Bearer ")) {
					return next(new HttpException(401, "No token provided"));
				}

				const token = authHeader.split(" ")[1];
				const decodedToken = jwt.decode(token) as AzureADPayload;

				if (!decodedToken?.name) {
					return next(new HttpException(401, "Invalid token"));
				}

				// Add user info to request
				const azureAdObjectId = decodedToken.unique_name;
				if (!azureAdObjectId) {
					return next(
						new HttpException(401, "Invalid token: missing unique_name"),
					);
				}

				req.headers["x-azure-ad-object-id"] = azureAdObjectId;

				// Check if user exists in the database, if not, create a new user
				try {
					const existingUser = await UserModel.findOne({
						azureAdObjectId,
					}).lean();

					if (!existingUser) {
						logger.info(
							`User ${azureAdObjectId} not found in database. Creating new user.`,
						);

						// Create a new user with no roles assigned
						const newUser = await UserModel.create({
							azureAdObjectId,
							roles: [],
						});

						logger.info(
							`User ${azureAdObjectId} created successfully with ID: ${newUser._id}`,
						);
					}
				} catch (userError) {
					logger.error("Error checking/creating user:", {
						error: userError,
						azureAdObjectId,
					});
					// We don't want to block the request if user creation fails
					// Just log the error and continue
				}

				next();
			} catch (error) {
				logger.error("Token validation failed", { error });
				next(error);
			}
		};
	}
}
