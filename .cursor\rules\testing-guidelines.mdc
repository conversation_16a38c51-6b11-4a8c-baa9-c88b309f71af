---
description: 
globs: 
alwaysApply: true
---
# Testing Guidelines

This project emphasizes testability through its architectural choices and code organization. Follow these guidelines when implementing tests. We are using vitest and supertest for testing. 

## Test Structure

Tests should follow the same organizational structure as the source code:

```
services/
└── domain-name/
    ├── __tests__/
        ├── domain.service.test.ts
        ├── domain.controller.test.ts
        ├── mongoDomain.repository.test.ts
```

## Unit Testing

Unit tests should:

- Test one unit of functionality in isolation
- Mock external dependencies
- Use dependency injection to facilitate mocking
- Focus on testing behavior, not implementation details

Example of unit testing a service:
```typescript
// Mock dependencies
const mockFileRepo = {...};
const mockBlobService = {...};

// Inject mocks
const fileService = new FileService(mockFileRepo, mockBlobService);

// Test behavior
const result = await fileService.uploadFile({...});
expect(result).toEqual(expected);
```

## Integration Testing

Integration tests should:

- Test interaction between multiple components
- Use test databases or environment-specific configurations
- Validate correct integration between services, repositories, and external systems
- Focus on boundaries and contracts

## Test Environment

- Use environment variables to configure test environment
- Ensure tests can run in CI/CD pipeline
- Avoid testing against production systems

## Mocking

- Use Jest mocks for unit testing
- Consider using containers for integration testing
- Mock external services (Azure, MongoDB) in unit tests

## Coverage

- Aim for high code coverage
- Focus on critical business logic
- Include positive and negative test cases
- Test edge cases and error handling

