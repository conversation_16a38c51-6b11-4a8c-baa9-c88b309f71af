import type { File } from "./file.model";
import type { FileRepository } from "./file.repository";
import { FileModel } from "./file.model";
export class MongoFileRepository implements FileRepository {
	async create(
		file: Omit<File, "id" | "createdAt" | "updatedAt" | "deletedAt">,
	): Promise<File> {
		const createdFile = new FileModel(file);
		await createdFile.save();
		return createdFile.toObject();
	}

	async findAll(params?: {
		userId?: string;
		limit?: number;
		offset?: number;
	}): Promise<File[]> {
		const query: Record<string, unknown> = {};
		if (params?.userId) {
			query.userId = params.userId;
		}
		const files = await FileModel.find(query)
			.sort({ createdAt: -1 })
			.skip(params?.offset || 0)
			.limit(params?.limit || 10)
			.lean();
		return files;
	}

	async findById(id: string): Promise<File | null> {
		const file = await FileModel.findById(id).lean();
		return file || null;
	}

	async findByUserId(userId: string): Promise<File[] | null> {
		const files = await FileModel.find({ userId }).lean();
		return files || null;
	}

	async findByStorageName(storageName: string): Promise<File | null> {
		const file = await FileModel.findOne({ storageName }).lean();
		return file || null;
	}

	async findByUrl(url: string): Promise<File | null> {
		const file = await FileModel.findOne({ url }).lean();
		return file || null;
	}

	async deleteById(id: string): Promise<File | null> {
		const deletedFile = await FileModel.findByIdAndDelete(id).lean();
		return deletedFile || null;
	}

	async updateById(id: string, file: Partial<File>): Promise<File | null> {
		const updatedFile = await FileModel.findByIdAndUpdate(id, file, {
			new: true,
		}).lean();
		return updatedFile || null;
	}

	async updateByUserId(
		userId: string,
		file: Partial<File>,
	): Promise<File | null> {
		const updatedFile = await FileModel.findOneAndUpdate({ userId }, file, {
			new: true,
		}).lean();
		return updatedFile || null;
	}
}
