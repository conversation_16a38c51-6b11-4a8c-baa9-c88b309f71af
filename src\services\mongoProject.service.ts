import type { Project } from "@/models/project.model";
import { ProjectModel } from "@/models/project.model";
import { getRandomColors } from "@/utils/colors";
import logger from "@/utils/logging";
import XLSX = require("xlsx"); // Import xlsx for Excel file generation
import type { ProjectRepository } from "./repositories/ProjectRepository";
import {
	FILTER_MAPPING,
	FRONTEND_MAPPING,
	type FilterMapping,
	type MarketShareData,
	type MarketShareRawData,
	type PaginatedResult,
	ProjectStatus,
} from "./repositories/types";

/**
 * Builds a MongoDB query object from a set of frontend filter inputs.
 *
 * Given a set of frontend filter inputs, this function will return a MongoDB query
 * object that can be used to fetch projects that match the given filters.
 *
 * If the filter is a numeric range (e.g. GFA), it will be converted to a MongoDB range
 * query. If the filter is a string or a string array (e.g. customer name, project name,
 * city, industry, service offering, planned revenue), it will be converted to a MongoDB
 * $in query.
 *
 * If the filter is a status filter, it will be converted to a MongoDB query that checks
 * for the given status.
 *
 * If the filter is a deselected projects filter, it will be converted to a MongoDB query
 * that excludes the given projects.
 *
 * @param filters - The frontend filter inputs
 * @param deselectedProjects - An optional array of project objects that should be excluded
 * from the results. Each project object should have a `projectId` and an `index` property.
 * @returns A MongoDB query object that can be used to fetch projects that match the given
 * filters.
 */
const buildFilterQuery = async (
	filters: Partial<Record<keyof FilterMapping, string[]>>,
	deselectedProjects?: Array<{ projectId: string; index: string }>,
): Promise<Record<string, unknown>> => {
	const query: Record<string, unknown> = {};
	const andConditions: Array<Record<string, unknown>> = [];

	// Add filter conditions
	for (const [key, values] of Object.entries(filters)) {
		if (!values?.length) {
			continue;
		}

		// Check if the key exists in the mapping
		if (!(key in FILTER_MAPPING)) {
			logger.warn(
				`Unknown filter key: ${key}. Available keys: ${Object.keys(FILTER_MAPPING).join(", ")}`,
			);
			continue;
		}

		const mongoField = FILTER_MAPPING[key as keyof FilterMapping];

		if (key === "plannedRevenue") {
			const revenueConditions: Record<string, unknown>[] = [];
			for (const range of values) {
				switch (range) {
					case "OVER_1M":
						revenueConditions.push({ [mongoField]: { $gte: 1000000 } });
						break;
					case "500K_1M":
						revenueConditions.push({
							[mongoField]: { $gte: 500000, $lt: 1000000 },
						});
						break;
					case "250K_500K":
						revenueConditions.push({
							[mongoField]: { $gte: 250000, $lt: 500000 },
						});
						break;
					case "UNDER_250K":
						revenueConditions.push({ [mongoField]: { $lt: 250000 } });
						break;
				}
			}
			if (revenueConditions.length > 0) {
				andConditions.push({ $or: revenueConditions });
			}
		} else if (key === "gfa") {
			// Handle GFA as a numeric range [low, high]
			if (Array.isArray(values) && values.length === 2) {
				const [lowValue, highValue] = values.map(Number);
				andConditions.push({
					gfa: {
						$gte: lowValue,
						$lte: highValue,
					},
				});
			}
		} else if (key === "status") {
			const now = new Date();
			for (const status of values) {
				switch (status) {
					case ProjectStatus.ACTIVE:
						andConditions.push({
							$or: [
								{ startdate: { $ne: null } },
								{ activedate: { $ne: null } },
							],
							enddate: { $gt: now },
						});
						break;
					case ProjectStatus.PLANNED:
						andConditions.push({
							startdate: null,
							activedate: null,
						});
						break;
					case ProjectStatus.COMPLETED:
						andConditions.push({
							$or: [
								{ startdate: { $ne: null } },
								{ activedate: { $ne: null } },
							],
							enddate: { $lte: now },
						});
						break;
				}
			}
		} else {
			// For regular string array filters, use $in operator
			andConditions.push({ [mongoField]: { $in: values } });
		}
	}

	// Add deselected projects condition
	if (deselectedProjects && deselectedProjects.length > 0) {
		const deselectedIds = deselectedProjects.map(
			(project) => project.projectId,
		);
		andConditions.push({
			projectid: { $nin: deselectedIds },
		});
	}

	// Add $and to query if we have any conditions
	if (andConditions.length > 0) {
		query.$and = andConditions;
	}

	return query;
};

export const createMongoProjectService = (): ProjectRepository => {
	/**
	 * Retrieve paginated projects.
	 * @param page The page number (starts at 1).
	 * @param limit The number of projects per page.
	 * @returns The paginated result with projects, total count, page number, limit, and total pages.
	 */
	const getProjects = async (
		page: number,
		limit: number,
	): Promise<PaginatedResult<Project>> => {
		try {
			const skip = (page - 1) * limit;

			const [total, projects] = await Promise.all([
				ProjectModel.countDocuments(),
				ProjectModel.find()
					.sort({ projectid: -1 })
					.skip(skip)
					.limit(limit)
					.lean(),
			]);

			const totalPages = Math.ceil(total / limit);

			return {
				data: projects as Project[],
				total,
				page,
				limit,
				totalPages,
			};
		} catch (error) {
			logger.error("Error fetching paginated projects", { error, page, limit });
			throw error;
		}
	};

	/**
	 * Retrieve a project by id.
	 * @param projectId The project id.
	 * @returns The project or null if not found.
	 */
	const getProjectById = async (projectId: string): Promise<Project | null> => {
		try {
			return await ProjectModel.findOne({ projectid: projectId }).lean();
		} catch (error) {
			logger.error("Error fetching project by id", { error, projectId });
			throw error;
		}
	};

	/**
	 * Get available project filters
	 * @returns The available project filters including count, company, city, industry, service offering, planned revenue, and status.
	 */
	const getAvailableProjectFilters = async (): Promise<{
		count: number;
		company: string[];
		city: string[];
		industry: string[];
		serviceOffering: string[];
		plannedRevenue: { value: string; label: string }[];
		status: string[];
		projectManager: string[];
		projectDirector: string[];
	}> => {
		try {
			// Count documents
			const count = await ProjectModel.countDocuments();
			// Get distinct companies (only non-null)
			const companies = await ProjectModel.distinct("company", {
				company: { $ne: null },
			});
			// Get distinct locations (non-null)
			const locations = await ProjectModel.distinct("location", {
				location: { $ne: null },
			});
			// Get distinct primary markets (non-null)
			const primaryMarkets = await ProjectModel.distinct("primarymarket", {
				primarymarket: { $ne: null },
			});
			// Get distinct project managers (non-null)
			const projectManagers = await ProjectModel.distinct("projectmanager", {
				projectmanager: { $ne: null },
			});
			// Get distinct project directors (non-null)
			const projectDirectors = await ProjectModel.distinct("projectdirector", {
				projectdirector: { $ne: null },
			});

			// Hard-code additional filters (as in your map service)
			const serviceOffering = [
				"Claims & Dispute Resolution",
				"IPC - Integrated Project Controls",
				"IPC - Cost",
				"IPC - Risk",
				"Planning & Scheduling",
				"Strategic Advisory Services",
			];
			const plannedRevenue = [
				{ value: "OVER_1M", label: "Over $1M" },
				{ value: "500K_1M", label: "$500K to $1M" },
				{ value: "250K_500K", label: "$250K to $500K" },
				{ value: "UNDER_250K", label: "Under $250K" },
			];
			const status = ["ACTIVE", "PLANNED", "COMPLETED"];

			return {
				count,
				company: companies,
				city: locations,
				industry: primaryMarkets,
				serviceOffering,
				plannedRevenue,
				status,
				projectManager: projectManagers,
				projectDirector: projectDirectors,
			};
		} catch (error) {
			logger.error("Error fetching available project filters", { error });
			throw error;
		}
	};

	/**
	 * Searches for projects, returning distinct results based on a specified field,
	 * while including associated project details. Supports pagination.
	 *
	 * @param {Object} params - The search parameters.
	 * @param {string} params.fieldName - The name of the field to ensure distinctness on.
	 * @param {number} [params.page=1] - The page number (starts at 1). Default is 1.
	 * @param {number} [params.limit=30] - The number of distinct items per page. Default is 30.
	 * @param {string} [params.queryTerm] - Optional search query to filter results based on the fieldName.
	 * @returns {Promise<Array<{ [key: string]: any; projectdirector?: string; projectname?: string }>>}
	 *          A promise resolving to an array of objects, each containing the distinct field value,
	 *          project director, and project name. The key for the distinct field value will be the
	 *          value of `fieldName`.
	 */
	const searchItemFilter = async ({
		fieldName,
		page = 1, // Default page to 1
		limit = 30, // Default limit to 30
		queryTerm,
	}: {
		fieldName: string;
		page?: number;
		limit?: number;
		queryTerm?: string;
		// Using a more specific return type based on the expected output structure
	}): Promise<
		Array<{
			[key: string]: any;
			projectdirector?: string | null;
			projectname?: string | null;
		}>
	> => {
		try {
			const query: Record<string, unknown> = {};

			// Build the match query based on the queryTerm for the specified fieldName
			if (queryTerm) {
				query[fieldName] = { $ne: null, $regex: queryTerm, $options: "i" };
			} else {
				// Ensure the field exists and is not null when searching without a specific term
				query[fieldName] = { $ne: null };
			}

			const skip = (page - 1) * limit;

			// Aggregation Pipeline
			const aggregationPipeline: any[] = [
				// 1. Match documents based on the initial query
				{ $match: query },
				// 2. Sort before grouping to potentially make $first predictable (optional)
				{ $sort: { [fieldName]: 1 } }, // You might adjust sorting based on requirements
				// 3. Group by the distinct fieldName
				{
					$group: {
						_id: `$${fieldName}`, // Group by the value of the dynamic field
						// Take the projectdirector and projectname from the first document in each group
						projectdirector: { $first: "$projectdirector" },
						projectname: { $first: "$projectname" },
					},
				},
				// 4. Project the results into the desired output structure
				{
					$project: {
						_id: 0, // Exclude the default _id field from the $group stage
						[fieldName]: "$_id", // Create a field with the dynamic name `fieldName` using the grouped value
						projectdirector: 1, // Include the projectdirector
						projectname: 1, // Include the projectname
					},
				},
				// 5. Sort the final distinct results (optional, e.g., by the fieldName)
				{ $sort: { [fieldName]: 1 } },
				// 6. Apply pagination (skip and limit)
				// Note: For large datasets, $facet might be more performant for getting total counts,
				// but for just pagination, $skip/$limit after grouping/projecting is often sufficient.
				{ $skip: skip },
				{ $limit: limit },
			];

			const distinctItems = await ProjectModel.aggregate(aggregationPipeline);

			// Return the array of distinct items with the requested structure
			return distinctItems;
		} catch (error) {
			logger.error("Error searching distinct item filter with aggregation", {
				error,
				fieldName,
				page,
				limit,
				queryTerm,
			});
			throw error; // Re-throw the error
		}
	};

	/**
	 * Retrieves projects filtered by the provided filter with pagination support.
	 * @param {Partial<Record<keyof FilterMapping, string[]>>} filters The filter to apply.
	 * @param {number} page The page number (starts at 1).
	 * @param {number} limit The number of projects per page.
	 * @returns {Promise<PaginatedResult<Project>>} The paginated filtered projects.
	 */
	const getProjectsByFilters = async (
		filters: Partial<Record<keyof FilterMapping, string[]>>,
		page = 1,
		limit = 10,
	): Promise<PaginatedResult<Project>> => {
		try {
			const query = await buildFilterQuery(filters);
			const skip = (page - 1) * limit;

			// Execute count and find in parallel for better performance
			const [total, projects] = await Promise.all([
				ProjectModel.countDocuments(query),
				ProjectModel.find(query, {
					projectid: 1,
					projectname: 1,
					customername: 1,
					location: 1,
					primarymarket: 1,
					servicename: 1,
					estconsfees: 1,
					// activedate: 1,
					// enddate: 1,
					gfa: 1,
					_id: 0,
				})
					.skip(skip)
					.limit(limit)
					.lean(),
			]);

			const totalPages = Math.ceil(total / limit);

			// Map only the fields specified in FilterMapping
			const mappedProjects = projects.map((project) => {
				const mappedProject: Record<string, unknown> = { ...project };

				for (const [dbField, frontendField] of Object.entries(
					FRONTEND_MAPPING,
				) as Array<[keyof typeof project, string]>) {
					if (dbField in project) {
						mappedProject[frontendField] = project[dbField];
						mappedProject[dbField] = undefined;
					}
				}

				return mappedProject as unknown as Project;
			});

			return {
				data: mappedProjects,
				total,
				page,
				limit,
				totalPages,
			};
		} catch (error) {
			logger.error("Error fetching filtered projects", {
				error,
				filters,
				page,
				limit,
			});
			throw error;
		}
	};

	/**
	 * Gets the market share percentages for the provided filters.
	 * @param {Partial<Record<keyof FilterMapping, string[]>>} filters The filter to apply.
	 * @param {Array<{ projectId: string; index: string }>} [deselectedProjects] The projects to exclude.
	 * @returns {Promise<MarketShareData[]>} The market share percentages.
	 */
	const getPercentageByMarketShare = async (
		filters: Partial<Record<keyof FilterMapping, string[]>>,
		deselectedProjects?: Array<{ projectId: string; index: string }>,
	): Promise<MarketShareData[]> => {
		try {
			const query = await buildFilterQuery(filters, deselectedProjects);

			// Add non-null primarymarket condition
			if (!query.$and) {
				query.$and = [];
			}
			(query.$and as Array<Record<string, unknown>>).push({
				primarymarket: { $ne: null },
			});

			const rawData = await ProjectModel.aggregate<MarketShareRawData>([
				{ $match: query },
				{
					$group: {
						_id: "$primarymarket",
						market_count: { $sum: 1 },
					},
				},
				{
					$group: {
						_id: null,
						marketData: { $push: "$$ROOT" },
						totalCount: { $sum: "$market_count" },
					},
				},
				{
					$project: {
						_id: 0,
						data: {
							$map: {
								input: "$marketData",
								as: "market",
								in: {
									primarymarket: "$$market._id",
									market_count: "$$market.market_count",
									percentage: {
										$round: [
											{
												$multiply: [
													{ $divide: ["$$market.market_count", "$totalCount"] },
													100,
												],
											},
											2,
										],
									},
								},
							},
						},
					},
				},
				{ $unwind: "$data" },
				{ $replaceRoot: { newRoot: "$data" } },
				{ $sort: { percentage: -1 } },
			]);

			// Get random colors for the data points
			const colors = getRandomColors(rawData.length);

			// Transform the data for the frontend
			return rawData.map((item, index) => ({
				label: item.primarymarket,
				value: item.market_count,
				color: colors[index],
				percentage: item.percentage,
			}));
		} catch (error) {
			logger.error("Error getting market share percentages", { error });
			throw error;
		}
	};

	/**
	 * Downloads filtered projects data as an Excel file
	 * @param {Partial<Record<keyof FilterMapping, string[]>>} filters The filter to apply
	 * @returns {Promise<{filename: string; data: Buffer}>} The Excel file data and filename
	 */
	const downloadFilteredProjects = async (
		filters: Partial<Record<keyof FilterMapping, string[]>>,
	): Promise<{ filename: string; data: Buffer }> => {
		try {
			// Use the existing filter query builder
			const query = await buildFilterQuery(filters);
			logger.info("Generated query for download:", query);

			// Fetch all matching projects without pagination
			// We need more fields for the Excel file than what's returned by getProjectsByFilters
			const projects = await ProjectModel.find(query, {
				projectid: 1,
				projectname: 1,
				customername: 1,
				location: 1,
				primarymarket: 1,
				servicename: 1,
				estconsfees: 1,
				projectdirector: 1,
				projectmanager: 1,
				startdate: 1,
				enddate: 1,
				company: 1,
				profitcentre: 1,
				_id: 0,
			}).lean();

			if (!projects || projects.length === 0) {
				return { filename: "no_data.xlsx", data: Buffer.from("") }; // Return empty if no data
			}

			// Define the structure for Excel row data
			type ExcelRowData = {
				projectId: string;
				projectName: string;
				customerName: string;
				city: string;
				industry: string;
				serviceOffering: string;
				plannedRevenue: number | string;
				projectDirector: string;
				projectManager: string;
				startDate: string;
				endDate: string;
				company: string;
				profitCentre: string;
			};

			// Define Excel headers
			const headers: (keyof ExcelRowData)[] = [
				"projectId",
				"projectName",
				"customerName",
				"city",
				"industry",
				"serviceOffering",
				"plannedRevenue",
				"projectDirector",
				"projectManager",
				"startDate",
				"endDate",
				"company",
				"profitCentre",
			];

			// Map projects to Excel rows
			const excelDataRows: ExcelRowData[] = projects.map((project) => {
				return {
					projectId: project.projectid || "",
					projectName: project.projectname || "",
					customerName: project.customername || "",
					city: project.location || "",
					industry: project.primarymarket || "",
					serviceOffering: project.servicename || "",
					plannedRevenue: project.estconsfees || "",
					projectDirector: project.projectdirector || "",
					projectManager: project.projectmanager || "",
					startDate: project.startdate
						? String(project.startdate).split("T")[0]
						: "",
					endDate: project.enddate ? String(project.enddate).split("T")[0] : "",
					company: project.company || "",
					profitCentre: project.profitcentre || "",
				};
			});

			// Generate XLSX
			// Create worksheet with headers and data
			const worksheetData: string[][] = [
				headers as string[], // Cast headers to string[]
				...excelDataRows.map((row) =>
					headers.map((header) => String(row[header] || "")),
				),
			];

			// Create worksheet
			const ws = XLSX.utils.aoa_to_sheet(worksheetData);

			// Create workbook and add worksheet
			const wb = XLSX.utils.book_new();
			XLSX.utils.book_append_sheet(wb, ws, "Filtered Projects");

			// Generate XLSX buffer
			const xlsxBuffer = XLSX.write(wb, {
				type: "buffer",
				bookType: "xlsx",
			});

			const filename = `filtered_projects_${new Date().toISOString().split("T")[0]}.xlsx`;

			return { filename, data: xlsxBuffer };
		} catch (error) {
			logger.error("Error generating Excel file for download:", error);
			throw new Error("Failed to generate download file.");
		}
	};

	return {
		getProjects,
		getProjectById,
		getAvailableProjectFilters,
		searchItemFilter,
		getProjectsByFilters,
		getPercentageByMarketShare,
		downloadFilteredProjects,
	};
};

export type MongoProjectService = ProjectRepository;
