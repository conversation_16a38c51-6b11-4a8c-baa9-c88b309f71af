import type { Request, Response, NextFunction } from "express";
import type { ElasticSearchFolderService } from "./elasticSearchFolder.service";

export class ElasticSearchFolderController {
	constructor(private elasticSearchFolderService: ElasticSearchFolderService) {}

	/**
	 * Search documents by sub-text in file_path (across all indexes)
	 */
	public searchByEsIndexAndFilePath = async (
		req: Request,
		res: Response,
		next: NextFunction,
	): Promise<void> => {
		try {
			const { search_text, limit = 20, offset = 0 } = req.body;

			if (!search_text || typeof search_text !== "string") {
				res.status(400).json({
					error: "search_text is required and must be a string",
				});
				return;
			}

			const limitNum =
				typeof limit === "string" ? Number.parseInt(limit, 10) : Number(limit);
			const offsetNum =
				typeof offset === "string"
					? Number.parseInt(offset, 10)
					: Number(offset);

			if (Number.isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
				res.status(400).json({
					error: "limit must be a number between 1 and 100",
				});
				return;
			}

			if (Number.isNaN(offsetNum) || offsetNum < 0) {
				res.status(400).json({
					error: "offset must be a non-negative number",
				});
				return;
			}

			const results = await this.elasticSearchFolderService.searchByFilePath(
				search_text,
				limitNum,
				offsetNum,
			);

			res.status(200).json({
				success: true,
				data: results,
				pagination: {
					limit: limitNum,
					offset: offsetNum,
					count: results.length,
				},
			});
		} catch (error) {
			next(error);
		}
	};
}
