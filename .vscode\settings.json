{"eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "eslint.workingDirectories": [{"mode": "auto"}], "eslint.run": "onType", "editor.codeActionsOnSave": {"source.fixAll.eslint": "always"}, "editor.defaultFormatter": "rvest.vs-code-prettier-eslint", "editor.formatOnType": false, "editor.formatOnPaste": true, "editor.formatOnSave": true, "editor.formatOnSaveMode": "file", "files.autoSave": "onFocusChange", "vs-code-prettier-eslint.prettierLast": false, "typescript.tsdk": "node_modules\\typescript\\lib", "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[jsonc]": {"editor.defaultFormatter": "biomejs.biome"}}