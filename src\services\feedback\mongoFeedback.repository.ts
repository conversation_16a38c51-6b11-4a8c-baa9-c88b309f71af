import { Feedback, FeedbackModel } from './feedback.model';
import { FeedbackRepository } from './feedback.repository';
import { Types } from 'mongoose';
import { HttpException } from '@/utils/exceptions/http.exception';
import { FeedbackCreateInput } from './feedback.types';

export class MongoFeedbackRepository implements FeedbackRepository {
  async create(feedback: FeedbackCreateInput): Promise<Feedback> {
    const createdFeedback = new FeedbackModel(feedback);
    await createdFeedback.save();
    return createdFeedback.toObject();
  }

  async findAll(): Promise<Feedback[]> {
    const feedback = await FeedbackModel.find().sort({ createdAt: -1 }).lean();
    return feedback;
  }

  async findById(id: string): Promise<Feedback | null> {
    if (!Types.ObjectId.isValid(id)) {
      throw new HttpException(400, 'Invalid feedback ID');
    }
    const feedback = await FeedbackModel.findById(id).lean();
    return feedback;
  }

  async findByAzureAdObjectId(azureAdObjectId: string): Promise<Feedback[]> {
    const feedback = await FeedbackModel.find({ azureAdObjectId }).sort({ createdAt: -1 }).lean();
    return feedback;
  }
}
