import mapRoutes from "@/routes/maps";
import accumulatedDurationRefactoredRoutes from "@/services/duration/refactored/accumulatedDuration.routes";
import feedbackRoutes from "@/services/feedback/feedback.routes";
import fileRoutes from "@/services/file/file.routes";
import { Router } from "express";
import howToRefactoredRoutes from "./howTo/refactor/index";
import methodologyRoutes from "./methodology/index";
import productivityRoutes from "./productivity/index";
import projectRoutes from "./project/index";
import searchRoutes from "./search/index";
import userRoutes from "./users/index";

type RouteDef = {
	prefix: string;
	router: Router;
};

const einsteinRoutes: RouteDef[] = [
	{
		prefix: "/how-to",
		router: howToRefactoredRoutes,
	},
	{
		prefix: "/projects",
		router: projectRoutes,
	},
	{
		prefix: "/users",
		router: userRoutes,
	},
	{
		prefix: "/map",
		router: mapRoutes,
	},
	{
		prefix: "/methodologies",
		router: methodologyRoutes,
	},
	{
		prefix: "/search",
		router: searchRoutes,
	},
	{
		prefix: "/feedback",
		router: feedbackRoutes,
	},
	{
		prefix: "/files",
		router: fileRoutes,
	},
	{
		prefix: "/productivity",
		router: productivityRoutes,
	},
];

import abacusProjectRoutes from "./abacus/projects";

const abacusRoutes: RouteDef[] = [
	{
		prefix: "/users",
		router: userRoutes,
	},
	{
		prefix: "/projects",
		router: abacusProjectRoutes,
	},
	{
		prefix: "/accumulated-duration-refactored",
		router: accumulatedDurationRefactoredRoutes,
	},
];

function buildRouter(routes: RouteDef[]) {
	const router = Router();

	for (const route of routes) {
		router.use(route.prefix, route.router);
	}
	return router;
}

const einsteinRouter = buildRouter(einsteinRoutes);
const abacusRouter = buildRouter(abacusRoutes);

export { einsteinRouter, abacusRouter };
