export const COLOR_PALETTE = [
	"#00AEEF",
	"#567E92",
	"#92648E",
	"#E8C5AA",
	"#82A25A",
	"#5951A2",
	"#4072B8",
	"#69C6B1",
	"#D8D8DA",
	"#1D79AA",
	"#7972B5",
	"#1C3E94",
	"#FFCB1B",
	"#75C15D",
	"#5299D3",
];

export const getRandomColors = (count: number): string[] => {
	const colors = [...COLOR_PALETTE];
	const result: string[] = [];

	// If we need more colors than available, we'll start reusing colors
	while (result.length < count) {
		if (colors.length === 0) {
			colors.push(...COLOR_PALETTE);
		}
		const randomIndex = Math.floor(Math.random() * colors.length);
		result.push(colors[randomIndex]);
		colors.splice(randomIndex, 1);
	}

	return result;
};
